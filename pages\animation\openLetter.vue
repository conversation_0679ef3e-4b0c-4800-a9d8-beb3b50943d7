<template>
	<view class="container">
		<!-- <view class="navbar">
			<u-navbar
				title="开门信"
				:autoBack="true"
				:placeholder="true"
			>
			</u-navbar>
		</view> -->
		
		<!-- 灰色蒙版和动画容器 -->
		<view class="overlay" v-if="showOverlay" @click.stop="handleOverlayClick">
			<view id="animation-container" ref="animationContainer" 
				class="animation-container"
				@click.stop="handleContainerClick">
			</view>
			
			<!-- 精美的关闭按钮 -->
			<view v-if="showCloseBtn" class="close-btn" @click="closeOverlay">
				<view class="close-btn-inner">
					<u-icon name="close" color="#FFB74D" size="30"></u-icon>
					<text>关闭信封</text>
				</view>
			</view>
		</view>
		
		<!-- 红包封面图片 -->
		<view class="hongbao-container" @click="showAnimation" v-if="!showOverlay">
			<image src="/static/hongbao.png" mode="aspectFit" class="hongbao-image"></image>
			<view class="hongbao-text">
				<text>点击打开</text>
				<view class="hongbao-pulse"></view>
			</view>
		</view>
		
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import { Player } from '@galacean/effects';

export default {
	data() {
		return {
			loading: false,
			player: null,
			showOverlay: false,
			animationFinished: false,
			showCloseBtn: false,
			closeTimer: null
		}
	},
	methods: {
		// 显示动画和蒙版
		showAnimation() {
			this.showOverlay = true;
			this.animationFinished = false;
			this.showCloseBtn = false;
			
			// 延迟一点时间初始化动画，确保DOM已渲染
			setTimeout(() => {
				this.initAnimation();
			}, 300);
			
			// 设置2秒后显示关闭按钮
			this.closeTimer = setTimeout(() => {
				this.showCloseBtn = true;
			}, 2000);
		},
		
		// 关闭蒙版
		closeOverlay() {
			this.showOverlay = false;
			this.showCloseBtn = false;
			
			// 清理计时器
			if (this.closeTimer) {
				clearTimeout(this.closeTimer);
				this.closeTimer = null;
			}
			
			// 清理资源
			if (this.player) {
				this.player.dispose();
				this.player = null;
			}
			
			// 返回上一页
			setTimeout(() => {
				uni.navigateBack();
			}, 200);
		},
		
		// 点击蒙版区域
		handleOverlayClick() {
			// 如果已显示关闭按钮，点击蒙版任意区域可关闭
			if (this.showCloseBtn) {
				this.closeOverlay();
			}
		},
		
		// 点击动画容器
		handleContainerClick(event) {
			// 动画内部点击逻辑
			console.log('动画容器被点击');
		},
		
		// 初始化动画
		initAnimation() {
			// 使用 uni 选择器获取元素
			const query = uni.createSelectorQuery().in(this);
			query.select('#animation-container').boundingClientRect(data => {
				if (!data) {
					console.error('未找到容器元素');
					return;
				}
				
				// 确保容器有足够尺寸
				if (data.width === 0 || data.height === 0) {
					console.error('容器尺寸为0');
					return;
				}
				
				// 获取元素引用
				const container = this.$refs.animationContainer;
				if (!container) {
					console.error('未找到容器元素引用');
					return;
				}
				
				// 初始化播放器
				try {
					// #ifdef H5
					this.player = new Player({
						container: container.$el || container,
						width: data.width,
						height: data.height,
						transparent: true,
						interactive: true, // 启用内置交互功能
						renderOptions: {
							preserveDrawingBuffer: true,
						},
						onError: (err) => {
							console.error('动画加载错误:', err);
							// 错误处理
							this.$refs.uToast.show({
								title: '动画加载失败',
								type: 'error'
							});
						},
					});
					
					// 监听动画播放完成事件
					this.player.on('complete', () => {
						console.log('动画播放完成');
						this.animationFinished = true;
						// 确保关闭按钮显示
						this.showCloseBtn = true;
					});
					
					// 加载动画
					this.player.loadScene('../../static/hongbao/open.json', {
						autoplay: false // 先加载但不自动播放
					})
					.then(() => {
						console.log('动画加载成功');
						// 手动播放动画
						this.player.play();
					})
					.catch(err => {
						console.error('动画加载失败:', err);
						// 尝试使用绝对路径
						this.player.loadScene(uni.getStorageSync('baseUrl') + '/static/hongbao/open.json', {
							autoplay: false
						})
						.then(() => {
							console.log('使用绝对路径加载动画成功');
							this.player.play();
						})
						.catch(e => {
							console.error('所有尝试均失败:', e);
							this.$refs.uToast.show({
								title: '动画加载失败',
								type: 'error'
							});
							// 出错时也显示关闭按钮
							this.showCloseBtn = true;
						});
					});
					// #endif
					
					// #ifndef H5
					// 在非H5平台，使用图片降级
					this.$refs.uToast.show({
						title: '当前平台不支持动画播放',
						type: 'warning'
					});
					this.animationFinished = true;
					this.showCloseBtn = true;
					// #endif
				} catch (err) {
					console.error('初始化播放器失败:', err);
					this.$refs.uToast.show({
						title: '动画初始化失败',
						type: 'error'
					});
					// 出错时也显示关闭按钮
					this.showCloseBtn = true;
				}
			}).exec();
		}
	},
	onUnload() {
		// 清理资源
		if (this.closeTimer) {
			clearTimeout(this.closeTimer);
			this.closeTimer = null;
		}
		
		if (this.player) {
			this.player.dispose();
			this.player = null;
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100vh;
	position: relative;
	background-color: rgba(0, 0, 0, 0.7); // 与动画蒙版相同的背景色
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.navbar {
	background-color: transparent;
}

.overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.7); // 灰色蒙版
	z-index: 100;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.animation-container {
	width: 90%;
	height: 80vh;
	position: relative;
	background-color: transparent;
}

.close-btn {
	margin-top: 30rpx;
	animation: fadeIn 0.5s ease-in-out;
}

.close-btn-inner {
	padding: 20rpx 40rpx;
	background: linear-gradient(to right, #FFECB3, #FFA000);
	color: #7D3C00;
	border-radius: 40rpx;
	font-size: 28rpx;
	font-weight: bold;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	transform: translateY(0);
	transition: transform 0.2s ease;
	
	&:active {
		transform: translateY(4rpx);
	}
	
	text {
		margin-left: 10rpx;
	}
}

.hongbao-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	flex: 1;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.hongbao-image {
	width: 700rpx;
	height: 700rpx;
	animation: float 3s ease-in-out infinite;
	filter: drop-shadow(0 5rpx 15rpx rgba(255, 255, 255, 0.3));
}

.hongbao-text {
	margin-top: 40rpx;
	font-size: 32rpx;
	color: #FFD54F;
	font-weight: bold;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.5);
}

.hongbao-pulse {
	width: 20rpx;
	height: 20rpx;
	background-color: #FFD54F;
	border-radius: 50%;
	margin-top: 20rpx;
	animation: pulse 1.5s infinite;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(20rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes float {
	0% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-15rpx);
	}
	100% {
		transform: translateY(0);
	}
}

@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(255, 213, 79, 0.7);
	}
	70% {
		box-shadow: 0 0 0 20rpx rgba(255, 213, 79, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(255, 213, 79, 0);
	}
}
</style> 