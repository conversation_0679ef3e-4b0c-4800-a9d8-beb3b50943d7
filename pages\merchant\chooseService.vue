<template>
	<view>
		<z-paging 
			:show-scrollbar="false" 
			refresher-background='#f3f3f3' 
			ref="paging" 
			:use-page-scroll='true' 
			refresher-only
			@onRefresh="onRefresh"
			:auto-scroll-to-top="true"
			:refresher-threshold="80"
			:refresher-default-duration="300"
		>
			<view slot="top">
				<u-navbar
					:title="pageTitle"
					:autoBack="true"
					:placeholder="true"
				>
				</u-navbar>
			</view>
			
			<template #refresher="{refresherStatus}">
				<custom-refresher :status="refresherStatus" color="#000" />
			</template>
			
			<template #loadingMoreNoMore>
				<custom-nomore />
			</template>
			
			<view v-if="loading">
				<x-skeleton type="waterfall" :loading="true" :configs="{
					gridColumns: 1,
					headHeight: '200rpx',
					textRows: 2,
					gridRows: 8,
					textShow: false
				}">
					<view></view>
				</x-skeleton>
			</view>
			
			<view v-else>
				<view class="service-container">
					<view class="service-card" 
						v-for="(item, index) in serviceList" 
						:key="item.id"
						@click="goToServiceDetail(item)"
						hover-class="service-card-hover"
						:style="{ backgroundImage: item.bgImage ? `url(${item.bgImage})` : 'url(/static/image/servemer.png)' }"
					>
						<view class="service-content">
							<view class="service-title">{{item.offeringName || '勃学超市自习室'}}</view>
							<!-- <view class="service-period">创建时间：{{item.createTime || '-'}}</view> -->
						</view>
						<view class="service-indicator">
							<u-icon name="arrow-right" color="rgba(0,0,0,0.2)" size="28"></u-icon>
						</view>
					</view>
				</view>
				
<!-- 				<u-empty
					v-if="!loading && (!serviceList || serviceList.length === 0)"
					text="暂无服务信息"
					mode="list"
					icon="../../static/empty/list.png"
				>
				</u-empty> -->
			</view>
			
			<u-toast ref="uToast"></u-toast>
		</z-paging>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				serviceList: [], // 服务列表
				type: '', // 服务类型：attendance(考勤管理) 或 evaluate(评价管理)
			}
		},
		computed: {
			// 根据type计算页面标题
			pageTitle() {
				if (this.type === 'attendance') {
					return '选择考勤服务';
				} else if (this.type === 'evaluate') {
					return '选择评价服务';
				}
				return '我的服务';
			}
		},
		onLoad(options) {
			// 获取type参数，用于判断是考勤管理还是评价管理
			this.type = options.type || '';
			
			// 页面加载时获取服务列表
			this.getServiceList();
		},
		methods: {
			// 跳转到服务详情页
			goToServiceDetail(item) {
				// 根据type参数跳转到不同页面
				let url = '';
				if (this.type === 'attendance') {
					// 考勤管理
					url = `/pages/merchant/attendance?id=${item.id}&name=${encodeURIComponent(item.offeringName || '')}`;
				} else if (this.type === 'evaluate') {
					// 评价管理
					url = `/pages/merchant/evaluateList?id=${item.id}&name=${encodeURIComponent(item.offeringName || '')}`;
				} else {
					// 默认跳转原来的路径
					url = `/pages/myserve/serviceDetail?id=${item.id}`;
				}
				
				uni.navigateTo({ url });
			},
			
			// 获取服务列表
			getServiceList() {
				this.loading = true;
				this.merhttp.ajax({
					url: this.merhttp.api.simplelist,
					method: 'GET',
					success: (res) => {
						this.loading = false;
						if (res.code === 0) {
							this.serviceList = res.data || [];
							// 处理返回的数据
							this.serviceList.forEach(item => {
								// 使用backgroundMember作为背景图
								item.bgImage = item.backgroundMember;
								// 格式化创建时间
								item.createTime = item.createTime ? this.$u.timeFormat(new Date(item.createTime), 'yyyy/mm/dd') : '暂无日期';
							});
						} else {
							uni.showToast({
								title: res.msg || '获取服务列表失败',
								icon: 'none'
							});
						}
						
						// 完成下拉刷新
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					},
					fail: () => {
						this.loading = false;
						uni.showToast({
							title: '获取服务列表失败',
							icon: 'none'
						});
						
						// 完成下拉刷新
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					},
					complete: () => {
						// 确保在任何情况下都会执行complete
						this.loading = false;
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					}
				});
			},
			
			// 修复下拉刷新问题
			onRefresh() {
				this.getServiceList();
			}
		}
	}
</script>

<style>
	page, body {
		background-color: #f3f3f3;
	}
</style>

<style lang="scss" scoped>
	.service-container {
		width: 100%;
		padding: 30rpx;
	}
	
	.service-card {
		margin-bottom: 30rpx;
		border-radius: 16rpx;
		background-image: url('/static/image/servemer.png');
		background-size: 100% 100%;
		width: 100%;
		height: 250rpx;
		padding: 30rpx;
		padding-left: 240rpx;
		box-sizing: border-box;
		color: #000;
		position: relative;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		display: flex;
		justify-content: space-between;
		align-items: center;
		transition: all 0.2s;
	}
	
	.service-card-hover {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.service-content {
		flex: 1;
	}
	
	.service-title {
		font-size: 32rpx;
		font-weight: bold;
		// margin-bottom: 20rpx;
	}
	
	.service-period {
		font-size: 26rpx;
		opacity: 0.9;
	}
	
	.service-indicator {
		margin-left: 10upx;
		width: 40rpx;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		opacity: 0.6;
	}
</style>