<template>
  <view class="tip-container" v-if="show && shouldShow">
	  
    <view class="tip-content">
		<!-- <view style="padding-right: 30upx;border-right:2upx solid #fff ;">
				  <u-icon name="close" color="#fff"  @click="close"></u-icon>
		</view> -->
		<view class="dis-ali" style="" @click="addChild">
			<text style="width: 100%;font-size: 30upx;">{{text}}</text>
			  <u-icon name="arrow-right" color="#fff"></u-icon>
		</view>
    
    </view>
  </view>
</template>

<script>
export default {
  name: 'no-child-tip',
  props: {
    text: {
      type: String,
      default: '当前信息待完善，为了享受更精准的服务请前往设置'
    }
  },
  data() {
    return {
      show: false,
      hasChild: false,
      loginStatus: false
    }
  },
  computed: {
    shouldShow() {
      return this.loginStatus && !this.hasChild
    }
  },
  created() {
    this.loginStatus = getApp().globalData.login
    if(this.loginStatus) {
      this.checkChild()
    }
    
    // 监听登录状态变化
    uni.$on('loginStatusChange', (status) => {
      this.loginStatus = status
      if(status) {
        this.checkChild()
      }
    })
    
    // 监听页面返回事件
    uni.$on('updateChildInfo', () => {
      if(this.loginStatus) {
        this.checkChild()
      }
    })
  },
  onShow() {
    // 每次页面显示时重新检查
    if(this.loginStatus) {
      this.checkChild()
    }
  },
  beforeDestroy() {
    uni.$off('loginStatusChange')
    uni.$off('updateChildInfo')
  },
  methods: {
    close() {
      this.show = false
    },
    addChild() {
      uni.navigateTo({
        url:'/pages/children/children'
      })
    },
    async checkChild() {
      try {
        const res = await this.http.ajax({
          url: this.http.api.getChild,
          method: 'GET'
        })
        this.hasChild = !!(res.code === 0 && res.data.length > 0)
        this.show = true
		// console.log( this.hasChild,this.show )
      } catch(err) {
        console.error('获取孩子信息失败:', err)
        this.hasChild = false
        this.show = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tip-container {
  position: fixed;
  
  bottom: var(--window-bottom); // tabbar上方
  left: 0;
  right: 0;
  z-index:10;
  padding: 0 30rpx;
  
  .tip-content {
    background: rgba(0,0,0,0.7);
    border-radius: 8rpx;
    padding: 30rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    text {
      color: #fff;
      font-size: 26rpx;
    }
    
    .close-icon {
      padding-left: 20rpx;
      font-size: 32rpx;
    }
  }
}
</style> 