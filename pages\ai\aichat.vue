<template>
	<view class="ai-chat-container">
		<u-navbar
			title="智能客服"
			:autoBack="true"
			:placeholder="true"
		>
			<view slot="right" class="navbar-right">
				<view class="icon-wrapper" @click="toggleCall">
					<u-icon name="phone" color="#333" size="24"></u-icon>
				</view>
				<view class="icon-wrapper" @click="toggleMute">
					<u-icon :name="isMuted ? 'volume-off' : 'volume'" color="#333" size="24"></u-icon>
				</view>
			</view>
		</u-navbar>
		
		<!-- 使用z-paging组件替代scroll-view -->
		<z-paging 
			ref="paging" 
			class="chat-content"
			:show-scrollbar="false" 
			refresher-background='#f6f6f6' 
			:use-page-scroll="false"
			@query="loadMessages"
			:show-loading-more-no-more-view="false"
			:hide-empty-view="true"
			style="padding-top: 100upx;"
		>
			<template #top>
				<!-- 顶部占位，如有需要 -->
			</template>
			
			<template #refresher="{refresherStatus}">
				<custom-refresher :status="refresherStatus" color="#000" />
			</template>

			
			<view v-if="loading" class="loading-container">
				<!-- 简单加载提示，移除u-loading组件 -->
				<view class="simple-loading">
					<text>加载中...</text>
				</view>
			</view>
			
			<view v-else class="message-list">
				<view class="time-divider">{{currentDate}}</view>
				
				<!-- 合并所有消息并按时间排序显示 -->
				<template v-for="(item, index) in sortedMessages">
					<!-- AI消息 -->
					<view class="message-item ai-message" v-if="!item.isUser" :key="'m'+index">
						<view class="avatar">
							<image src="/static/image/ai-avatar.png" mode="aspectFill"></image>
						</view>
						<view class="message-content">
							<view class="message-bubble" :class="{'system-message': item.isSystem}">
								<rich-text :nodes="item.content"></rich-text>
								<!-- 如果消息有音频，显示播放按钮 -->
								<view v-if="item.hasAudio" class="audio-player" @click="playAudio(item.audioUrl, 'msg-'+index)">
									<u-icon :name="item.isPlaying ? 'pause-circle' : 'play-right-fill'" color="#2BCBD4" size="24"></u-icon>
									<text>{{item.isPlaying ? '暂停语音' : '播放语音'}}</text>
								</view>
							</view>
							<view class="message-time">{{item.time}}</view>
						</view>
					</view>
					
					<!-- 用户消息 -->
					<view class="message-item user-message" v-else :key="'user-'+index">
						<view class="message-content">
							<view class="message-bubble" :class="{'voice-message': item.isVoice}">
								<view v-if="item.isVoice" class="voice-message-content" @click="playVoice(item)">
									<view class="voice-icon">
										<view class="voice-wave-icon">
											<view class="wave-line" v-for="(line, i) in 4" :key="i"></view>
										</view>
									</view>
									<text>{{item.duration}}″</text>
								</view>
								<rich-text v-else :nodes="item.content"></rich-text>
							</view>
							<view class="message-time">{{item.time}}</view>
						</view>
						<view class="avatar">
							<image :src="userAvatar || '/static/image/default-avatar.png'" mode="aspectFill"></image>
						</view>
					</view>
				</template>
				
				<!-- 底部空白区域，防止内容被输入框遮挡 -->
				<view class="bottom-safe-area"></view>
			</view>
		</z-paging>
		
		<!-- 底部输入区域 - 固定在底部 -->
		<view class="chat-footer">
			<view class="input-area">
				<!-- 语音/键盘切换按钮 -->
				<view class="voice-keyboard-toggle" @click="toggleInputMode">
					<image src="/static/image/keyboard-fill.png" mode="" style="width: 50upx;height: 50upx;" v-if="isVoiceMode"></image>
					<u-icon name="mic" color="#333" size="26" v-else></u-icon>
				</view>
				
				<!-- 文本输入框 - 在非语音模式下显示 -->
				<input 
					v-if="!isVoiceMode"
					class="message-input" 
					v-model="inputMessage" 
					placeholder="请输入消息..." 
					confirm-type="send"
					@confirm="sendMessage"
				/>
				
				<!-- 语音按钮 - 在语音模式下显示 -->
				<view 
					v-else
					class="voice-button-large" 
					@touchstart="startRecording" 
					@touchend="stopRecording"
					@touchcancel="cancelRecording"
				>
					<text>按住说话</text>
				</view>
				
				<view class="attachment-button" @click="showActionSheet = true">
					<u-icon name="plus-circle" color="#333" size="26"></u-icon>
				</view>
				
				<!-- 发送按钮或停止按钮 -->
				<view v-if="inputMessage.trim() && !isVoiceMode && !isResponding" class="send-button" @click="sendMessage">
					<text>发送</text>
				</view>
				<view v-else-if="isResponding" class="stop-button" @click="stopResponse">
					<u-icon name="close" color="#ff3636" size="26"></u-icon>
				</view>
			</view>
			
			<!-- AI响应状态指示器 -->
			<view v-if="isResponding" class="responding-indicator">
				<text>AI正在思考中{{typingIndicator}}</text>
			</view>
		</view>
		
		<!-- 功能选择面板 -->
		<u-action-sheet 
			:actions="actionItems" 
			:show="showActionSheet"
			@select="handleActionSelect"
			@close="showActionSheet = false"
			cancel-text="取消">
		</u-action-sheet>
		
		<!-- 语音录制动画提示 -->
		<view class="voice-recording-tip" v-if="isRecording">
			<view class="voice-wave">
				<!-- 动态波形效果 -->
				<view class="voice-wave-container">
					<view 
						v-for="(item, index) in voiceWaveArray" 
						:key="index" 
						class="voice-wave-bar"
						:style="{height: item + 'rpx'}"
					></view>
				</view>
			</view>
			<text>{{recordingTip}}</text>
		</view>
		
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
	import { StreamAudioPlayer } from "@/utils/StreamPlayer.js"
	
	export default {
		created() {
			
		},
		data() {
			return {
				loading: false,
				inputMessage: '',
				messages: [],
				isRefreshing: false,
				showActionSheet: false,
				isMuted: false,
				isInCall: false,
				userAvatar: '',
				currentDate: this.formatDate(new Date()),
				isVoiceMode: false,
				isRecording: false,
				recordingTip: '松开发送，上滑取消',
				recorderManager: null,
				currentVoiceFile: '',
				recordingTimeout: null,
				recordDuration: 0,
				recordStartTime: 0,
				cancelRecordY: 0,
				voiceWaveArray: [10, 15, 20, 15, 10, 15, 20, 15, 10],
				animationTimer: null,
				websocket: null,
				wsUrl: 'wss://manage.boxuehao.cn/websocket/agent/',
				userId: '',
				botId: '1',
				isConnected: false,
				audioSources: [],
				currentTrackIndex: 0,
				isPlaying: false,
				MAX_CHUNK_SIZE: 1024,
				actionItems: [
					{
						name: '发送图片',
						icon: 'photo'
					},
					{
						name: '发送文件',
						icon: 'file-text'
					},
					{
						name: '我的材料',
						icon: 'folder'
					}
				],
				aiMessages: [],
				userMessages: [],
				currentAIMessage: null,
				isCurrentResponseComplete: true,
				audioBuffer: [],
				
				// WebSocket心跳相关
				heartbeatTimer: null,
				reconnectTimer: null,
				heartbeatInterval: 30000, // 30秒发送一次心跳
				reconnectInterval: 3000, // 3秒尝试重连一次
				maxReconnectAttempts: 5,
				currentReconnectAttempts: 0,
				reconnecting: false,
				
				// 流式音频播放相关
				audioChunks: [],
				audioContext: null,
				isStreamPlaying: false,
				lastAudioTimestamp: 0,
				streamAudioQueue: [], // 存储待播放的音频块队列
				streamAudioPlayer: null, // 使用StreamAudioPlayer
				
				// 音频播放控制相关
				currentAudioContext: null, // 当前播放的音频上下文
				currentPlayingUrl: '', // 当前播放的音频URL
				
				// 响应状态相关
				isResponding: false, // AI是否正在响应
				typingIndicator: '...', // 打字指示器文本
				
				// 调试模式 - 音频处理诊断
				audioDiagnosticMode: true, // 音频处理诊断模式，记录详细日志
				audioProcessingStats: {   // 音频处理统计
					chunksReceived: 0,    // 收到的音频块数
					chunksSaved: 0,       // 成功保存的音频块数
					chunksPlayed: 0,      // 成功播放的音频块数
					completeSaved: false, // 是否成功保存完整音频
					completePlayed: false // 是否成功播放完整音频
				}
			}
		},
		computed: {
			// 所有消息按时间排序
			allMessages() {
				// 实际应用中需要合并aiMessages和userMessages并按时间排序
				return this.messages;
			},
			// 是否有更多消息可加载
			hasMoreMessages() {
				// 实际应用中根据API返回判断是否有更多历史消息
				return false;
			},
			// 完整的WebSocket URL
			fullWsUrl() {
				if (!this.userId) {
					// 如果没有用户ID，则从缓存中获取
					const userInfo = uni.getStorageSync('userInfo');
					if (userInfo && userInfo.id) {
						this.userId = userInfo.id;
					} else {
						// 默认使用示例ID
						this.userId = '291';
					}
				}
				return `${this.wsUrl}${this.userId}/${this.botId}`;
			},
			// 所有消息合并并按时间排序
			sortedMessages() {
				// 合并AI消息和用户消息
				const allMessages = [...this.aiMessages, ...this.userMessages];
				
				// 为消息添加时间戳，用于排序
				allMessages.forEach(msg => {
					if (!msg.timestamp) {
						const [hours, minutes] = msg.time.split(':');
						const today = new Date();
						today.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);
						msg.timestamp = today.getTime();
					}
				});
				
				// 按时间戳排序
				return allMessages.sort((a, b) => a.timestamp - b.timestamp);
			}
		},
		onLoad() {
			// 初始化数据
			this.initData();
			
			// 获取用户信息
			this.getUserInfo();
			
			// 初始化录音管理器
			this.initRecorderManager();
			
			// 初始化WebSocket连接
			this.initWebSocket();
			
			// 启动打字指示器动画
			this.startTypingAnimation();
			
			// 初始化StreamAudioPlayer
			this.initStreamAudioPlayer();
		},
		onUnload() {
			// 页面卸载时关闭WebSocket连接
			this.closeWebSocket();
			
			// 清理录音相关资源
			if (this.animationTimer) {
				clearInterval(this.animationTimer);
				this.animationTimer = null;
			}
			
			// 停止录音
			if (this.isRecording && this.recorderManager) {
				try {
					this.recorderManager.stop();
				} catch (e) {
					console.error('停止录音失败', e);
				}
			}
			
			// 停止所有正在播放的音频
			this.stopAllAudio();
			
			// 清理音频URL资源
			this.clearAudioResources();
			
			// 释放StreamAudioPlayer资源
			this.streamAudioPlayer = null;
			
			// 重置所有状态
			this.resetAllStates();
			
			console.log('页面已卸载，所有资源已清理');
		},
		onHide() {
			// 暂停所有音频播放
			this.stopAllAudio();
			
			console.log('页面已隐藏，暂停音频播放');
		},
		methods: {
			// 初始化WebSocket连接
			initWebSocket() {
				try {
					// 关闭已有连接
					this.closeWebSocket();
					
					// 重置消息和音频数据（仅在初次连接或用户手动刷新时）
					if (!this.reconnecting) {
						this.clearMessages();
					}
					
					// 使用uni.connectSocket替代原生WebSocket
					uni.connectSocket({
						url: this.fullWsUrl,
						success: () => {
							console.log('WebSocket连接请求已发送');
						},
						fail: (err) => {
							console.error('WebSocket连接请求失败', err);
							// 尝试重连
							this.reconnect();
							
							if (!this.reconnecting) {
								uni.showToast({
									title: 'WebSocket连接失败',
									icon: 'none'
								});
							}
						}
					});
					
					// 监听WebSocket连接打开事件
					uni.onSocketOpen((res) => {
						console.log('WebSocket连接已打开');
						this.isConnected = true;
						this.currentReconnectAttempts = 0;
						this.reconnecting = false;
						
						// 清除重连定时器
						if (this.reconnectTimer) {
							clearTimeout(this.reconnectTimer);
							this.reconnectTimer = null;
						}
						
						// 开始心跳
						this.startHeartbeat();
						
						// 只在初次连接时显示系统消息，重连时不显示
						if (!this.reconnecting) {
							// 添加系统消息
							this.addSystemMessage('连接成功，您可以开始聊天了');
						}
					});
					
					// 监听WebSocket关闭事件
					uni.onSocketClose((res) => {
						console.log('WebSocket连接已断开', res);
						this.isConnected = false;
						
						// 停止心跳
						this.stopHeartbeat();
						
						// 尝试重连
						this.reconnect();
					});
					
					// 监听WebSocket错误事件
					uni.onSocketError((res) => {
						console.error('WebSocket错误', res);
						this.isConnected = false;
						
						// 只在非重连状态下显示错误消息
						if (!this.reconnecting) {
							// 添加系统消息
							// this.addSystemMessage('连接发生错误'); // 不向用户显示错误消息
						}
					});
					
					// 监听WebSocket接收到服务器的消息事件
					uni.onSocketMessage((res) => {
						this.handleWebSocketMessage(res);
					});
				} catch (e) {
					console.error('初始化WebSocket失败', e);
					
					// 尝试重连
					this.reconnect();
					
					if (!this.reconnecting) {
						uni.showToast({
							title: 'WebSocket连接失败',
							icon: 'none'
						});
					}
				}
			},
			
			// 开始心跳
			startHeartbeat() {
				// 清除已有的心跳定时器
				this.stopHeartbeat();
				
				// 创建新的心跳定时器
				this.heartbeatTimer = setInterval(() => {
					if (this.isConnected) {
						try {
							// 使用字符串转二进制函数将心跳包转换为二进制数据
							const heartbeat = this.stringToUint8Array('[HEARTBEAT]');
							
							// 发送心跳包，使用一个特殊的字符串标识心跳
							uni.sendSocketMessage({
								data: heartbeat.buffer,
								fail: (err) => {
									console.error('发送心跳包失败', err);
									// 心跳失败，可能连接已断开，尝试重连
									this.reconnect();
								}
							});
							console.log('心跳包已发送');
						} catch (e) {
							console.error('发送心跳包失败', e);
							// 心跳失败，可能连接已断开，尝试重连
							this.reconnect();
						}
					} else {
						// WebSocket不在开启状态，尝试重连
						this.reconnect();
					}
				}, this.heartbeatInterval);
			},
			
			// 停止心跳
			stopHeartbeat() {
				if (this.heartbeatTimer) {
					clearInterval(this.heartbeatTimer);
					this.heartbeatTimer = null;
				}
			},
			
			// 重连逻辑
			reconnect() {
				// 如果已经在尝试重连，则不重复操作
				if (this.reconnecting) return;
				
				// 如果达到最大重连次数，则停止重连
				if (this.currentReconnectAttempts >= this.maxReconnectAttempts) {
					console.log('达到最大重连次数，停止重连');
					this.reconnecting = false;
					this.currentReconnectAttempts = 0;
					
					// 显示消息（可选，视需求而定）
					// uni.showToast({
					//     title: '网络连接不稳定，请稍后再试',
					//     icon: 'none'
					// });
					return;
				}
				
				// 标记为正在重连
				this.reconnecting = true;
				this.currentReconnectAttempts++;
				
				console.log(`尝试第 ${this.currentReconnectAttempts} 次重连`);
				
				// 清除旧的重连定时器
				if (this.reconnectTimer) {
					clearTimeout(this.reconnectTimer);
				}
				
				// 设置新的重连定时器
				this.reconnectTimer = setTimeout(() => {
					// 尝试初始化WebSocket
					this.initWebSocket();
				}, this.reconnectInterval);
			},
			
			// 关闭WebSocket连接
			closeWebSocket() {
				// 停止心跳
				this.stopHeartbeat();
				
				// 停止重连
				if (this.reconnectTimer) {
					clearTimeout(this.reconnectTimer);
					this.reconnectTimer = null;
				}
				
				if (this.isConnected) {
					try {
						uni.closeSocket({
							success: (res) => {
								console.log('WebSocket连接已关闭');
							},
							fail: (err) => {
								console.error('关闭WebSocket失败', err);
							}
						});
					} catch (e) {
						console.error('关闭WebSocket失败', e);
					}
					this.isConnected = false;
				}
			},
			
			// 处理WebSocket消息
			handleWebSocketMessage(event) {
				// 获取消息数据，uni.onSocketMessage中的数据结构与原生WebSocket不同
				const data = event.data;
				
				if (typeof data === 'string') {
					// 处理文本消息
					console.log('收到WebSocket文本消息', data);
					
					// 忽略心跳响应
					if (data === '[HEARTBEAT_ACK]' || data === '[HEARTBEAT]') {
						console.log('收到心跳响应');
						return;
					}
					
					// 检查是否是新会话或当前会话已结束
					const isNewResponse = this.isCurrentResponseComplete || !this.currentAIMessage;
					
					if (isNewResponse) {
						// 创建新的AI消息
						this.currentAIMessage = {
							content: data,
							time: this.formatTime(new Date()),
							timestamp: Date.now(),
							isUser: false,
							isComplete: false
						};
						// 添加到AI消息列表
						this.aiMessages.push(this.currentAIMessage);
						this.isCurrentResponseComplete = false;
						
						// 新会话开始时，重置音频相关状态
						this.resetStreamAudioState();
					} else {
						// 追加到当前消息
						this.currentAIMessage.content += data;
						// 更新时间戳
						this.currentAIMessage.timestamp = Date.now();
					}
					
					// 滚动到底部
					this.scrollToBottom();
				} else {
					// 处理二进制消息（音频）
					console.log('收到WebSocket二进制消息', data.byteLength);
					
					// 二进制数据在uni-app中的处理方式会不同
					// 检查是否是结束标记 (2字节大小的数据)
					if (data.byteLength === 2) {
						const uint8Array = new Uint8Array(data);
						
						// 检查是否是结束标记 [255, 255]
						if (uint8Array[0] === 255 && uint8Array[1] === 255) {
							console.log('收到结束标记');
							
							// 处理完成的音频
							if (this.audioBuffer && this.audioBuffer.length > 0) {
								this.processCompleteAudio();
							}
							
							// 处理流式播放结束
							this.finalizeStreamAudio();
							
							// 标记文本响应也结束
							if (this.currentAIMessage) {
								this.isCurrentResponseComplete = true;
								this.currentAIMessage.isComplete = true;
							}
							
							// 标记响应结束
							this.isResponding = false;
						} else {
							// 不是结束标记，将其添加到缓冲区
							if (!this.audioBuffer) {
								this.audioBuffer = [];
							}
							this.audioBuffer.push(uint8Array);
							
							// 将音频块加入流式播放
							this.processAudioChunk(uint8Array);
						}
					} else {
						// 普通音频数据
						const uint8Array = new Uint8Array(data);
						
						// 将数据添加到完整音频缓冲区
						if (!this.audioBuffer) {
							this.audioBuffer = [];
						}
						this.audioBuffer.push(uint8Array);
						
						// 添加到流式播放队列并播放
						this.processAudioChunk(uint8Array);
					}
				}
			},
			
			// 重置流式音频状态
			resetStreamAudioState() {
				// 停止之前可能存在的流式播放
				this.isStreamPlaying = false;
				this.streamAudioQueue = [];
				this.audioChunks = [];
				this.lastAudioTimestamp = 0;
				
				// 重新创建StreamAudioPlayer实例
				if (!this.streamAudioPlayer) {
					this.initStreamAudioPlayer();
				}
			},
			
			// 处理单个音频块用于流式播放
			processAudioChunk(uint8Array) {
				// 创建音频块的拷贝并添加到队列
				const chunk = new Uint8Array(uint8Array);
				this.audioChunks.push(chunk);
				
				// 更新诊断统计
				if (this.audioDiagnosticMode) {
					this.audioProcessingStats.chunksReceived++;
					console.log(`[音频诊断] 收到音频块 #${this.audioProcessingStats.chunksReceived}, 大小: ${chunk.length} 字节`);
				}
				
				// 使用StreamAudioPlayer处理音频数据
				if (this.streamAudioPlayer) {
					try {
						// 将数据添加到StreamAudioPlayer
						this.streamAudioPlayer.appendChunk(chunk.buffer);
						
						if (this.audioDiagnosticMode) {
							console.log(`[音频诊断] 音频块已添加到StreamAudioPlayer`);
						}
					} catch (e) {
						console.error('添加音频块到StreamAudioPlayer失败', e);
					}
					return;
				}
				
				// 如果StreamAudioPlayer不可用，使用原始处理方法
				// #ifdef H5
				try {
					// H5环境使用Blob和URL.createObjectURL
					const audioBlob = new Blob([chunk], { type: 'audio/mp3' });
					const audioUrl = URL.createObjectURL(audioBlob);
					
					// 更新诊断统计
					if (this.audioDiagnosticMode) {
						console.log(`[音频诊断] H5环境创建音频URL: ${audioUrl}`);
					}
					
					// 将新的音频块加入播放队列
					this.streamAudioQueue.push(audioUrl);
					
					// 如果当前没有正在播放，开始播放
					if (!this.isStreamPlaying) {
						this.playNextAudioChunk();
					}
				} catch (e) {
					console.error('H5环境处理音频块失败', e);
				}
				// #endif
				
				// #ifdef APP-PLUS
				try {
					// 将音频数据转换为base64
					const base64Audio = uni.arrayBufferToBase64(chunk.buffer);
					
					// 更新诊断统计
					if (this.audioDiagnosticMode) {
						console.log(`[音频诊断] APP环境 - 音频块转为base64成功, 长度: ${base64Audio.length}`);
					}
					
					// 使用plus.io写入文件
					this.saveAudioChunkToFile(base64Audio).then(tempFilePath => {
						if (tempFilePath) {
							// 更新诊断统计
							if (this.audioDiagnosticMode) {
								this.audioProcessingStats.chunksSaved++;
								console.log(`[音频诊断] APP环境 - 音频块保存成功, 路径: ${tempFilePath}, 已保存 ${this.audioProcessingStats.chunksSaved}/${this.audioProcessingStats.chunksReceived}`);
							}
							
							// 将新的音频块加入播放队列
							this.streamAudioQueue.push(tempFilePath);
							
							// 如果当前没有正在播放，开始播放
							if (!this.isStreamPlaying) {
								this.playNextAudioChunk();
							}
						} else {
							console.error('音频块保存失败，未能获取有效的文件路径');
							if (this.audioDiagnosticMode) {
								console.log(`[音频诊断] APP环境 - ⚠️ 音频块保存失败, 已保存 ${this.audioProcessingStats.chunksSaved}/${this.audioProcessingStats.chunksReceived}`);
							}
						}
					});
				} catch (e) {
					console.error('APP环境处理音频块失败', e);
					if (this.audioDiagnosticMode) {
						console.log(`[音频诊断] APP环境 - ❌ 音频块处理异常: ${e.message}`);
					}
				}
				// #endif
				
				// #ifdef MP
				// 小程序环境
				try {
					const fs = wx.getFileSystemManager();
					const tempFilePath = `${wx.env.USER_DATA_PATH}/audio_chunk_${Date.now()}_${Math.floor(Math.random() * 1000)}.mp3`;
					
					fs.writeFile({
						filePath: tempFilePath,
						data: uint8Array.buffer,
						success: () => {
							this.streamAudioQueue.push(tempFilePath);
							if (!this.isStreamPlaying) {
								this.playNextAudioChunk();
							}
						},
						fail: (err) => {
							console.error('小程序环境写入音频文件失败', err);
						}
					});
				} catch (e) {
					console.error('小程序环境处理音频块失败', e);
				}
				// #endif
			},
			
			// 为APP环境保存音频块到文件系统
			saveAudioChunkToFile(base64Data) {
				return new Promise((resolve, reject) => {
					// #ifdef APP-PLUS
					try {
						const tempFileName = `audio_chunk_${Date.now()}_${Math.floor(Math.random() * 1000)}.mp3`;
						const tempDir = '_doc/temp_audio/';
						const filePath = `${tempDir}${tempFileName}`;
						
						// 确保目录存在
						plus.io.requestFileSystem(plus.io.PRIVATE_DOC, (fs) => {
							fs.root.getDirectory('temp_audio', {
								create: true
							}, (dirEntry) => {
								// 将base64转为二进制数组
								const byteCharacters = atob(base64Data);
								const byteNumbers = new Array(byteCharacters.length);
								for (let i = 0; i < byteCharacters.length; i++) {
									byteNumbers[i] = byteCharacters.charCodeAt(i);
								}
								const byteArray = new Uint8Array(byteNumbers);
								
								// 使用uni.saveFile直接保存文件
								const tempFilePath = `_doc/temp_${Date.now()}.temp`;
								plus.io.resolveLocalFileSystemURL(tempFilePath, (entry) => {
									entry.remove(() => {
										console.log('清理临时文件成功');
									}, (e) => {
										console.log('清理临时文件失败或文件不存在');
									});
								}, (e) => {
									console.log('临时文件不存在');
								});
								
								uni.getFileSystemManager().writeFile({
									filePath: tempFilePath,
									data: byteArray.buffer,
									success: () => {
										uni.saveFile({
											tempFilePath: tempFilePath,
											success: (res) => {
												const savedFilePath = res.savedFilePath;
												console.log('音频块文件保存成功:', savedFilePath);
												resolve(savedFilePath);
											},
											fail: (err) => {
												console.error('音频块文件保存失败:', err);
												
												// 尝试备用方法
												this.saveAudioFileAlternative(base64Data, filePath).then(path => {
													resolve(path);
												});
											}
										});
									},
									fail: (err) => {
										console.error('写入临时文件失败:', err);
										
										// 尝试备用方法
										this.saveAudioFileAlternative(base64Data, filePath).then(path => {
											resolve(path);
										});
									}
								});
							}, (error) => {
								console.error('创建临时音频目录失败:', error);
								resolve(null);
							});
						}, (error) => {
							console.error('获取文件系统失败:', error);
							resolve(null);
						});
					} catch (e) {
						console.error('保存音频块文件过程出错:', e);
						
						// 生成一个新的文件路径（因为在catch中filePath可能不在作用域内）
						const emergencyFilePath = `_doc/temp_audio/emergency_audio_${Date.now()}.mp3`;
						
						// 尝试备用方法
						this.saveAudioFileAlternative(base64Data, emergencyFilePath).then(path => {
							resolve(path);
						}).catch(() => {
							resolve(null);
						});
					}
					// #endif
					
					// 非APP环境
					// #ifndef APP-PLUS
					resolve(null);
					// #endif
				});
			},
			
			// 备用保存音频文件方法
			saveAudioFileAlternative(base64Data, filePath) {
				return new Promise((resolve) => {
					// #ifdef APP-PLUS
					try {
						// 使用plus.io的另一种方式保存文件
						const fullPath = plus.io.convertLocalFileSystemURL(filePath);
						
						// 将base64转为二进制数组
						const byteCharacters = atob(base64Data);
						const byteArray = new Uint8Array(byteCharacters.length);
						for (let i = 0; i < byteCharacters.length; i++) {
							byteArray[i] = byteCharacters.charCodeAt(i);
						}
						
						// 使用原生的方式写入文件
						plus.io.resolveLocalFileSystemURL('_doc/', (entry) => {
							entry.getDirectory('temp_audio', { create: true }, (dirEntry) => {
								const fileName = `audio_${Date.now()}.mp3`;
								dirEntry.getFile(fileName, { create: true }, (fileEntry) => {
									fileEntry.createWriter((writer) => {
										writer.onwrite = () => {
											console.log('备用方法音频文件写入成功:', fileEntry.toLocalURL());
											resolve(fileEntry.toLocalURL());
										};
										writer.onerror = (e) => {
											console.error('备用方法音频文件写入失败:', e);
											resolve(null);
										};
										
										// 直接写入ArrayBuffer
										writer.write(byteArray.buffer);
									}, (error) => {
										console.error('创建文件写入器失败:', error);
										resolve(null);
									});
								}, (error) => {
									console.error('创建音频文件失败:', error);
									resolve(null);
								});
							}, (error) => {
								console.error('创建音频目录失败:', error);
								resolve(null);
							});
						}, (error) => {
							console.error('解析文件系统URL失败:', error);
							resolve(null);
						});
					} catch (e) {
						console.error('备用方法保存音频文件失败:', e);
						resolve(null);
					}
					// #endif
					
					// 非APP环境
					// #ifndef APP-PLUS
					resolve(null);
					// #endif
				});
			},
			
			// 播放下一个音频块
			playNextAudioChunk() {
				if (this.streamAudioQueue.length === 0) {
					this.isStreamPlaying = false;
					return;
				}
				
				this.isStreamPlaying = true;
				
				// 获取队列中的下一个音频文件路径或URL
				const audioSource = this.streamAudioQueue.shift();
				
				// 更新诊断统计
				if (this.audioDiagnosticMode) {
					console.log(`[音频诊断] 尝试播放音频块, 源: ${audioSource}`);
				}
				
				// 播放音频
				try {
					const audioCtx = uni.createInnerAudioContext();
					audioCtx.src = audioSource;
					audioCtx.autoplay = true;
					
					// 设置音量 - 确保响应静音状态
					audioCtx.volume = this.isMuted ? 0 : 1;
					
					// 判断是否为本地文件（APP环境）
					let isLocalFile = false;
					// #ifdef APP-PLUS
					if (audioSource && typeof audioSource === 'string' && audioSource.indexOf('_doc/') === 0) {
						isLocalFile = true;
						if (this.audioDiagnosticMode) {
							console.log(`[音频诊断] 播放本地临时音频块: ${audioSource}`);
						}
					}
					// #endif
					
					// 添加播放开始事件
					audioCtx.onPlay(() => {
						if (this.audioDiagnosticMode) {
							this.audioProcessingStats.chunksPlayed++;
							console.log(`[音频诊断] 音频块开始播放成功 ✅ 已播放 ${this.audioProcessingStats.chunksPlayed}/${this.audioProcessingStats.chunksReceived}`);
						}
					});
					
					// 播放结束后释放资源并继续播放下一块
					audioCtx.onEnded(() => {
						// #ifdef H5
						// H5环境释放URL对象
						try {
							if (!isLocalFile) {
								URL.revokeObjectURL(audioSource);
							}
						} catch (e) {
							console.error('释放音频URL失败', e);
						}
						// #endif
						
						// #ifdef APP-PLUS
						// App环境处理逻辑不需要在这里删除文件，因为所有临时文件会在退出时统一清理
						// #endif
						
						// #ifdef MP
						// 小程序环境尝试删除临时文件
						try {
							uni.removeSavedFile({
								filePath: audioSource,
								fail: (err) => {
									console.error('删除临时音频文件失败', err);
								}
							});
						} catch (e) {
							console.error('尝试删除临时文件时出错', e);
						}
						// #endif
						
						// 继续播放下一个音频块
						this.playNextAudioChunk();
					});
					
					// 播放错误处理
					audioCtx.onError((err) => {
						console.error('音频块播放错误', err);
						if (this.audioDiagnosticMode) {
							console.log(`[音频诊断] ❌ 音频块播放失败: ${err.errMsg || JSON.stringify(err)}`);
						}
						// 继续尝试播放下一块
						this.playNextAudioChunk();
					});
				} catch (e) {
					console.error('创建音频播放器失败', e);
					if (this.audioDiagnosticMode) {
						console.log(`[音频诊断] ❌ 创建音频播放器异常: ${e.message}`);
					}
					this.playNextAudioChunk(); // 尝试继续播放
				}
			},
			
			// 流式播放完成后的处理
			finalizeStreamAudio() {
				// 等待所有音频块播放完毕
				this.isStreamPlaying = false;
				
				// 不需要额外处理，因为完整的音频已经在processCompleteAudio中处理
				console.log('流式音频播放已完成');
				
				if (this.audioDiagnosticMode) {
					console.log(`[音频诊断] 流式音频播放完成, 统计: 收到${this.audioProcessingStats.chunksReceived}块, 保存${this.audioProcessingStats.chunksSaved}块, 播放${this.audioProcessingStats.chunksPlayed}块`);
				}
			},
			
			// 处理完整的音频数据
			processCompleteAudio() {
				// 合并所有音频数据
				let totalLength = 0;
				this.audioBuffer.forEach(buffer => {
					totalLength += buffer.length;
				});
				
				const mergedBuffer = new Uint8Array(totalLength);
				let offset = 0;
				
				this.audioBuffer.forEach(buffer => {
					mergedBuffer.set(buffer, offset);
					offset += buffer.length;
				});
				
				// 诊断信息
				if (this.audioDiagnosticMode) {
					console.log(`[音频诊断] 1处理完整音频, 总大小: ${totalLength} 字节, 块数: ${this.audioBuffer.length}`);
				}
				
				// 根据不同平台处理完整音频
				// #ifdef H5
				try {
					// H5环境使用Blob直接创建
					const audioBlob = new Blob([mergedBuffer], { type: 'audio/mp3' });
					const audioUrl = URL.createObjectURL(audioBlob);
					
					if (this.audioDiagnosticMode) {
						console.log(`[音频诊断] H5环境创建音频Blob URL 成功: ${audioUrl}`);
					}
					
					// 添加到音频源
					this.audioSources.push(audioUrl);
					
					// 添加或更新AI消息中的音频信息
					this.updateAIMessageWithAudio(audioUrl);
				} catch (e) {
					console.error('H5环境处理完整音频失败', e);
					if (this.audioDiagnosticMode) {
						console.log(`[音频诊断] ❌ H5环境处理完整音频异常: ${e.message}`);
					}
				}
				// #endif
				
				// #ifdef APP-PLUS
				try {
					// 将完整音频数据转换为base64
					const base64Audio = uni.arrayBufferToBase64(mergedBuffer.buffer);
					
					if (this.audioDiagnosticMode) {
						console.log(`[音频诊断] APP环境 - 完整音频转为base64成功, 长度: ${base64Audio.length}`);
					}
					
					// 使用plus.io写入文件
					this.saveCompleteAudioToFile(base64Audio).then(tempFilePath => {
						if (tempFilePath) {
							if (this.audioDiagnosticMode) {
								console.log(`[音频诊断] APP环境 - 完整音频文件保存成功 ✅ 路径: ${tempFilePath}`);
								this.audioProcessingStats.completeSaved = true;
							}
							
							// 添加到音频源
							this.audioSources.push(tempFilePath);
							
							// 添加或更新AI消息中的音频信息
							this.updateAIMessageWithAudio(tempFilePath);
						} else {
							if (this.audioDiagnosticMode) {
								console.log(`[音频诊断] APP环境 - ⚠️ 完整音频文件保存失败, 返回null`);
							}
						}
					});
				} catch (e) {
					console.error('APP环境处理完整音频失败', e);
					if (this.audioDiagnosticMode) {
						console.log(`[音频诊断] APP环境 - ❌ 处理完整音频异常: ${e.message}`);
					}
				}
				// #endif
				
				// #ifdef MP
				// 小程序环境
				try {
					const fs = wx.getFileSystemManager();
					const tempFilePath = `${wx.env.USER_DATA_PATH}/complete_audio_${Date.now()}.mp3`;
					
					fs.writeFile({
						filePath: tempFilePath,
						data: mergedBuffer.buffer,
						success: () => {
							console.log('小程序环境创建音频文件', tempFilePath);
							this.audioSources.push(tempFilePath);
							this.updateAIMessageWithAudio(tempFilePath);
						},
						fail: (err) => {
							console.error('小程序环境写入完整音频文件失败', err);
						}
					});
				} catch (e) {
					console.error('小程序环境处理完整音频失败', e);
				}
				// #endif
				
				// 清空音频缓冲区
				this.audioBuffer = [];
			},
			
			// 保存完整音频到文件 (APP环境)
			saveCompleteAudioToFile(base64Audio) {
				return new Promise((resolve, reject) => {
					// #ifdef APP-PLUS
					try {
						const tempFileName = `complete_audio_${Date.now()}.mp3`;
						const tempDir = '_doc/temp_audio/';
						const filePath = `${tempDir}${tempFileName}`;
						
						// 确保目录存在
						plus.io.requestFileSystem(plus.io.PRIVATE_DOC, (fs) => {
							fs.root.getDirectory('temp_audio', {
								create: true
							}, (dirEntry) => {
								// 将base64转为二进制数组
								const byteCharacters = atob(base64Audio);
								const byteNumbers = new Array(byteCharacters.length);
								for (let i = 0; i < byteCharacters.length; i++) {
									byteNumbers[i] = byteCharacters.charCodeAt(i);
								}
								const byteArray = new Uint8Array(byteNumbers);
								
								// 使用uni.saveFile直接保存文件
								const tempFilePath = `_doc/temp_${Date.now()}.temp`;
								uni.getFileSystemManager().writeFile({
									filePath: tempFilePath,
									data: byteArray.buffer,
									success: () => {
										uni.saveFile({
											tempFilePath: tempFilePath,
											success: (res) => {
												const savedFilePath = res.savedFilePath;
												console.log('完整音频文件保存成功:', savedFilePath);
												resolve(savedFilePath);
											},
											fail: (err) => {
												console.error('完整音频文件保存失败:', err);
												
												// 尝试备用方法
												this.saveAudioFileAlternative(base64Audio, filePath).then(path => {
													resolve(path);
												});
											}
										});
									},
									fail: (err) => {
										console.error('写入临时文件失败:', err);
										
										// 尝试备用方法
										this.saveAudioFileAlternative(base64Audio, filePath).then(path => {
											resolve(path);
										});
									}
								});
							}, (error) => {
								console.error('创建临时音频目录失败:', error);
								resolve(null);
							});
						}, (error) => {
							console.error('获取文件系统失败:', error);
							resolve(null);
						});
					} catch (e) {
						console.error('保存完整音频文件过程出错:', e);
						
						// 生成一个新的文件路径（因为在catch中filePath可能不在作用域内）
						const emergencyFilePath = `_doc/temp_audio/emergency_complete_${Date.now()}.mp3`;
						
						// 尝试备用方法
						this.saveAudioFileAlternative(base64Audio, emergencyFilePath).then(path => {
							resolve(path);
						}).catch(() => {
							resolve(null);
						});
					}
					// #endif
					
					// 非APP环境
					// #ifndef APP-PLUS
					resolve(null);
					// #endif
				});
			},
			
			// 更新AI消息中的音频信息
			updateAIMessageWithAudio(audioSource) {
				if (!this.currentAIMessage) {
					// 如果没有当前文本消息，创建一个新的音频消息
					const audioMessage = {
						content: '[语音回复]',
						time: this.formatTime(new Date()),
						timestamp: Date.now(),
						isUser: false,
						hasAudio: true,
						audioUrl: audioSource,
						isPlaying: false // 添加播放状态标记
					};
					this.aiMessages.push(audioMessage);
				} else {
					// 将音频添加到当前文本消息
					this.currentAIMessage.hasAudio = true;
					this.currentAIMessage.audioUrl = audioSource;
					this.currentAIMessage.isPlaying = false; // 添加播放状态标记
				}
				
				// 滚动到底部
				this.scrollToBottom();
				
				if (this.audioDiagnosticMode) {
					console.log(`[音频诊断] 音频URL已添加到AI消息 [${audioSource}]`);
				}
			},
			
			// 清理音频资源
			clearAudioResources() {
				// #ifdef H5
				// 在H5环境释放所有URL对象
				this.audioSources.forEach(url => {
					try {
						URL.revokeObjectURL(url);
					} catch (e) {
						console.error('释放音频URL失败', e);
					}
				});
				
				this.streamAudioQueue.forEach(url => {
					try {
						URL.revokeObjectURL(url);
					} catch (e) {
						console.error('释放流式音频URL失败', e);
					}
				});
				// #endif
				
				// #ifdef APP-PLUS
				// 在App环境删除临时文件
				this.audioSources.forEach(filePath => {
					try {
						// 使用plus.io删除文件
						if (filePath && filePath.indexOf('_doc/') === 0) {
							plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
								entry.remove(() => {
									console.log('临时音频文件删除成功:', filePath);
								}, (err) => {
									console.error('临时音频文件删除失败:', err);
								});
							}, (err) => {
								console.error('解析文件路径失败:', err);
							});
						}
					} catch (e) {
						console.error('删除音频文件出错', e);
					}
				});
				
				// 清理流式音频临时文件
				this.streamAudioQueue.forEach(filePath => {
					try {
						if (filePath && filePath.indexOf('_doc/') === 0) {
							plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
								entry.remove();
							});
						}
					} catch (e) {
						console.error('删除流式音频临时文件失败', e);
					}
				});
				// #endif
				
				// #ifdef MP
				// 小程序环境删除临时文件
				this.audioSources.forEach(filePath => {
					try {
						uni.removeSavedFile({
							filePath: filePath,
							fail: (err) => {
								console.error('删除音频文件失败', err);
							}
						});
					} catch (e) {
						console.error('删除音频文件出错', e);
					}
				});
				// #endif
				
				// 清空数组
				this.audioSources = [];
				this.streamAudioQueue = [];
				this.audioChunks = [];
				this.audioBuffer = [];
				
				if (this.audioDiagnosticMode) {
					console.log(`[音频诊断] 所有音频资源已清理`);
				}
			},
			
			// 播放或暂停音频
			playAudio(audioUrl, messageIndex) {
				console.log('播放/暂停音频', audioUrl);
				
				// 静音处理
				if (this.isMuted) {
					console.log('当前为静音状态，不播放音频');
					uni.showToast({
						title: '当前为静音状态',
						icon: 'none'
					});
					return;
				}
				
				// 停止所有片段播放
				this.isStreamPlaying = false;
				this.streamAudioQueue = [];
				
				// 如果正在播放同一个音频，则暂停播放
				if (this.currentPlayingUrl === audioUrl && this.currentAudioContext) {
					// 查找当前正在播放的消息
					const playingMessage = this.aiMessages.find(msg => msg.audioUrl === audioUrl);
					if (playingMessage) {
						playingMessage.isPlaying = false;
					}
					
					this.currentAudioContext.destroy(); // 销毁音频实例
					this.currentAudioContext = null;
					this.currentPlayingUrl = '';
					this.isPlaying = false;
					console.log('音频已暂停');
					return;
				}
				
				// 如果正在播放其他音频，先停止它
				if (this.currentAudioContext) {
					// 查找当前正在播放的消息
					const oldPlayingMessage = this.aiMessages.find(msg => msg.audioUrl === this.currentPlayingUrl);
					if (oldPlayingMessage) {
						oldPlayingMessage.isPlaying = false;
					}
					
					this.currentAudioContext.destroy();
					this.currentAudioContext = null;
					this.currentPlayingUrl = '';
				}
				
				// 开始播放新的音频
				try {
					// 查找并更新消息的播放状态
					const messageToPlay = this.aiMessages.find(msg => msg.audioUrl === audioUrl);
					if (messageToPlay) {
						messageToPlay.isPlaying = true;
					}
					
					// 创建新的音频上下文
					const audioCtx = uni.createInnerAudioContext();
					
					// 判断是否为本地文件路径（App环境下的_doc开头的路径）
					let isLocalFile = false;
					// #ifdef APP-PLUS
					if (audioUrl && typeof audioUrl === 'string' && audioUrl.indexOf('_doc/') === 0) {
						isLocalFile = true;
						if (this.audioDiagnosticMode) {
							console.log(`[音频诊断] 播放本地完整音频文件: ${audioUrl}`);
						}
					}
					// #endif
					
					// 设置音频源
					audioCtx.src = audioUrl;
					audioCtx.autoplay = true;
					
					// 保存当前播放的音频上下文和URL
					this.currentAudioContext = audioCtx;
					this.currentPlayingUrl = audioUrl;
					this.isPlaying = true;
					
					audioCtx.onPlay(() => {
						console.log('音频播放开始');
						if (this.audioDiagnosticMode) {
							console.log(`[音频诊断] 完整音频开始播放成功 ✅`);
							this.audioProcessingStats.completePlayed = true;
						}
					});
					
					audioCtx.onEnded(() => {
						console.log('音频播放结束');
						
						// 更新消息的播放状态
						if (messageToPlay) {
							messageToPlay.isPlaying = false;
						}
						
						// 重置播放状态
						this.isPlaying = false;
						this.currentAudioContext = null;
						this.currentPlayingUrl = '';
					});
					
					audioCtx.onError((res) => {
						console.error('音频播放错误', res);
						if (this.audioDiagnosticMode) {
							console.log(`[音频诊断] ❌ 完整音频播放错误: ${res.errMsg || JSON.stringify(res)}`);
						}
						
						// 更新消息的播放状态
						if (messageToPlay) {
							messageToPlay.isPlaying = false;
						}
						
						// 重置播放状态
						this.isPlaying = false;
						this.currentAudioContext = null;
						this.currentPlayingUrl = '';
						
						// 显示错误提示
						uni.showToast({
							title: '音频播放失败',
							icon: 'none'
						});
					});
				} catch (e) {
					console.error('创建音频播放器失败', e);
					if (this.audioDiagnosticMode) {
						console.log(`[音频诊断] ❌ 创建完整音频播放器异常: ${e.message}`);
					}
					this.isPlaying = false;
					
					uni.showToast({
						title: '创建音频播放器失败',
						icon: 'none'
					});
				}
			},
			
			// 初始化数据
			initData() {
				// 重置音频相关数据
				this.audioSources = [];
				this.currentTrackIndex = 0;
				this.isPlaying = false;
				this.audioBuffer = null;
				
				// 重置消息流相关数据
				this.currentAIMessage = null;
				this.isCurrentResponseComplete = true;
			},
			
			// 获取用户信息
			getUserInfo() {
				// 从缓存或API获取用户头像等信息
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.userAvatar = userInfo.avatarUrl;
					if (userInfo.id) {
						this.userId = userInfo.id;
					}
				}
			},
			
			// 初始化录音管理器
			initRecorderManager() {
				try {
					// 检查平台是否支持录音功能
					if (!uni.getRecorderManager) {
						console.error('当前平台不支持录音功能');
						uni.showToast({
							title: '当前平台不支持录音',
							icon: 'none'
						});
						return;
					}
					
					// 获取全局唯一的录音管理器
					this.recorderManager = uni.getRecorderManager();
					
					// 检查录音管理器是否有效
					if (!this.recorderManager) {
						console.error('录音管理器初始化失败');
						uni.showToast({
							title: '录音功能不可用',
							icon: 'none'
						});
						return;
					}
					
					// 监听录音开始事件
					if (typeof this.recorderManager.onStart === 'function') {
						this.recorderManager.onStart(() => {
							console.log('录音开始');
							this.isRecording = true;
							this.recordStartTime = Date.now();
							
							// 开始波形动画
							this.startWaveAnimation();
							
							// 设置录音超时（最长60秒）
							this.recordingTimeout = setTimeout(() => {
								if (this.isRecording) {
									this.stopRecording();
								}
							}, 60000);
						});
					}
					
					// 监听录音结束事件
					if (typeof this.recorderManager.onStop === 'function') {
						this.recorderManager.onStop((res) => {
							console.log('录音结束', res);
							this.isRecording = false;
							clearTimeout(this.recordingTimeout);
							
							// 停止波形动画
							if (this.animationTimer) {
								clearInterval(this.animationTimer);
								this.animationTimer = null;
							}
							
							// 计算录音时长
							this.recordDuration = Math.round((Date.now() - this.recordStartTime) / 1000);
							
							// 处理录音文件
							if (res && res.tempFilePath) {
								this.currentVoiceFile = res.tempFilePath;
								// 发送语音消息
								this.sendVoiceMessage(res.tempFilePath, this.recordDuration);
							}
						});
					}
					
					// 监听录音错误事件
					if (typeof this.recorderManager.onError === 'function') {
						this.recorderManager.onError((res) => {
							console.error('录音失败', res);
							this.isRecording = false;
							clearTimeout(this.recordingTimeout);
							
							uni.showToast({
								title: '录音失败',
								icon: 'none'
							});
						});
					}
					
					// 监听声音大小变化事件 - 仅在支持的平台使用
					if (this.recorderManager.onFrameRecorded && typeof this.recorderManager.onFrameRecorded === 'function') {
						this.recorderManager.onFrameRecorded((res) => {
							// 处理音频数据
						});
					}
				} catch (e) {
					console.error('初始化录音管理器失败', e);
					this.recorderManager = null; // 确保失败时设为null
					uni.showToast({
						title: '录音功能初始化失败',
						icon: 'none'
					});
				}
			},
			
			// 开始波形动画 - 使用随机波形代替依赖于分贝值的波形
			startWaveAnimation() {
				// 清除之前的动画定时器
				if (this.animationTimer) {
					clearInterval(this.animationTimer);
				}
				
				// 创建新的动画定时器 - 模拟波形变化
				this.animationTimer = setInterval(() => {
					let baseHeight = 10;
					let maxHeight = 40;
					
					let newWave = [];
					for (let i = 0; i < 9; i++) {
						// 生成随机波形高度
						let randomFactor = Math.random(); // 0~1之间的随机数
						let height = baseHeight + (maxHeight - baseHeight) * randomFactor;
						newWave.push(Math.round(height));
					}
					
					this.voiceWaveArray = newWave;
				}, 150); // 每150ms更新一次波形
			},
			
			// z-paging的查询方法
			loadMessages(pageNo, pageSize) {
				if (pageNo === 1) {
					// 第一页，加载最新消息
					this.loadChatHistory();
				} else {
					// 加载更多历史消息
					this.loadMoreHistoryMessages(pageNo, pageSize);
				}
			},
			
			// 加载聊天历史
			loadChatHistory() {
				this.loading = true;
				// 实际应用中这里应该调用API获取聊天记录
				setTimeout(() => {
					this.loading = false;
					
					// 如果没有连接，尝试连接WebSocket
					if (!this.isConnected) {
						this.initWebSocket();
					}
					
					// 通知z-paging加载完成
					this.$refs.paging && this.$refs.paging.complete();
				}, 800);
			},
			
			// 加载更多历史消息
			loadMoreHistoryMessages(pageNo, pageSize) {
				// 实现分页加载更多历史消息逻辑
				this.loading = true;
				// 模拟API请求
				setTimeout(() => {
					// 加载更多历史消息
					this.loading = false;
					
					if (this.hasMoreMessages) {
						// 有更多数据
						this.$refs.paging && this.$refs.paging.complete();
					} else {
						// 没有更多数据了
						this.$refs.paging && this.$refs.paging.completeByTotal(this.aiMessages.length + this.userMessages.length);
					}
				}, 800);
			},
			
			// 发送消息
			sendMessage() {
				if (!this.inputMessage.trim()) return;
				
				// 检查是否正在响应中，如果是则不允许发送新消息
				if (this.isResponding) {
					uni.showToast({
						title: 'AI正在回复中，请稍候...',
						icon: 'none'
					});
					return;
				}
				
				// 检查WebSocket连接
				if (!this.isConnected) {
					uni.showToast({
						title: '连接已断开，请重新连接',
						icon: 'none'
					});
					
					// 尝试重新连接
					this.initWebSocket();
					return;
				}
				
				// 获取消息内容
				const messageContent = this.inputMessage.trim();
				
				// 添加到用户消息列表
				this.addUserMessage(messageContent);
				
				// 清空输入框
				this.inputMessage = '';
				
				// 重置音频和消息流状态
				this.resetMessageState();
				
				// 立即进入响应状态
				this.isResponding = true;
				
				// 使用WebSocket发送消息
				try {
					// 使用字符串转二进制函数替代TextEncoder
					const uint8Array = this.stringToUint8Array(messageContent);
					
					// 发送文本消息（作为二进制数据）
					uni.sendSocketMessage({
						data: uint8Array.buffer,
						success: () => {
							console.log('消息已发送');
							
							// 发送结束标记
							const endMarker = new Uint8Array([255, 255]);
							uni.sendSocketMessage({
								data: endMarker.buffer,
								success: () => {
									console.log('结束标记已发送');
								},
								fail: (err) => {
									console.error('发送结束标记失败', err);
									this.isResponding = false;
								}
							});
						},
						fail: (err) => {
							console.error('发送消息失败', err);
							this.isResponding = false;
							uni.showToast({
								title: '发送失败',
								icon: 'none'
							});
						}
					});
				} catch (e) {
					console.error('发送消息失败', e);
					// 发送失败时重置响应状态
					this.isResponding = false;
					uni.showToast({
						title: '发送失败',
						icon: 'none'
					});
				}
			},
			
			// 重置消息状态
			resetMessageState() {
				// 标记当前响应已完成
				this.isCurrentResponseComplete = true;
				this.currentAIMessage = null;
				
				// 清空音频缓冲区
				this.audioBuffer = [];
			},
			
			// 发送语音消息
			sendVoiceMessage(filePath, duration) {
				// 检查WebSocket连接
				if (!this.isConnected) {
					uni.showToast({
						title: '连接已断开，请重新连接',
						icon: 'none'
					});
					
					// 尝试重新连接
					this.initWebSocket();
					return;
				}
				
				// 构造语音消息对象
				const message = {
					content: '[语音消息]',
					time: this.formatTime(new Date()),
					timestamp: Date.now(),
					isUser: true,
					isVoice: true,
					filePath: filePath, // 语音文件路径
					duration: duration || 1 // 语音时长(秒)
				};
				
				// 添加到用户消息列表
				this.userMessages.push(message);
				
				// 滚动到底部
				this.scrollToBottom();
				
				// 重置音频和消息流状态
				this.resetMessageState();
				
				// 立即进入响应状态
				this.isResponding = true;
				
				// #ifdef APP-PLUS
				console.log('正在读取语音文件:', filePath);
				
				// 使用plus.io读取文件
				plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
					entry.file((file) => {
						console.log('获取到文件信息:', JSON.stringify(file));
						
						// 使用plus.io.FileReader读取文件
						const reader = new plus.io.FileReader();
						
						reader.onloadend = (event) => {
							// 确保读取成功并且有结果
							if (event.target && event.target.result) {
								try {
									// 将base64数据部分提取出来
									let base64Data = event.target.result;
									// 如果数据是base64 URL格式，截取数据部分
									if (base64Data.indexOf('base64,') > -1) {
										base64Data = base64Data.split('base64,')[1];
									}
									
									// 将base64转为二进制数组
									const audioData = uni.base64ToArrayBuffer(base64Data);
									const uint8Array = new Uint8Array(audioData);
									
									console.log('读取到的音频数据大小:', uint8Array.length);
									
									// 发送音频数据
									uni.sendSocketMessage({
										data: audioData,
										success: () => {
											console.log('语音文件发送成功');
											// 发送结束标记
											const endMarker = new Uint8Array([255, 255]);
											uni.sendSocketMessage({
												data: endMarker.buffer,
												success: () => {
													console.log('语音消息结束标记已发送');
												},
												fail: (err) => {
													console.error('发送语音消息结束标记失败', err);
													this.isResponding = false;
												}
											});
										},
										fail: (err) => {
											console.error('发送语音文件失败', err);
											this.isResponding = false;
											uni.showToast({
												title: '发送失败',
												icon: 'none'
											});
										}
									});
								} catch (e) {
									console.error('处理音频数据失败', e);
									this.isResponding = false;
									uni.showToast({
										title: '发送失败',
										icon: 'none'
									});
								}
							} else {
								console.error('读取文件返回空结果');
								this.isResponding = false;
								uni.showToast({
									title: '读取文件失败',
									icon: 'none'
								});
							}
						};
						
						reader.onerror = (error) => {
							console.error('读取文件失败', error);
							this.isResponding = false;
							uni.showToast({
								title: '读取文件失败',
								icon: 'none'
							});
						};
						
						// 以DataURL方式读取文件
						reader.readAsDataURL(file);
						
					}, (error) => {
						console.error('获取文件对象失败', error);
						this.isResponding = false;
						uni.showToast({
							title: '读取文件失败',
							icon: 'none'
						});
					});
				}, (error) => {
					console.error('解析文件URL失败', error);
					this.isResponding = false;
					uni.showToast({
						title: '读取文件失败',
						icon: 'none'
					});
				});
				// #endif
				
				// #ifdef MP
				// 小程序环境使用 wx.getFileSystemManager()
				const fileManager = uni.getFileSystemManager();
				fileManager.readFile({
					filePath: filePath,
					success: (res) => {
						try {
							// 将文件数据转换为Uint8Array
							const audioData = new Uint8Array(res.data);
							
							// 一次性发送整个音频文件
							uni.sendSocketMessage({
								data: audioData.buffer,
								success: () => {
									console.log('语音文件发送成功');
									// 发送结束标记
									const endMarker = new Uint8Array([255, 255]);
									uni.sendSocketMessage({
										data: endMarker.buffer,
										success: () => {
											console.log('语音消息结束标记已发送');
										},
										fail: (err) => {
											console.error('发送语音消息结束标记失败', err);
											this.isResponding = false;
										}
									});
								},
								fail: (err) => {
									console.error('发送语音文件失败', err);
									this.isResponding = false;
									uni.showToast({
										title: '发送失败',
										icon: 'none'
									});
								}
							});
						} catch (e) {
							console.error('发送语音消息失败', e);
							this.isResponding = false;
							uni.showToast({
								title: '发送失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						console.error('读取语音文件失败', err);
						this.isResponding = false;
						uni.showToast({
							title: '读取文件失败',
							icon: 'none'
						});
					}
				});
				// #endif
			},
			
			// 播放语音消息
			playVoice(message) {
				if (!message.filePath) return;
				
				try {
					const innerAudioContext = uni.createInnerAudioContext();
					innerAudioContext.src = message.filePath;
					innerAudioContext.autoplay = true;
					
					innerAudioContext.onPlay(() => {
						console.log('开始播放语音');
					});
					
					innerAudioContext.onError((res) => {
						console.error('播放语音失败', res);
						uni.showToast({
							title: '播放失败',
							icon: 'none'
						});
					});
				} catch (e) {
					console.error('播放语音失败', e);
					uni.showToast({
						title: '播放失败',
						icon: 'none'
					});
				}
			},
			
			// 滚动到底部
			scrollToBottom() {
				this.$nextTick(() => {
					// 延迟执行滚动，确保消息渲染完成
					setTimeout(() => {
						// 使用z-paging的滚动到底部方法
						this.$refs.paging && this.$refs.paging.scrollToBottom(false);
					}, 100);
				});
			},
			
			// 处理附件选择
			handleActionSelect(index) {
				switch(index) {
					case 0: // 发送图片
						this.chooseImage();
						break;
					case 1: // 发送文件
						this.chooseFile();
						break;
					case 2: // 我的材料
						uni.navigateTo({
							url: '/pages/materials/index'
						});
						break;
				}
			},
			
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						// 处理选择的图片
						const tempFilePath = res.tempFilePaths[0];
						this.uploadFile(tempFilePath, 'image');
					}
				});
			},
			
			// 选择文件
			chooseFile() {
				// 选择文件逻辑，具体API根据平台支持情况调整
				uni.showToast({
					title: '选择文件功能开发中',
					icon: 'none'
				});
			},
			
			// 上传文件
			uploadFile(filePath, type) {
				uni.showLoading({
					title: '正在上传...'
				});
				
				// 检查WebSocket连接
				if (!this.isConnected) {
					uni.hideLoading();
					uni.showToast({
						title: '连接已断开，请重新连接',
						icon: 'none'
					});
					
					// 尝试重新连接
					this.initWebSocket();
					return;
				}
				
				// 读取文件
				uni.getFileSystemManager().readFile({
					filePath: filePath,
					success: (res) => {
						try {
							// 添加到用户消息列表
							const messageContent = type === 'image' ? 
								`<img src="${filePath}" style="max-width: 200px;" />` : 
								'[文件]';
							
							// 添加用户消息
							this.addUserMessage(messageContent);
							
							// 将文件数据转换为Uint8Array
							const fileData = new Uint8Array(res.data);
							
							// 分片发送文件数据
							let offset = 0;
							const sendNextChunk = () => {
								if (offset >= fileData.byteLength) {
									// 所有数据已发送，发送结束标记
									const endMarker = new Uint8Array([255, 255]);
									uni.sendSocketMessage({
										data: endMarker.buffer,
										success: () => {
											console.log('文件结束标记已发送');
											uni.hideLoading();
										},
										fail: (err) => {
											console.error('发送文件结束标记失败', err);
											uni.hideLoading();
											uni.showToast({
												title: '发送失败',
												icon: 'none'
											});
										}
									});
									return;
								}
								
								const end = Math.min(offset + this.MAX_CHUNK_SIZE, fileData.byteLength);
								const chunk = fileData.slice(offset, end);
								
								// 发送文件数据片段
								uni.sendSocketMessage({
									data: chunk.buffer,
									success: () => {
										offset = end;
										// 继续发送下一片段
										sendNextChunk();
									},
									fail: (err) => {
										console.error('发送文件数据片段失败', err);
										uni.hideLoading();
										uni.showToast({
											title: '发送失败',
											icon: 'none'
										});
									}
								});
							};
							
							// 开始发送第一片段
							sendNextChunk();
							
						} catch (e) {
							console.error('发送文件失败', e);
							uni.hideLoading();
							uni.showToast({
								title: '发送失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						console.error('读取文件失败', err);
						uni.hideLoading();
						uni.showToast({
							title: '读取文件失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 切换通话状态
			toggleCall() {
				this.isInCall = !this.isInCall;
				uni.showToast({
					title: this.isInCall ? '通话已开始' : '通话已结束',
					icon: 'none'
				});
			},
			
			// 切换静音状态
			toggleMute() {
				this.isMuted = !this.isMuted;
				
				// 更新当前正在播放的音频状态
				if (this.currentAudioContext) {
					this.currentAudioContext.volume = this.isMuted ? 0 : 1;
				}
				
				// 提示用户
				uni.showToast({
					title: this.isMuted ? '已静音' : '已取消静音',
					icon: 'none'
				});
			},
			
			// 下拉刷新 - 会自动触发loadMessages
			onRefresh() {
				// z-paging已经内部处理了刷新逻辑
			},
			
			// 格式化日期
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 格式化时间
			formatTime(date) {
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${hours}:${minutes}`;
			},
			
			// 切换输入模式（语音/键盘）
			toggleInputMode() {
				this.isVoiceMode = !this.isVoiceMode;
			},
			
			// 开始录音
			startRecording(e) {
				// 防止多次调用
				if (this.isRecording) return;
				
				try {
					// 检查录音管理器是否可用
					if (!this.recorderManager) {
						console.error('录音管理器未初始化');
						uni.showToast({
							title: '录音功能不可用',
							icon: 'none'
						});
						return;
					}
					
					// 检查start方法是否存在
					if (typeof this.recorderManager.start !== 'function') {
						console.error('录音管理器不支持start方法');
						uni.showToast({
							title: '录音功能不支持',
							icon: 'none'
						});
						return;
					}
					
					// 记录触摸开始的Y坐标，用于判断上滑取消
					if (e && e.touches && e.touches[0]) {
						this.cancelRecordY = e.touches[0].clientY;
					}
					
					// 设置录音参数
					const options = {
						duration: 60000, // 最长录音时间，单位ms
						sampleRate: 16000, // 采样率
						numberOfChannels: 1, // 录音通道数
						encodeBitRate: 96000, // 编码码率
						format: 'mp3' // 音频格式
					};
					
					// 开始录音
					this.recorderManager.start(options);
				} catch (e) {
					console.error('开始录音失败', e);
					this.isRecording = false; // 确保状态正确
					uni.showToast({
						title: '录音失败',
						icon: 'none'
					});
				}
			},
			
			// 结束录音并发送
			stopRecording(e) {
				if (!this.isRecording) return;
				
				try {
					// 检查录音管理器是否可用
					if (!this.recorderManager || typeof this.recorderManager.stop !== 'function') {
						console.error('录音管理器不可用或不支持stop方法');
						this.isRecording = false;
						clearTimeout(this.recordingTimeout);
						
						if (this.animationTimer) {
							clearInterval(this.animationTimer);
							this.animationTimer = null;
						}
						
						uni.showToast({
							title: '录音功能不支持',
							icon: 'none'
						});
						return;
					}
					
					// 检查是否为上滑取消
					if (e && e.changedTouches && e.changedTouches[0]) {
						const curY = e.changedTouches[0].clientY;
						const diffY = this.cancelRecordY - curY;
						
						// 如果上滑距离超过50，则取消发送
						if (diffY > 50) {
							this.cancelRecording();
							return;
						}
					}
					
					// 停止录音
					this.recorderManager.stop();
				} catch (e) {
					console.error('停止录音失败', e);
					this.isRecording = false;
					clearTimeout(this.recordingTimeout);
					
					// 清除动画定时器
					if (this.animationTimer) {
						clearInterval(this.animationTimer);
						this.animationTimer = null;
					}
					
					uni.showToast({
						title: '录音失败',
						icon: 'none'
					});
				}
			},
			
			// 取消录音
			cancelRecording() {
				if (!this.isRecording) return;
				
				try {
					// 检查录音管理器是否可用
					if (!this.recorderManager || typeof this.recorderManager.stop !== 'function') {
						console.error('录音管理器不可用或不支持stop方法');
						this.isRecording = false;
						this.recordingTip = '录音已取消';
						
						setTimeout(() => {
							this.recordingTip = '松开发送，上滑取消';
						}, 500);
						
						// 清除录音文件
						this.currentVoiceFile = '';
						
						// 清除动画定时器
						if (this.animationTimer) {
							clearInterval(this.animationTimer);
							this.animationTimer = null;
						}
						
						uni.showToast({
							title: '录音功能不支持',
							icon: 'none'
						});
						return;
					}
					
					this.recordingTip = '已取消录音';
					setTimeout(() => {
						this.recordingTip = '松开发送，上滑取消';
						this.isRecording = false;
					}, 500);
					
					// 停止录音但不发送
					this.recorderManager.stop();
					
					// 清除录音文件
					this.currentVoiceFile = '';
					
					uni.showToast({
						title: '已取消录音',
						icon: 'none'
					});
				} catch (e) {
					console.error('取消录音失败', e);
					this.isRecording = false;
					
					// 清除动画定时器
					if (this.animationTimer) {
						clearInterval(this.animationTimer);
						this.animationTimer = null;
					}
					
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					});
				}
			},
			
			// 添加用户消息
			addUserMessage(content, isVoice = false, filePath = '', duration = 0) {
				const message = {
					content: content,
					time: this.formatTime(new Date()),
					timestamp: Date.now(),
					isUser: true,
					isVoice: isVoice,
					filePath: filePath,
					duration: duration
				};
				
				// 添加到用户消息列表
				this.userMessages.push(message);
				
				// 滚动到底部
				this.scrollToBottom();
			},
			
			// 添加AI消息
			addAIMessage(content) {
				const message = {
					content: content,
					time: this.formatTime(new Date()),
					timestamp: Date.now(),
					isUser: false
				};
				
				// 添加到AI消息列表
				this.aiMessages.push(message);
				
				// 滚动到底部
				this.scrollToBottom();
			},
			
			// 添加系统消息
			addSystemMessage(content) {
				const message = {
					content: content,
					time: this.formatTime(new Date()),
					timestamp: Date.now(),
					isUser: false,
					isSystem: true
				};
				
				// 添加到AI消息列表（作为系统提示）
				this.aiMessages.push(message);
				
				// 滚动到底部
				this.scrollToBottom();
			},
			
			// 清空消息列表
			clearMessages() {
				this.aiMessages = [];
				this.userMessages = [];
				this.resetMessageState();
				this.audioSources = [];
				this.currentTrackIndex = 0;
				this.isPlaying = false;
			},
			
			// 停止所有音频播放
			stopAllAudio() {
				// 停止流式播放
				this.isStreamPlaying = false;
				this.streamAudioQueue = [];
				
				// 停止完整音频播放
				this.isPlaying = false;
				
				// 如果有正在播放的音频，停止它
				if (this.currentAudioContext) {
					try {
						this.currentAudioContext.destroy();
					} catch (e) {
						console.error('停止音频播放失败', e);
					}
					this.currentAudioContext = null;
					this.currentPlayingUrl = '';
				}
				
				// 重置所有消息的播放状态
				this.aiMessages.forEach(msg => {
					if (msg.hasAudio) {
						msg.isPlaying = false;
					}
				});
				
				// 实际停止音频需要在小程序环境获取背景音频管理器
				// 或使用uni API进行处理，每个平台可能不同
				// 以下为通用处理方法
				try {
					const bgAudioManager = uni.getBackgroundAudioManager();
					if (bgAudioManager) {
						bgAudioManager.stop();
					}
				} catch (e) {
					console.error('停止背景音频失败', e);
				}
			},
			
			// 停止AI响应
			stopResponse() {
				// 如果没有正在响应，则不需要操作
				if (!this.isResponding) return;
				
				// 发送停止响应指令
				if (this.isConnected) {
					try {
						// 使用字符串转二进制函数替代TextEncoder
						const stopCmd = this.stringToUint8Array('[STOP]');
						
						uni.sendSocketMessage({
							data: stopCmd.buffer,
							success: () => {
								console.log('已发送停止响应指令');
							},
							fail: (err) => {
								console.error('发送停止响应指令失败', err);
							}
						});
					} catch (e) {
						console.error('发送停止响应指令失败', e);
					}
				}
				
				// 清理相关状态
				if (this.currentAIMessage) {
					this.currentAIMessage.content += ' [已停止]';
					this.currentAIMessage.isComplete = true;
				}
				
				// 停止流式播放
				this.isStreamPlaying = false;
				this.streamAudioQueue = [];
				
				// 标记响应已完成
				this.isCurrentResponseComplete = true;
				this.isResponding = false;
				
				// 通知用户
				uni.showToast({
					title: '已停止AI响应',
					icon: 'none'
				});
			},
			
			// 开始打字指示器动画
			startTypingAnimation() {
				// 使用定时器制作打字动画效果
				setInterval(() => {
					if (this.typingIndicator === '.') {
						this.typingIndicator = '..';
					} else if (this.typingIndicator === '..') {
						this.typingIndicator = '...';
					} else {
						this.typingIndicator = '.';
					}
				}, 500);
			},
			
			// 字符串转Uint8Array函数（替代TextEncoder）
			stringToUint8Array(str) {
				const arr = [];
				for (let i = 0; i < str.length; i++) {
					const code = str.charCodeAt(i);
					
					if (code < 0x80) {
						// ASCII字符（单字节）
						arr.push(code);
					} else if (code < 0x800) {
						// 双字节字符
						arr.push(0xc0 | (code >> 6));
						arr.push(0x80 | (code & 0x3f));
					} else if (code < 0xd800 || code >= 0xe000) {
						// 三字节字符
						arr.push(0xe0 | (code >> 12));
						arr.push(0x80 | ((code >> 6) & 0x3f));
						arr.push(0x80 | (code & 0x3f));
					} else {
						// 处理Unicode代理对（四字节字符）
						i++;
						const c2 = str.charCodeAt(i);
						const cp = 0x10000 + ((code & 0x3ff) << 10) + (c2 & 0x3ff);
						arr.push(0xf0 | (cp >> 18));
						arr.push(0x80 | ((cp >> 12) & 0x3f));
						arr.push(0x80 | ((cp >> 6) & 0x3f));
						arr.push(0x80 | (cp & 0x3f));
					}
				}
				return new Uint8Array(arr);
			},
			
			// 初始化StreamAudioPlayer
			initStreamAudioPlayer() {
				// 创建StreamAudioPlayer实例
				this.streamAudioPlayer = new StreamAudioPlayer();
				if (this.audioDiagnosticMode) {
					console.log('[音频诊断] StreamAudioPlayer初始化成功');
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.ai-chat-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f6f6f6;
		position: relative;
	}
	
	.navbar-right {
		display: flex;
		align-items: center;
		padding-right: 10rpx;
		
		.icon-wrapper {
			width: 80rpx;
			height: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	
	.chat-content {
		flex: 1;
		box-sizing: border-box;
	}
	
	.loading-container {
		display: flex;
		justify-content: center;
		padding: 30rpx 0;
		
		.simple-loading {
			color: #999;
			font-size: 26rpx;
		}
	}
	
	.message-list {
		padding: 20rpx;
		padding-bottom: 140rpx; /* 增加底部内边距，防止内容被输入框遮挡 */
	}
	
	.time-divider {
		text-align: center;
		font-size: 24rpx;
		color: #999;
		margin: 20rpx 0;
		background: rgba(0,0,0,0.05);
		padding: 6rpx 20rpx;
		border-radius: 20rpx;
		width: fit-content;
		margin: 20rpx auto;
	}
	
	.message-item {
		display: flex;
		margin-bottom: 30rpx;
		
		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			overflow: hidden;
			flex-shrink: 0;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.message-content {
			max-width: 70%;
			
			.message-bubble {
				padding: 20rpx;
				border-radius: 12rpx;
				word-break: break-all;
				line-height: 1.4;
				
				&.voice-message {
					min-width: 160rpx;
					padding: 16rpx 20rpx;
				}
			}
			
			.message-time {
				font-size: 24rpx;
				color: #999;
				margin-top: 8rpx;
			}
			
			.voice-message-content {
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				.voice-icon {
					display: flex;
					align-items: center;
					
					.voice-wave-icon {
						display: flex;
						align-items: center;
						height: 30rpx;
						
						.wave-line {
							width: 4rpx;
							height: 16rpx;
							background-color: currentColor;
							margin: 0 2rpx;
							border-radius: 2rpx;
							
							&:nth-child(1) {
								height: 10rpx;
							}
							&:nth-child(2) {
								height: 16rpx;
							}
							&:nth-child(3) {
								height: 22rpx;
							}
							&:nth-child(4) {
								height: 16rpx;
							}
						}
					}
				}
				
				text {
					margin-left: 10rpx;
					font-size: 24rpx;
				}
			}
		}
	}
	
	.ai-message {
		.message-content {
			margin-left: 20rpx;
			
			.message-bubble {
				background-color: #fff;
				border: 1px solid #eee;
				border-top-left-radius: 4rpx;
				
				.audio-player {
					display: flex;
					align-items: center;
					margin-top: 10rpx;
					padding: 6rpx 12rpx;
					background-color: rgba(43, 203, 212, 0.1);
					border-radius: 8rpx;
					cursor: pointer;
					
					text {
						margin-left: 10rpx;
						font-size: 24rpx;
						color: #2BCBD4;
					}
				}
			}
			
			.message-bubble.system-message {
				background-color: #f8f8f8;
				color: #999;
				font-size: 26rpx;
			}
		}
	}
	
	.user-message {
		flex-direction: row-reverse;
		
		.message-content {
			margin-right: 20rpx;
			align-items: flex-end;
			
			.message-bubble {
				background-color: #2BCBD4;
				color: #fff;
				border-top-right-radius: 4rpx;
			}
			
			.message-time {
				text-align: right;
			}
			
			.voice-message-content {
				flex-direction: row-reverse;
				
				.voice-icon {
					transform: rotateY(180deg); // 翻转语音图标
				}
				
				text {
					margin-left: 0;
					margin-right: 10rpx;
				}
			}
		}
	}
	
	.chat-footer {
		background-color: #fff;
		padding: 20rpx;
		border-top: 1px solid #eee;
		box-sizing: border-box;
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 10;
	}
	
	.input-area {
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		border-radius: 36rpx;
		padding: 10rpx 20rpx;
	}
	
	.voice-keyboard-toggle, .attachment-button {
		flex-shrink: 0;
		padding: 10rpx;
	}
	
	.message-input {
		flex: 1;
		height: 72rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
	}
	
	.voice-button-large {
		flex: 1;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		border-radius: 36rpx;
		margin: 0 10rpx;
		font-size: 28rpx;
		color: #666;
	}
	
	.send-button {
		flex-shrink: 0;
		padding: 10rpx 20rpx;
		background-color: #2BCBD4; 
		color: #fff;
		border-radius: 30rpx;
		font-size: 26rpx;
	}
	
	.stop-button {
		flex-shrink: 0;
		padding: 10rpx;
		background-color: rgba(255, 54, 54, 0.1);
		border-radius: 50%;
		width: 40rpx;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.voice-recording-tip {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 260rpx;
		height: 260rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		z-index: 999;
		
		.voice-wave {
			width: 160rpx;
			height: 120rpx;
			margin-bottom: 20rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			
			.voice-wave-container {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: flex-end;
				
				.voice-wave-bar {
					width: 8rpx;
					background-color: #fff;
					margin: 0 4rpx;
					border-radius: 4rpx;
					transition: height 0.1s ease-in-out;
				}
			}
		}
		
		text {
			color: #fff;
			font-size: 26rpx;
		}
	}
	
	/* 底部安全区域，防止内容被输入框遮挡 */
	.bottom-safe-area {
		height: 140rpx;
		width: 100%;
	}
	
	.responding-indicator {
		position: absolute;
		bottom: 100rpx;
		left: 50%;
		transform: translateX(-50%);
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
		display: flex;
		align-items: center;
		z-index: 100;
		
		text {
			color: #fff;
			font-size: 26rpx;
		}
	}
</style>