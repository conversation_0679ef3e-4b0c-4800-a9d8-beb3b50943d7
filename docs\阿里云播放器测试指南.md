# 阿里云播放器测试指南

## 测试环境要求

### 1. 依赖检查
确保项目已安装阿里云播放器SDK：
```bash
npm install aliyun-aliplayer
```

### 2. 静态资源
确保 `/static/` 目录下有阿里云播放器的组件文件（如果需要）。

## 测试步骤

### 1. 基本功能测试

#### 1.1 页面跳转测试
1. 进入课程详情页面
2. 点击任意课时的"去观看"按钮
3. 验证是否正确跳转到 `videoPlayerStable.vue` 页面

#### 1.2 播放授权获取测试
1. 观察页面加载状态
2. 检查控制台日志，确认以下信息：
   ```
   开始获取播放授权，课时ID: xxx
   获取播放授权返回: {...}
   视频ID: 104493dd6b5a71f0bfd54531858c0102
   播放授权: eyJ...
   ```

#### 1.3 播放器初始化测试
1. 播放授权获取成功后，检查控制台日志：
   ```
   设置播放器配置: {vid: "...", playauth: "...", region: "cn-shanghai"}
   renderjs mounted
   创建阿里云播放器，配置: {...}
   初始化阿里云播放器...
   阿里云播放器准备就绪
   播放器准备完成
   ```

### 2. 错误处理测试

#### 2.1 网络错误测试
1. 断开网络连接
2. 尝试进入播放页面
3. 验证是否显示网络错误提示
4. 点击重试按钮测试重试功能

#### 2.2 视频不存在测试
1. 修改课时ID为不存在的值
2. 验证是否显示"视频不存在或已被删除"错误
3. 检查错误处理是否正确

### 3. 播放器功能测试

#### 3.1 播放控制测试
1. 播放器加载完成后，点击播放按钮
2. 测试暂停、快进、音量调节等功能
3. 验证播放器事件是否正确触发

#### 3.2 全屏播放测试
1. 点击全屏按钮
2. 验证是否能正常进入全屏模式
3. 测试退出全屏功能

## 预期结果

### 1. 成功场景
- ✅ 页面正常跳转
- ✅ 播放授权获取成功
- ✅ 阿里云播放器正常初始化
- ✅ 视频能够正常播放
- ✅ 播放控制功能正常

### 2. 错误场景
- ✅ 网络错误时显示友好提示
- ✅ 视频不存在时显示相应错误
- ✅ 重试功能正常工作
- ✅ 播放器错误时有相应处理

## 调试技巧

### 1. 控制台日志
关键日志信息：
```javascript
// 播放授权获取
"开始获取播放授权，课时ID: xxx"
"获取播放授权返回: {...}"

// 播放器初始化
"设置播放器配置: {...}"
"创建阿里云播放器，配置: {...}"
"初始化阿里云播放器..."

// 播放器事件
"阿里云播放器准备就绪"
"播放器准备完成"
"开始播放"
```

### 2. 网络请求检查
在浏览器开发者工具的 Network 标签中检查：
- `getVideoPlayAuth` 接口调用是否成功
- 返回数据格式是否正确
- 是否有其他网络错误

### 3. 常见问题排查

#### 3.1 播放器不显示
- 检查 `aliyun-aliplayer` 是否正确安装
- 确认 renderjs 是否正确加载
- 查看控制台是否有 JavaScript 错误

#### 3.2 播放授权失败
- 检查课时ID是否正确
- 确认后端接口是否正常
- 验证阿里云配置是否正确

#### 3.3 播放器初始化失败
- 检查播放授权数据格式
- 确认 `vid` 和 `playauth` 参数是否正确
- 查看阿里云播放器的错误日志

## 性能监控

### 1. 加载时间
- 页面跳转到播放器初始化的时间
- 播放授权获取的响应时间
- 播放器准备就绪的时间

### 2. 内存使用
- 播放器创建和销毁是否正确
- 是否存在内存泄漏
- 页面切换时资源是否正确释放

## 兼容性测试

### 1. 平台兼容性
- H5 浏览器
- 微信小程序
- App (Android/iOS)

### 2. 设备兼容性
- 不同屏幕尺寸
- 横屏/竖屏切换
- 不同网络环境

通过以上测试步骤，可以全面验证阿里云播放器的集成是否正确和稳定。
