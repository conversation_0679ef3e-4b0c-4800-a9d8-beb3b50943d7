<template>
	<view>
		<!-- <u-navbar title="" height="44" bgColor="#ffffff00" :placeholder='true' left-icon="">
		</u-navbar> -->

		<view class="dis-ali jc_cen flex-column " style="padding-top: 300upx;">
			<u-empty mode="wifi" icon="../../static/empty/404.png" text='网络开小差了'>
			</u-empty>
			<u-toast ref="uToast"></u-toast>
			<u-button type="primary" :loading='loading' loadingText='加载中' style="width: 500upx;margin-top: 100upx;"
				color='#00C1CC' shape='circle' @click="getW">{{loadingText}}</u-button>
		</view>

		<!-- 网络设置弹窗 -->
		<u-popup :show="showNetworkPopup"  :round="10" mode="bottom" :closeable="false" :closeOnClickOverlay="false">
			<view class="network-popup">
				<view class="network-title">网络权限申请</view>
				<view class="network-content">应用需要访问网络权限，请在设置中允许</view>
				<view class="network-btns">
					<view class="network-btn network-btn--cancel" @click="showNetworkPopup = false">取消</view>
					<view class="network-btn network-btn--confirm" @click="goToNetworkSettings">去设置</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: false,
				loadingText: '重新加载',
				
				show: false,
				isRefreshing: false,
				showNetworkPopup: false,
				isNavigating: false,
				networkCallback: null
			}
		},
		// onBackPress(options) {
		// 	// 点击虚拟键或者侧滑的时候触发
		// 	if (options.from === 'backbutton' || options.from === 'navigateBack') {
		// 		return true; // 阻止返回
		// 	}
		// 	return false; // 允许返回（可选，因为默认就是false）
		// },
		methods: {
			async getW() {
				// 先检查网络状态
				try {
					const networkRes = await new Promise((resolve) => {
						uni.getNetworkType({
							success: (res) => resolve(res)
						});
					});
					
					if (networkRes.networkType === 'none') {
						this.showNetworkPopup = true;
						return;
					} else {
						this.showNetworkPopup = false;
					}
					
					if(this.isRefreshing) {
						return;
					}
					
					this.loading = true;
					this.isRefreshing = true;
					
					try {
						const token = uni.getStorageSync('token');
						if(!token) {
							if(this.isNavigating) return;
							this.isNavigating = true;
							
							setTimeout(() => {
								uni.reLaunch({
									url: '/pages/sign/sign',
									complete: () => {
										this.isNavigating = false;
									}
								});
							}, 200);
							return;
						}
						
						const res = await this.http.ajax({
							url: this.http.api.refresh,
							method: 'POST',
							data: {
								refreshToken: token.refreshToken
							}
						});
						
						if(res.code === 0) {
							await uni.setStorageSync('token', res.data);
							this.loading = false;
							uni.navigateBack();
						} else {
							this.showErrorToast();
						}
					} catch(err) {
						console.error('刷新token失败:', err);
						this.showErrorToast();
					} finally {
						this.loading = false;
						this.isRefreshing = false;
					}
				} catch(err) {
					console.error('检查网络状态失败:', err);
					this.showErrorToast();
				}
			},

			// 显示错误提示
			showErrorToast() {
				this.$refs.uToast.show({
					type: 'error',
					icon: false,
					title: '刷新失败',
					message: "网络开小差了,请稍后再试",
					iconUrl: 'https://cdn.uviewui.com/uview/demo/toast/error.png'
				})
				this.loading = false
				this.isRefreshing = false
			},

			// 跳转到网络设置
			goToNetworkSettings() {
				if (uni.getSystemInfoSync().platform === 'ios') {
					const UIApplication = plus.ios.import("UIApplication");
					const application = UIApplication.sharedApplication();
					const NSURL = plus.ios.import("NSURL");
					const setting = NSURL.URLWithString("app-settings:");
					application.openURL(setting);
					plus.ios.deleteObject(setting);
					plus.ios.deleteObject(NSURL);
					plus.ios.deleteObject(application);
				} else {
					var main = plus.android.runtimeMainActivity();
					var Intent = plus.android.importClass('android.content.Intent');
					var Settings = plus.android.importClass('android.provider.Settings');
					var intent = new Intent(Settings.ACTION_SETTINGS);
					main.startActivity(intent);
				}
				this.showNetworkPopup = false;
			}
		},
		onUnload() {
			if(this.timer) {
				clearTimeout(this.timer)
			}
			// 移除网络状态监听
			if(this.networkCallback) {
				uni.offNetworkStatusChange(this.networkCallback);
			}
			this.isNavigating = false;
		},
		onLoad() {
			// 显示网络设置弹窗
			this.showNetworkPopup = true;
			
			// 监听网络状态变化
			this.networkCallback = (res) => {
				if(res.networkType !== 'none') {
					// 网络已连接，关闭弹窗
					this.showNetworkPopup = false;
				}
			};
			
			uni.onNetworkStatusChange(this.networkCallback);
		}
	}
</script>

<style lang="scss" scoped>
	page,
	body {
		height: 100%;
	}

	.network-popup {
		width: 100%;
		padding: 40rpx;
		border-radius: 20rpx;
		background: #fff;
		
		.network-title {
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
			margin-bottom: 30rpx;
		}
		
		.network-content {
			font-size: 28rpx;
			line-height: 1.6;
			color: #666;
			margin-bottom: 40rpx;
			text-align: center;
		}
		
		.network-btns {
			display: flex;
			justify-content: space-between;
			gap: 20rpx;
			
			.network-btn {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border-radius: 40rpx;
				font-size: 28rpx;
				
				&--cancel {
					background: #f5f5f5;
					color: #666;
				}
				
				&--confirm {
					background: #00C1CC;
					color: #fff;
				}
			}
		}
	}
</style>