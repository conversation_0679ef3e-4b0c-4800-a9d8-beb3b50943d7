<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, maximum-scale=1, minimum-scale=1">  <!-- 禁止用户缩放 -->
	<title>广告启动页</title>
	<link rel="stylesheet" href="../css/advertise.css">
	<style>
		body, html {
			margin: 0;
			padding: 0;
			height: 100%;
			width: 100%;
			overflow: hidden; /* 防止整体滚动 */
			position: fixed; /* 固定位置防止iOS滚动 */
		}
		.content {
			position: relative;
			height: 100vh;
			width: 100vw;
			overflow: hidden;
		}
		.con {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			overflow: hidden;
			-webkit-overflow-scrolling: none; /* 禁用iOS滚动 */
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.con video {
			/* display: none; /* 隐藏视频 */ */
			width: 100%;
			height: 100%;
			touch-action: none; /* 禁止所有触摸操作 */
			object-fit: contain; /* 改为contain模式，确保视频完整显示 */
			-webkit-touch-callout: none; /* 禁用长按菜单 */
			-webkit-user-select: none; /* 禁用选择 */
			user-select: none;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			-webkit-transform: translate(-50%, -50%);
		}
		.con img {
			width: 100%;
			height: 100%;
			touch-action: none;
			object-fit: contain;
			-webkit-touch-callout: none;
			-webkit-user-select: none;
			user-select: none;
		}
		.btn {
			-webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
		}
	</style>
	<script type="text/javascript" src="../js/webView.js"></script>
</head>
<body>
	<div class="content">
		<div class="con">
			<video src="../imgs/12.webm" autoplay muted loop playsinline webkit-playsinline></video>
			<!-- <img src="../imgs/output.png" alt="广告启动页"> -->
		</div>
		<div class="btn" id="timer">
			<div id="info">跳过</div>
			<div class="circleProgress_wrapper btn">
				<div class="wrapper right">
					<div class="circleProgress rightcircle"></div>
				</div>
				<div class="wrapper left">
					<div class="circleProgress leftcircle"></div>
				</div>
			</div>
		</div>
	</div>
	</body>
	<script>
		document.addEventListener('UniAppJSBridgeReady', function() {
			// 阻止默认的触摸行为
			document.addEventListener('touchmove', function(e) {
				e.preventDefault();
			}, { passive: false });
			
			document.querySelector('.btn').addEventListener('click', function(e) {
				if (e.isTrusted) {
					plus.webview.currentWebview().close();
				}
			}, { passive: true });
		});
	</script>
</html>
