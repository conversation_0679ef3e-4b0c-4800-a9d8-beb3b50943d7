<template>
	<view>


		<view class="content"></view>
		<u-notice-bar text="不借款不收费,融信与联通共同组建,正规靠谱" bg-color="#e0ebff" color="#397afe" mode="closable"></u-notice-bar>
		<u-steps current="0" active-color="#397afe" class="mt20 mb20">
			<u-steps-item title="身份认证">
			</u-steps-item>
			<u-steps-item title="实名认证"></u-steps-item>
			<u-steps-item title="完善信息"></u-steps-item>
		</u-steps>
		<view style="flex-direction: column;padding-bottom: 120upx;" class="dis-ali jc_cen  ">
			<view style="background-color: #fff;width: 100%;">
				<view
					style="font-size: 30upx;font-weight: 700;margin: 30upx 0 0;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);padding-bottom: 30upx;"
					class="dis-ali">
					<span style="border-left:10upx solid #397afe;padding-left: 20upx;">身份信息</span><u-icon
						name="info-circle" size="17" :bold="false" style="margin-left: 18upx ;"
						@click="show=true"></u-icon>
				</view>
				<view style="height: 90upx;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" class="dis-ali">
					<u-input v-model="name" placeholder="请输入姓名" prefixIcon="phone" style="width:710upx;border:0;"
						prefixIconStyle="font-size: 22px;color: #909399" fontSize='16px'>
						<template slot="prefix">
							<span style="width: 150upx;">姓名</span>
						</template>
						<!-- <template slot="suffix">
										<u-icon name="info-circle" size="17" :bold="true" style="margin-right: 8upx ;" @click="show=true"></u-icon>
										</template> -->
					</u-input>
				</view>
				<view style="height: 90upx;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" class="dis-ali">
					<u-input v-model="number" placeholder="请输入身份证号" prefixIcon="phone" style="width:710upx;border:0;"
						prefixIconStyle="font-size: 22px;color: #909399" fontSize='16px'>
						<template slot="prefix">
							<span style="width: 150upx;">身份证号</span>
						</template>
						<!-- <template slot="suffix">
										<u-icon name="info-circle" size="17" :bold="true" style="margin-right: 8upx ;" @click="show=true"></u-icon>
										</template> -->
					</u-input>
				</view>
				<!-- <view style="height: 100upx;">
					<u-input placeholder="输入密码" prefixIcon="phone" height='50'
						style="width:710upx;border:0; height: 80upx;"
						fontSize='18px' >
						<template slot="prefix">
										<image src="../../static/image/icons/mm.png" mode="widthFix" style="width: 30upx;margin-right: 15upx;"></image>
										</template>
						
						</u-input>
				</view> -->
			</view>




			<view class="dis-ali mt20 mb10" style="justify-content: flex-start;width: 680upx;">
				<u-checkbox-group iconPlacement='left' v-model="btn" placement="column" @change="ChangeIntegral">
					<u-checkbox name="1" shape='circle' size="18">

					</u-checkbox>

				</u-checkbox-group>
				<view style="font-size: 26upx;color: #939393;">
					同意<span style="color: #397afe;"><span @click="goUrl(4)">征信授权、</span><span
							@click="goUrl(5)">资信授权、</span><span @click="goUrl(6)">额度合同、</span><span
							@click="goUrl(7)">非学生承诺函敏感信息授权</span></span><!-- <span style="color: #397afe;">融信生活隐私政策</span> -->
				</view>
			</view>

			<view @click="goUrl(1)"
				style="font-size: 34upx;upx;color: #fff;background-color: #397afe;width: 680upx;height: 100upx;border-radius: 10upx;display: flex;align-items: center;justify-content: center;margin-top: 30upx;">
				下一步
			</view>
			<view style="font-size: 26upx;color: #939393;margin-top: 50upx;">
				身份证不在身边？<span style="color: #397afe;"
					@click="show2=true">点击查看></span><!-- <span style="color: #397afe;">融信生活隐私政策</span> -->
			</view>
			<u-popup :show="show1" mode="center" round='5'>
				<view style="padding: 40upx;width: 600upx;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">关于个人信息处理规则说明</text>
					<view style="line-height: 1.7;text-indent: 2em;margin-top: 20upx;font-size: 28upx;">
						同意/允许融信生活通过语音/短信方式向我推荐我可能感兴趣的内容，包括但不限于:会员权益服务、促销、活动、产品等信息。 如不希望接收上述信息，可联系融信客服处理</view>

					<view @click="show = false" class='btn'>
						我知道了
					</view>

				</view>

			</u-popup>
			<u-popup :show="show" mode="center" round='5'>
				<view style="padding: 40upx;width: 600upx;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">关于个人信息处理规则说明</text>
					<view style="line-height: 1.7;text-indent: 2em;margin-top: 20upx;font-size: 28upx;">
						同意/允许融信生活通过语音/短信方式向我推荐我可能感兴趣的内容，包括但不限于:会员权益服务、促销、活动、产品等信息。 如不希望接收上述信息，可联系融信客服处理</view>
					<view @click="show = false" class='btn'>
						我知道了
					</view>
				</view>

			</u-popup>
			<u-popup :show="show2" mode="bottom" round='5' :closeable='false'>
				<view style="padding: 40upx;width: 100%;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">查看和下载身份证照片</text>
					<view>
						<image src="../../static/image/zfb.jpg" mode="widthFix" style="width: 710upx;"></image>
					</view>
					<view @click="show2 = false" class="btn">
						我知道了
					</view>
				</view>

			</u-popup>









			<view style="width: 640upx;border-radius:20upx;position: fixed;bottom: 0upx;">
				<view class="dis-ali " style="justify-content:center;align-items: center;">
					<image src="../../static/image/bottom.png" mode="widthFix" style="width: 640upx;"></image>
				</view>
					
			
			</view>
		</view>

	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				list1: [
					'../../static/image/20230208195156793b17.png',
					'../../static/image/202302081952019c0ab4.png',
				],
				show: false,
				btn: [],
				show1: false,
				tips: '',
				value: '',
				show2: false,
				name: '',
				number: '',
				sex: '',
			}
		},
		watch: {
			value(newValue, oldValue) {
				// console.log('v-model', newValue);
			}
		},
		onLoad() {
			that = this;
			var detail=uni.getStorageSync('info')
			this.name=detail.realname
			this.number=detail.idcard
			if(detail.idcard_images.length==0){
				uni.showModal({
					title:'提示',
					content:'请先完成完善身份信息',
					showCancel:false,
					success(res) {
						if(res.confirm){
							uni.redirectTo({
								url:'/pages/personal/add'
							})
						}
					}
				})
				
			}
		},
		methods: {
			ChangeIntegral() {

			},
			codeChange(text) {
				this.tips = text;
			},
			change(e) {
				console.log('change', e);
			},
			isIdCard(idCard) {
				// 正则表达式，匹配18位身份证号码
				var regex = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[01])\d{3}(\d|X|x)$/;
				// const bankCardRegex = /^(\d{16}|\d{19})$/;
				return regex.test(idCard);
			},
			isName(idCard) {
				// 正则表达式 真实姓名
				var realNameRegex = /^[\u4e00-\u9fa5]{2,}$/; // 只能包含汉字且至少两个字符
				 
				return realNameRegex.test(idCard);
			},
			goUrl(type) {
				if (type == 1) {
				
					if (!this.name) {
						this.$u.toast('请输入姓名')
						return;
					}
					if (!this.isName(this.name)) {
						this.$u.toast('请输入真实姓名')
						return;
					}
					if (!this.isIdCard(this.number)) {
						this.$u.toast('请输入正确的身份证号')
						return;
					}
					if (this.btn.length == 0) {
						this.$u.toast('阅读并同意相关协议')
						return;
					}
					//获取性别
					if (parseInt(this.number.substr(16, 1)) % 2 == 1) {
						//男
						this.sex = 1;
					} else {
						//女
						this.sex = 2;
					}
					console.log(this.sex)
					that.http.ajax({
						url: that.http.api.memberedit,
						method: 'POST',
						data: {
							realname: that.name,
							idcard: that.number,
							gender: that.sex,
						},
						success(res) {
							if (res.code == 200) {
								uni.showToast({
									title: res.message,
									icon: 'none'
								})
								that.http.info()
								uni.redirectTo({
									url: '/pages/apply/apply_two?name=' + that.name + '&number=' + that.number
								})
								
							} else {
								uni.showToast({
									title: res.message,
									icon: 'none'
								})
							}
						}
					})
				
				}
				if (type == 4) {
					var config = uni.getStorageSync('config')
					uni.navigateTo({
						url: '/pages/detail/detail?id=' + config.agreement_zhengxin
					})
				}
				if (type == 5) {
					var config = uni.getStorageSync('config')
					uni.navigateTo({
						url: '/pages/detail/detail?id=' + config.agreement_zixin
					})
				}
				if (type == 6) {
					var config = uni.getStorageSync('config')
					uni.navigateTo({
						url: '/pages/detail/detail?id=' + config.agreement_edu
					})
				}
				if (type == 7) {
					var config = uni.getStorageSync('config')
					uni.navigateTo({
						url: '/pages/detail/detail?id=' + config.agreement_feixuesheng
					})
				}
			},
			gotoAnli() {

				uni.navigateTo({
					url: '/pages/train/train'
				})
			},


			aa(html) {
				const el = document.createElement('div')
				el.innerHTML = html
				return el.innerText
			},
			gotoDetail(id) {
				uni.navigateTo({
					url: '/pages/main/main?id=' + id
				})
			},
			tabChange(index) {

				this.tabId = index.id
				that.page = 0
				that.list = []
				this.status = 'loadmore'
				this.getlist(index.id)
			}
		}
	}
</script>
<style>
	page,
	body {
		background-color: #f3f3f3;
		/* background-color: #fff; */
		/* height: 100%; */
	}
</style>

<style lang="scss">
	.content {
		// z-index: 1;
		// position: absolute;
		// top: 0;
		// width: 100%;
		// // height: 500upx;
		// background-image: linear-gradient(180deg,#397afe,#f3f3f3);
	}

	.btn {
		font-size: 34upx;
		color: #fff;
		background-color: #397afe;
		width: 500upx;
		height: 100upx;
		border-radius: 10upx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30upx;
	}
</style>