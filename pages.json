{
	"pages": [
		
		{
			"path": "pages/sign/sign",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"app-plus": {
					"softinputMode": "adjustResize" //adjustPan窗体高度不变，但窗体上推、adjustResize屏幕高度=webview窗体高度+软键盘高度
				}
			}
		},
		{
			"path": "pages/investment/index",
			"style": {
				"navigationBarTitleText": "生意港",
				"navigationStyle": "custom",
				"backgroundColorTop": "#00C1CC",
				"app-plus": {
					//配置导航栏
					"scrollIndicator": "none"
				}
			}
		},
		{
		
			"path": "pages/investment/sygindex",
			"style": {
				"navigationBarTitleText": "AI",
				"navigationStyle": "custom",
				"backgroundColorTop": "#00C1CC",
				"app-plus": {
					//配置导航栏
					"scrollIndicator": "none"
				}
			}
		},
		{
			"path": "pages/guide/guide",
			"style": {
				"navigationBarTitleText": "引导页",
				"navigationStyle": "custom",
				"app-plus": {
					"scrollIndicator": "none"
				}
			}
		},
	
		{
			"path": "pages/course/course",
			"style": {
				"navigationBarTitleText": "课程",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"backgroundColorTop": "#00C1CC"
			}
		},

		{
			"path": "pages/setting/setting",
			"style": {
				"navigationBarTitleText": "设置",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/apply/apply",
			"style": {
				"navigationBarTitleText": "身份认证",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/apply/apply_two",
			"style": {
				"navigationBarTitleText": "实名认证",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/apply/apply_three",
			"style": {
				"navigationBarTitleText": "完善信息",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/personal/personal",
			"style": {
				"navigationBarTitleText": "个人信息保护",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/myContract/myContract",
			"style": {
				"navigationBarTitleText": "我的合同",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/bank/bank",
			"style": {
				"navigationBarTitleText": "我的银行卡",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/bank/add",
			"style": {
				"navigationBarTitleText": "添加银行卡",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/personal/identity",
			"style": {
				"navigationBarTitleText": "身份信息",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/personal/contacts",
			"style": {
				"navigationBarTitleText": "我的联系人信息",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/sign/regiter",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/detail/detail",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/loan/loan",
			"style": {
				"navigationBarTitleText": "申请借款",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/loan/loanList",
			"style": {
				"navigationBarTitleText": "借款记录",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/video/video",
			"style": {
				"navigationBarTitleText": "视频材料",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/video/videoTest",
			"style": {
				"navigationBarTitleText": "测试",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/yinsi/yinsi",
			"style": {
				"navigationBarTitleText": "隐私设置",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/yinsi/ysIndex",
			"style": {
				"navigationBarTitleText": "隐私权限",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/yinsi/gexing",
			"style": {
				"navigationBarTitleText": "个性化设置",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/yinsi/tongzhi",
			"style": {
				"navigationBarTitleText": "通知管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/personal/add",
			"style": {
				"navigationBarTitleText": "个人信息完善",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/courseDetail/courseDetail",
			"style": {
				"navigationBarTitleText": "课程详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/growthPlan/growpList",
			"style": {
				"navigationBarTitleText": "计划详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/loading/loading",
			"disableSwipeBack": true, // 禁用侧滑返回
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"animationType": "fade-in", // 页面进入无动画
					"background": "transparent",
					"backgroundColor": "transparent",
					"popGesture": "none", // 关闭IOS屏幕左边滑动关闭当前页面的功能
					"bounce": "none"
				}
			}
		},
		{
			"path": "pages/loading/image-mask",
			"disableSwipeBack": true, // 禁用侧滑返回
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"animationType": "fade-in", // 页面进入无动画
					"background": "transparent",
					"backgroundColor": "rgba(0,0,0,0)",
					"popGesture": "none", // 关闭IOS屏幕左边滑动关闭当前页面的功能
					"bounce": "none",
					"disableSwipeBack": true // 禁止IOS侧滑事件
				}
			}
		},
		{
			"path": "pages/loading/fulture-mask",
			"disableSwipeBack": true, // 禁用侧滑返回
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"animationType": "fade-in", // 页面进入无动画
					"background": "transparent",
					"backgroundColor": "rgba(0,0,0,0)",
					"popGesture": "none", // 关闭IOS屏幕左边滑动关闭当前页面的功能
					"bounce": "none",
					"disableSwipeBack": true // 禁止IOS侧滑事件
				}
			}
		},
		{
			"path": "pages/loading/yinsi-mask",
			"disableSwipeBack": true, // 禁用侧滑返回
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"animationType": "fade-in", // 页面进入无动画
					"background": "transparent",
					"backgroundColor": "rgba(0,0,0,0)",
					"popGesture": "none", // 关闭IOS屏幕左边滑动关闭当前页面的功能
					"bounce": "none",
					"disableSwipeBack": true // 禁止IOS侧滑事件
				}
			}
		},
		{
			"path": "pages/growthPlan/growthPlan",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"bounce": "none"
				}
			}
		},
		{
			"path": "pages/growthPlanhengping/growpList",
			"style": {
				"navigationBarTitleText": "计划详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/growthPlan/groupDetail",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/growthPlanhengping/growthPlan",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"bounce": "none"
				}
			}
		},
		{
			"path": "pages/growthPlanhengping/groupDetail",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/helpList/helpList",
			"style": {
				"navigationBarTitleText": "帮助与客服"
			}
		},
		{
			"path": "pages/systemMessage/systemMessage",
			"style": {
				"navigationBarTitleText": "系统消息",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/systemMessage/notificationMessage",
			"style": {
				"navigationBarTitleText": "消息通知",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/systemMessage/message",
			"style": {
				"navigationBarTitleText": "详情"
			}
		},
		{
			"path": "pages/myCollection/myCollection",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/historicalRecords/historicalRecords",
			"style": {
				"navigationBarTitleText": "最近再看",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/clockIn/clockIn",
			"style": {
				"navigationBarTitleText": "签到中心",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/courseDetail/chapterDetails",
			"style": {
				"navigationBarTitleText": "立春天气",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/404/404",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"disableSwipeBack": true,
				"app-plus": {
					"popGesture": "none", // 关闭IOS屏幕左边滑动关闭当前页面的功能
					"bounce": "none"
				}
			}
		},
		{
			"path": "pages/children/children",
			"style": {
				"navigationBarTitleText": "孩子信息"
			}
		},
		{
			"path": "pages/learningRecord/learningRecord",
			"style": {
				"navigationBarTitleText": "学习记录",
				"navigationStyle": "custom",
				"backgroundColorTop": "#00C1CC"
			}
		},
		{
			"path": "pages/eyeProtection/eyeProtection",
			"style": {
				"navigationBarTitleText": "护眼攻略"
			}
		},
		{
			"path": "pages/search/search",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/courseDetail/course",
			"style": {
				"navigationBarTitleText": "课程",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/activity/activity",
			"style": {
				"navigationBarTitleText": "活动详情",
				"navigationStyle": "custom",
				"disableSwipeBack": true
			}
		},
		{
			"path": "pages/personal/adress",
			"style": {
				"navigationBarTitleText": "收货地址"
			}
		},
		{
			"path": "pages/personal/addAdress",
			"style": {
				"navigationBarTitleText": "收货地址",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/myApply/myApply",
			"style": {
				"navigationBarTitleText": "我的兑换",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/myPlan/myPlan",
			"style": {
				"navigationBarTitleText": "我的成长计划",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/report/report",
			"style": {
				"navigationBarTitleText": "报告",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/report/aireport",
			"style": {
				"navigationBarTitleText": "报告",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/report/machine",
			"style": {
				"navigationBarTitleText": "报告",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/report/ai-guide",
			"style": {
				"navigationBarTitleText": "督学机",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/report/report-webview",
			"style": {
				"navigationBarTitleText": "报告详情"
				// "navigationStyle": "custom"
			}
		},
		{
			"path": "pages/shop/shop",
			"style": {
				"navigationBarTitleText": "勃学好物"
			}
		},
		{
			"path": "pages/shop/index",
			"style": {
				"navigationBarTitleText": "勃学好物",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/shop/detail",
			"style": {
				"navigationBarTitleText": "产品详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/shop/webview",
			"style" :
			{
				"navigationBarTitleText" : "勃学商城",
				"navigationStyle": "custom",
				"disableSwipeBack": true,
				"app-plus": {
					"popGesture": "none", // 关闭iOS侧滑返回功能
					"bounce": "none" // 禁止回弹效果
				}
			}
		},
		{
			"path": "pages/setting/aboutMe",
			"style": {
				"navigationBarTitleText": "关于勃学",
				"navigationStyle": "custom"
			}
		},

		{
			"path": "pages/yinsi/policy",
			"style": {
				"navigationBarTitleText": "相关协议",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/detail/sharedetail",
			"style": {
				"navigationBarTitleText": "分享视频详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/detail/aidetail",
			"style": {
				"navigationBarTitleText": "AI自习室",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/detail/all",
			"style": {
				"navigationBarTitleText": "聚合",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/detail/allbook",
			"style": {
				"navigationBarTitleText": "图书分类",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/exchangeList/exchangeList",
			"style": {
				"navigationBarTitleText": "兑换列表"
				
			}
		},
		{
			"path": "pages/myRead/myRead",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path" : "pages/merchant/merchant",
			"style" : 
			{
				"navigationBarTitleText" : "我是商家",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/management",
			"style" : 
			{
				"navigationBarTitleText" : "客户管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/servicehand",
			"style" : 
			{
				"navigationBarTitleText" : "服务申请",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/serviceDetail",
			"style" : 
			{
				"navigationBarTitleText" : "客户详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/evaluate",
			"style" : 
			{
				"navigationBarTitleText" : "评价",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/evaluateList",
			"style" : 
			{
				"navigationBarTitleText" : "评价",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/chooseService",
			"style" : 
			{
				"navigationBarTitleText" : "选择服务",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/attendance",
			"style" : 
			{
				"navigationBarTitleText" : "考勤管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/memberList",
			"style" : 
			{
				"navigationBarTitleText" : "服务记录",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/cardreplacement",
			"style" : 
			{
				"navigationBarTitleText" : "补卡管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/Custom/list",
			"style" : 
			{
				"navigationBarTitleText" : "自定义服务",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/Custom/add",
			"style" : 
			{
				"navigationBarTitleText" : "添加服务",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/order",
			"style" : 
			{
				"navigationBarTitleText" : "订单管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/customerDetail",
			"style" : 
			{
				"navigationBarTitleText" : "客户详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/myserve/myserve",
			"style" : 
			{
				"navigationBarTitleText" : "我的服务",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/myserve/serveDetail",
			"style" : 
			{
				"navigationBarTitleText" : "服务内容",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/mychilren/mychilren",
			"style" : 
			{
				"navigationBarTitleText" : "我的孩子"
			}
		},
		{
		  "path": "pages/ai/ai",
		  "style": {
		    "navigationBarTitleText": "英语陪练"
		  }
		},
		{
			"path" : "pages/ai/ailist",
			"style" : 
			{
				"navigationBarTitleText" : "ai对话",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/ai/aichat",
			"style" : 
			{
				"navigationBarTitleText" : "ai对话",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/ai/index",
			"style" : 
			{
				"navigationBarTitleText" : "ai对话",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/ai/ainiuceshi",
			"style" : 
			{
				"navigationBarTitleText" : "ai对话",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/ai/project/pzst",
			"style" : 
			{
				"navigationBarTitleText" : "解题有方",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/ai/phonecall",
			"style" : 
			{
				"navigationBarTitleText" : "音视频通话",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/flappy-bird/index",
			"style" : 
			{
				"navigationBarTitleText" : "游戏",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/merchant/systemMessage",
			"style" : 
			{
				"navigationBarTitleText" : "消息提醒",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/exchange/exchange",
			"style" : 
			{
				"navigationBarTitleText" : "兑换中心",
				"navigationStyle": "custom"
			}
		},

		{
			"path" : "pages/live/index",
			"style" : 
			{
				"navigationBarTitleText" : "直播广场",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/live/video",
			"style" : 
			{
				"navigationBarTitleText" : "正在直播",
				"navigationStyle": "custom",
				"disableSwipeBack": true,
				"app-plus": {
					"popGesture": "none", // 关闭iOS侧滑返回功能
					"bounce": "none" // 禁止回弹效果
				}
			}
		},
		{
			"path" : "pages/pdf/pdf",
			"style" : 
			{
				"navigationBarTitleText" : ""
			}
		},
		{
			"path" : "pages/investment/cooperate",
			"style" : 
			{
				"navigationBarTitleText" : "商业合作",
				"navigationStyle": "custom"
			}
		},

		{
			"path" : "pages/investment/video",
			"style" : 
			{
				"navigationBarTitleText" : "招商视频",
				"navigationBarTextStyle": "white",
				"navigationBarBackgroundColor": "#000"
			}
		},
		{
			"path" : "pages/investment/webview",
			"style" : 
			{
				"navigationBarTitleText" : "360全景展示"
			}
		},
		{
			"path" : "pages/investment/news",
			"style" : 
			{
				"navigationBarTitleText" : "新闻资讯"
			}
		},
		{
			"path" : "pages/investment/aiList",
			"style" : 
			{
				"navigationBarTitleText" : "AI直播间"
			}
		},
		{
			"path" : "pages/investment/application",
			"style" : 
			{
				"navigationBarTitleText" : "合作申请表"
			}
		},
		{
			"path" : "pages/investment/account",
			"style" : 
			{
				"navigationBarTitleText" : "合作账号"
			}
		},
		{
			"path" : "pages/investment/deposit",
			"style" : 
			{
				"navigationBarTitleText" : "参会押金"
			}
		},
		{
			"path" : "pages/investment/ailive",
			"style" : 
			{
				"navigationBarTitleText" : "AI直播",
				"navigationStyle": "custom",
				"disableSwipeBack": true,
				"app-plus": {
					"popGesture": "none", // 关闭iOS侧滑返回功能
					"bounce": "none" // 禁止回弹效果
				}
			}
		},
		{
			"path" : "pages/investment/live",
			"style" : 
			{
				"navigationBarTitleText" : "正在直播",
				"navigationStyle": "custom",
				"disableSwipeBack": true,
				"app-plus": {
					"popGesture": "none", // 关闭iOS侧滑返回功能
					"bounce": "none" // 禁止回弹效果
				}
			}
		},
		{
			"path": "pages/investment/lottie",
			"disableSwipeBack": true, // 禁用侧滑返回
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"animationType": "fade-in", // 页面进入无动画
					"background": "transparent",
					"backgroundColor": "rgba(0,0,0,0)",
					"popGesture": "none", // 关闭IOS屏幕左边滑动关闭当前页面的功能
					"bounce": "none",
					"disableSwipeBack": true // 禁止IOS侧滑事件
				}
			}
		},
		{
			"path" : "pages/investment/add-deposit",
			"style" : 
			{
				"navigationBarTitleText" : "申请押金"
			}
		},
		{
			"path" : "pages/investment/deposit-detail",
			"style" : 
			{
				"navigationBarTitleText" : "押金详情"
			}
		},
		{
			"path" : "pages/investment/projectList",
			"style" : 
			{
				"navigationBarTitleText" : "我的项目"
			}
		},
		{
			"path" : "pages/service/profit",
			"style" : 
			{
				"navigationBarTitleText" : "算盈利",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/investment/liveStatistics",
			"style" : 
			{
				"navigationBarTitleText" : "正在直播统计"
			}
		},
		{
			"path" : "pages/investment/historyLiveStatistics",
			"style" : 
			{
				"navigationBarTitleText" : "历史直播统计"
			}
		},
		{
			"path" : "pages/investment/myLive1",
			"style" : 
			{
				"navigationBarTitleText" : "正在直播"
			}
		},
		{
			"path": "pages/live/live-pusher",
			"style": {
				"navigationBarTitleText": "直播推流",
				"navigationStyle": "custom",
				"disableSwipeBack": true,
				"app-plus": {
					"titleNView": false,
					"popGesture": "none",
					"bounce": "none"
				}
			}
		},
		{
			"path" : "pages/hall/hall",
			"style" :
			{
				"navigationBarTitleText" : "大讲堂",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/hall/courseList",
			"style" :
			{
				"navigationBarTitleText" : "课程列表",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/hall/videoPlayer",
			"style" :
			{
				"navigationBarTitleText" : "视频播放",
				"navigationStyle": "custom",
				"app-plus": {
					"orientation": ["portrait-primary", "landscape-primary", "landscape-secondary"],
					"softinputMode": "adjustResize"
				}
			}
		},
		{
			"path" : "pages/hall/webview",
			"style" :
			{
				"navigationBarTitleText" : "视频播放",
				"app-plus": {
					"titleNView": false,
					"subNVues": []
				}
			}
		},

		{
			"path" : "pages/setting/complaints",
			"style" : 
			{
				"navigationBarTitleText" : "投诉建议",
					"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/investment/text",
			"style" : 
			{
				"navigationBarTitleText" : "详情",
					"navigationStyle": "custom"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "勃学超市",
		"navigationBarBackgroundColor": "#fff",
		"backgroundColor": "#fff",
		"scrollIndicator": "none", // 不显示滚动条
		"app-plus": {
			"scrollIndicator": "none", // 在APP平台都不显示滚动条
			"bounce": "none"
		}
		// "rpxCalcMaxDeviceWidth": 2000 // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
		// "rpxCalcBaseDeviceWidth":2000, // rpx 计算使用的基准设备宽度，设备实际宽度超出 rpx 计算所支持的最大设备宽度时将按基准宽度计算，单位 px，默认值为 375
		// "rpxCalcIncludeWidth": 750 // rpx 计算特殊处理的值，始终按实际的设备宽度计算，单位 rpx，默认值为 750
		// "navigationStyle": "custom"
		// "app-plus": {
		// 					"titleNView": {
		// 						"buttons": [
		// 							{
		// 								"color": "#E8B36F",
		// 								"fontSize": "30rpx",
		// 								"text": "语音"
										
		// 							}
		// 						]
		// 					}
		// 				}
	},

	"tabBar": {
		// "custom":true,
		// "list": [{
		// 		"pagePath": "pages/index/index"
		// 	},
		// 	{
		// 		"pagePath": "pages/growthPlan/growthPlan"
		// 	},
		// 	{
		// 		"pagePath": "pages/my/my"
		// 	}
		// ]
		"color": "#bcbcbc",
		"selectedColor": "#f2b42e",
		"borderStyle": "white",
		"backgroundColor": "#FFFFFF",
		"iconWidth": "20px",
		"fontSize": "8px",
		"height": "55px",
		"list": [
			{
				"pagePath": "pages/investment/index",
				"iconPath": "static/syg/index/home.png",
					// #ifdef MP-WEIXIN
					"selectedIconPath": "static/syg/index/home2.png",
					// #endif
					// #ifndef MP-WEIXIN
					"selectedIconPath": "static/syg/index/home2.gif",
					// #endif
				"text": "项目"
			},
			{
				"pagePath": "pages/live/index",
				"iconPath": "static/syg/index/live1.png",
				// #ifdef MP-WEIXIN
				"selectedIconPath": "/static/syg/index/live2.png",
				// #endif
				// #ifndef MP-WEIXIN
				"selectedIconPath": "/static/syg/index/live2.gif",
				// #endif
				"text": "直播"
			},
			
			{
				"pagePath": "pages/hall/hall",
				"iconPath": "/static/syg/index/video2.png",
				// #ifdef MP-WEIXIN
				"selectedIconPath": "/static/syg/index/<EMAIL>",
				// #endif
				// #ifndef MP-WEIXIN
				"selectedIconPath": "static/syg/index/video2.gif",
				// #endif
				"text": "大讲堂"
			},
			{
					"pagePath": "pages/investment/sygindex",
					"iconPath": "/static/syg/index/noai.png",
					// #ifdef MP-WEIXIN
					"selectedIconPath": "/static/syg/index/live2.png",
					// #endif
					// #ifndef MP-WEIXIN
					"selectedIconPath": "/static/syg/index/ai2.gif",
					// #endif
					"text": "AI"
				},
			{
				"pagePath": "pages/my/my",
				"iconPath": "static/syg/index/my.png",
				// #ifdef MP-WEIXIN
				"selectedIconPath": "/static/syg/index/my2.png",
				// #endif
				// #ifndef MP-WEIXIN
				"selectedIconPath": "static/syg/index/my2.gif",
				// #endif
				"text": "我的"
			}
		]
	},
	"uniIdRouter": {}
}