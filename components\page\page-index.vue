<template>
	<view>

		<u-navbar title="" height="0" bgColor="rgba(255,255,255,0.01)" :placeholder='true' left-icon="">
		</u-navbar>
		<view class="content"></view>
		<view style="flex-direction: column;padding-bottom: 120upx;width: 100%;padding:33upx;" class="dis-ali jc_cen ">
			<view class="dis-ali jc_bet" style="width: 100%;">
				<view class="dis-ali ">
					<view>
						<image src="../../static/1cc249a937fde3dc41171d7e9b610ea.png" mode="aspectFill"
							style="width: 84upx;height: 84upx;"></image>
					</view>
					<view class="ml5">
						<view style="color: #fff;font-size: 30upx;">学生名称</view>
						<view style="color: #fff;font-size: 24upx;">5年级</view>
					</view>
				</view>
				<view class="com-fff com-fontsize-26">
					签到
				</view>
			</view>
			<view
				style="margin: 30upx;width: 426rpx;height: 145rpx;border-radius: 30rpx 30rpx 30rpx 30rpx;border: 1rpx solid #FFFFFF;padding: 30upx;color:#fff;font-size: 24upx;align-self: start;line-height:1.5;overflow: auto;">
				我是您的私人智能老师，完成新手任务赢好礼
				我是您的私人智能老师，完成新手任务赢好礼
				我是您的私人智能老师，完成新手任务赢好礼
				我是您的私人智能老师，完成新手任务赢好礼
			</view>

			<view
				style="width: 100%;background-color: #fbfcfd;justify-content: space-around;padding-bottom: 30upx;border-radius: 30upx;flex-wrap: wrap;padding: 30upx 30upx 0 30upx ;"
				class="dis-ali ">

				<view class="dis-ali"
					style="font-size: 26upx;flex-direction: column;width: 120upx;margin-bottom: 30upx;"
					v-for="item in 10">
					<image src="../../static/1cc249a937fde3dc41171d7e9b610ea.png"
						style="width: 76upx;height: 76upx;margin-bottom: 15upx;" mode=""></image>AI学习机
				</view>

			</view>
			<view class="dis-ali jc_bet title_nav" style="">
				<span>近期活动</span>
				<view style="font-size: 24upx;color:#6C6C6C;font-weight: 350;">更多活动</view>
			</view>
			<!-- 近期活动 -->
			<view style="width: 100%;height: 280upx;border-radius: 30upx;overflow: hidden;">
				<image src="../../static/image/20210126173020cb11b0.png" mode="aspectFill"
					style="width: 700upx;height: 280upx;"></image>
			</view>
			<!-- banner -->
			<view class="dis-ali jc_bet title_nav" style="">
				<span>近期直播</span>
				<view style="font-size: 24upx;color:#6C6C6C;font-weight: 350;">更多直播</view>
			</view>
			<!-- 近期直播 -->
			<view
				style="width: 100%;justify-content: space-around;padding-bottom: 30upx;border-radius: 30upx;flex-wrap: wrap;flex-direction:column;"
				class="dis-ali ">

				<view class="dis-ali" style="font-size: 26upx;width: 120upx;margin-bottom: 30upx;width: 100%;"
					v-for="item in 2">
					<view>
						<image src="../../static/image/signBack.png"
							style="width: 220upx;height: 292upx;border-radius: 25upx;" mode="aspectFill"></image>
					</view>
					<view class=" flex-column jc_bet"
						style="display: flex;margin-left: 20upx;height: 280upx;padding: 20upx 0 0;">
						<view class="com-fontsize-32 font-bold">直播标题</view>
						<view class="com-fontsize-24">直播时间：2024/11/20 12:00:00</view>
						<view class="dis-ali">
							<view class="dis-ali" style="font-size: 26upx;flex-direction: column;width: 120upx;"
								v-for="item in 2">
								<image src="../../static/1cc249a937fde3dc41171d7e9b610ea.png"
									style="width: 76upx;height: 76upx;margin-bottom: 15upx;" mode=""></image>AI学习机
							</view>
						</view>

					</view>
				</view>

			</view>
			<!-- 直播列表 -->
			<view class="dis-ali jc_bet title_nav" style="">
				<span>适合你的成长计划</span>
				<view style="font-size: 24upx;color:#6C6C6C;font-weight: 350;">查看全部</view>
			</view>
			<!-- 计划title -->
			<view style="width: 100%;height: 280upx;border-radius: 30upx;overflow: hidden;">
				<image src="../../static/image/20210126173020cb11b0.png" mode="aspectFill"
					style="width: 700upx;height: 280upx;"></image>
			</view>
			<!-- 计划列表 -->
			<view
				style="width: 100%;justify-content: space-around;padding-bottom: 30upx;border-radius: 30upx;flex-wrap: wrap;flex-direction:column;"
				class="dis-ali ">
				<u-tabs :list="list1" @click="changeTab" lineHeight='0' :activeStyle="{
        color: '#303133',
        fontWeight: 'bold',
        transform: 'scale(1.05)'
    }"></u-tabs>
				<view class="dis-ali" style="font-size: 26upx;width: 120upx;margin-bottom: 30upx;width: 100%;"
					v-for="item in 2">
					<view>
						<image src="../../static/image/signBack.png"
							style="width: 175upx;height: 175upx;border-radius: 25upx;" mode="aspectFill"></image>
					</view>
					<view class="dis-ali jc_bet" style="width: 100%;">
						<view class=" flex-column jc_bet" style="display: flex;margin-left: 20upx;height: 175upx;">
							<view class="com-fontsize-32 font-bold">直播标题</view>
							<view class="com-fontsize-24 mb20">趣味学历史</view>
							<view style="border:1upx solid #6C6C6C;" class="dis-ali jc_cen">
								历史故事
							</view>

						</view>
						<view style="width: 140rpx;
									height: 70rpx;
							background: #D8D8D8;
					border-radius: 90rpx 90rpx 90rpx 90rpx;font-size: 24upx;" class="dis-ali jc_cen">
							查看详情
						</view>
					</view>

				</view>

			</view>
			<!-- 学习列表 -->
			<view
				style="display: flex;flex-direction: column;align-items: center;margin: 30upx 0 100upx;line-height: 1.9;"
				@click="goUrl(7)">
				<span style="font-size: 28upx;color: #817f7f;">勃学·让信用不负期待</span>
				<span style="font-size: 24upx;color: #adaaaa;">
					挂牌机构 | 成功率高达90% | 安全备案
				</span>
				<view
					style="font-size: 26upx;border: 1upx solid #a6a4a4;color:#747373;padding: 3upx 25upx;border-radius: 50upx;margin-top: 20upx;">
					了解勃学
				</view>
			</view>
			<!-- </bottom> -->
			 <view>
			        <drag-button
			            :isDock="true"
			            :existTabBar="true"
			            @btnClick="btnClick"
			            @btnTouchstart="btnTouchstart"
			            @btnTouchend="btnTouchend" />
			    </view>
		</view>
	</view>
</template>

<script>
	var that
	import dragButton from "@/components/drag-button/drag-button.vue";
	export default {
		components:{
			dragButton
		},
		data() {
			return {
				login: 0,
				loading: true,
				status: 'loadmore',
				title: 'Hello',
				list1: [{
					name: '大家都在学',
				}, {
					name: '兴趣阅读',
				}, {
					name: '节气文化'
				}, {
					name: '科学实验'
				}],
				list2: [],
				page: 1,
				list: [],
				newlist: {},
				detail: [],
				config: uni.getStorageSync('config')
			}
		},
		onLoad() {
			that = this;
			this.getlist()
		},
		onShow() {
			this.detail = uni.getStorageSync('info')
			this.login = getApp().globalData.login
			console.log(getApp().globalData)
			// console.log(this.$login,uni.getStorageSync('token'))
			if (uni.getStorageSync('token')) {

			}
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		methods: {
			btnClick(e){
				console.log(e)
			},
			btnTouchstart(e){
				console.log(e)
			},
			btnTouchend(e){
				console.log(e)
			},
			changeTab(e) {
				console.log(e)
			},
			goUrl(type) {
				if (type == 1) {
					uni.navigateTo({
						url: '/pages/nowActivities/nowActivities'
					})
				}
				if (type == 2) {
					var detail = uni.getStorageSync('info')

					if (detail.is_auth == 1) {
						uni.navigateTo({
							url: '/pages/apply/apply_three'
						})
						return;
					}

					uni.navigateTo({
						url: '/pages/apply/apply'
					})
				}
				if (type == 7) {
					var a = uni.getStorageSync('config')
					uni.navigateTo({
						url: '/pages/detail/detail?id=' + a.agreement_about + '&type=1'
					})
				}
				if (type == 8) {
					uni.navigateTo({
						url: '/pages/loan/loan'
					})
				}
			},
			gosign() {

				uni.navigateTo({
					url: '/pages/sign/sign'
				})
			},
			getlist(id) {
				this.loading = true
				this.http.ajax({
					url: that.http.api.newslist,
					data: {
						page: that.page,
						type_id: 2
					},
					success(res) {

						if (res.code == 200) {

							that.list = res.data.list
							if (that.list.length == Number(res.data.totalCount)) {

								that.status = 'nomore'
							}
						} else {
							uni.showToast({
								title: res.message,
								icon: 'none'
							})
						}
					}
				})
			},
			gotoDetail(id) {
				uni.navigateTo({
					url: '/pages/detail/detail?id=' + id + "&type=1"
				})
			},
			tabChange(index) {

				this.tabId = index.id
				that.page = 0
				that.list = []
				this.status = 'loadmore'
				this.getlist(index.id)
			}
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #f3f3f3;
		background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 40%;
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>
	.content {}

	.title_nav {
		font-size: 32upx;
		font-weight: 700;
		align-self: start;
		margin-left: 30upx;
		margin: 40upx 0;
		width: 100%;
	}
</style>