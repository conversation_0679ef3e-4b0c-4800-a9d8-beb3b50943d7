<template>
  <view class="container">
    <!-- 替换为u-navbar标题栏 -->
    <u-navbar 
      title="拍照搜题" 
      :auto-back="true" 
      height="50"
      bgColor="linear-gradient(135deg, #4A90E2, #5B6EF9)" 
      titleStyle="color: #fff; font-weight: 600;"
      :placeholder='true'
    >
    </u-navbar>
    
    <!-- 聊天内容区域 -->
      <scroll-view 
        scroll-y="true" 
      class="chat-container"
        :scroll-top="scrollTop"
        :scroll-with-animation="true"
      :style="{ paddingBottom: chatHistory.length > 0 ? '180rpx' : '0' }"
      >
      <!-- 有聊天历史时显示聊天记录 -->
      <view class="chat-list" v-if="chatHistory.length > 0">
          <view 
            class="chat-item" 
            :class="message.role"
            v-for="(message, index) in chatHistory" 
            :key="index"
          >
            <view class="message-wrapper">
            <view class="avatar" v-if="message.role === 'assistant'">
              <image src="/static/science-captain-avatar.png" mode="aspectFill"></image>
              </view>
            <view class="message" :class="{'user-message': message.role === 'user', 'ai-message': message.role === 'assistant'}">
              <!-- 助手消息 -->
                  <template v-if="message.role === 'assistant'">
                    <ua2-markdown 
                      :source="message.content" 
                      :show-line="true"
                    ></ua2-markdown>
                    <view class="typing-indicator" v-if="message.isTyping">
                  <text class="dot"></text>
                  <text class="dot"></text>
                  <text class="dot"></text>
                </view>
                <view class="message-actions" v-if="!message.isTyping">
                  <button class="action-btn" @click="copyMessage(message.content)">
                    <u-icon name="file-text" color="#666" size="20"></u-icon>
                    <text>复制</text>
                  </button>
                  <button class="action-btn" @click="deleteMessage(index)">
                    <u-icon name="trash" color="#666" size="20"></u-icon>
                    <text>删除</text>
                  </button>
                    </view>
                  </template>
              
              <!-- 用户消息 -->
                  <template v-else>
                <view class="user-content">
                  <template v-if="message.isImage">
                    <image :src="message.content" mode="widthFix" class="uploaded-image" @click="previewImage(message.content)"></image>
                    </template>
                  <text v-else>{{message.content}}</text>
                </view>
                <view class="message-actions" v-if="message.canRetry">
                  <button class="action-btn" @click="retryChat(message.content)">
                    <u-icon name="reload" color="#666" size="20"></u-icon>
                    <text>重试</text>
                  </button>
                  <button class="action-btn" @click="deleteMessage(index)">
                    <u-icon name="trash" color="#666" size="20"></u-icon>
                    <text>删除</text>
                  </button>
                </view>
              </template>
                </view>
            <view class="avatar" v-if="message.role === 'user'">
              <image src="/static/user-avatar.png" mode="aspectFill"></image>
            </view>
          </view>
        </view>
        
        <!-- 取消按钮 -->
        <view class="cancel-btn-wrapper" v-if="chatting">
          <button class="btn-stop" @click="cancelChat">
            <u-icon name="close" color="#fff" size="18" style="margin-right: 6rpx;"></u-icon>
            <text>停止响应</text>
          </button>
        </view>
    </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <view class="empty-content">
          <image src="/static/empty-state.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-title">智能拍照搜题</text>
          <text class="empty-desc">拍照或选择图片，AI智能解析答案</text>
          <view class="empty-btns">
            <view class="take-photo-btn" @tap="takePhoto">
              <u-icon name="camera" color="#fff" size="24"></u-icon>
              <text>拍照搜题</text>
        </view>
            <view class="choose-photo-btn" @tap="chooseFromAlbum">
              <u-icon name="photo" color="#fff" size="24"></u-icon>
              <text>相册选择</text>
      </view>
    </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部输入框 -->
    <view class="input-container" v-if="chatHistory.length > 0">
      <view class="input-wrapper">
        <view class="action-btns">
          <view class="action-btn photo-btn" @tap="takePhoto">
            <u-icon name="camera-fill" color="#4A90E2" size="26"></u-icon>
          </view>
          <view class="action-btn album-btn" @tap="chooseFromAlbum">
            <u-icon name="photo-fill" color="#4A90E2" size="26"></u-icon>
          </view>
        </view>
        <u--textarea 
          v-model="userInput" 
          placeholder="输入问题或使用拍照搜题" 
          :disabled="chatting"
          @confirm="startChat"
          @keyboardheightchange="onKeyboardheightchange"  
          class="input-field"
          autoHeight 
        ></u--textarea>
        <button 
          class="send-btn" 
          :disabled="!userInput && !imageUrl || chatting" 
          @tap="startChat"
        >
          <u-icon name="arrow-right" color="#fff" size="20"></u-icon>
        </button>
      </view>
        </view>
    
    <!-- 图片裁剪组件 -->
    <photo-cropper
      ref="photoCropper"
      :aspectRatio="1.5"
      :quality="0.8"
      @success="handleImageSuccess"
      @cancel="handleCancel"
    ></photo-cropper>
	<yhdslCropper ref="yhdslCropper" :quality="1" mode="scale"  @uploadImg="uploadImg" :scale="scale"></yhdslCropper>
  </view>
</template>

<script>
import { extractVideoText, cancelConversation } from '@/utils/coze.js'
import ua2Markdown from '@/uni_modules/ua2-markdown/ua-markdown.vue'
import PhotoCropper from '@/components/photo-cropper/photo-cropper.vue'
import yhdslCropper from '@/components/yhdsl-cropper/yhdsl-cropper.vue';
export default {
  components: {
    ua2Markdown,
    PhotoCropper,
	yhdslCropper
  },
  data() {
    return {
		scale:1,
      userInput: '',
      chatting: false,
      chatHistory: [],
      scrollTop: 0,
      currentChatId: '',
      currentConversationId: '',
      botId: '7480541510627934227', // 科学队长的botId
      imageUrl: '', // 存储上传后的图片URL
      statusBarHeight: 0 // 状态栏高度
    }
  },
  onLoad() {
    // 获取状态栏高度
    this.getStatusBarHeight();
  },
  methods: {
    // 获取状态栏高度
    getStatusBarHeight() {
      // #ifdef APP-PLUS || MP
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 20;
      // #endif
      
    // #ifdef H5
      this.statusBarHeight = 0;
    // #endif
    },
    
    // 打开相机拍照
    takePhoto() {
      // 使用yhdslCropper进行拍照
      this.$refs.yhdslCropper.chooseImage();
    },
    
    // 从相册选择
    chooseFromAlbum() {
      this.$refs.yhdslCropper.chooseImage();
    },
    
    // yhdslCropper裁剪完成回调
    uploadImg(tempFilePath) {
      try {
        // 显示加载中提示
        uni.showLoading({
          title: '正在上传图片...'
        });
        
        console.log('裁剪图片成功:', tempFilePath);
        
        // 上传图片到服务器
        this.http.upload({
          filePath: tempFilePath
        }).then(uploadRes => {
          uni.hideLoading();
          
          if (uploadRes.code === 0) {
            this.imageUrl = uploadRes.data;
            
            // 添加图片消息到聊天历史
            const userMessage = {
              role: 'user',
              content: this.imageUrl,
              isImage: true,
              canRetry: true // 允许重试裁剪后的图片
            };
            this.chatHistory.push(userMessage);
            this.scrollToBottom();
            
            // 自动开始分析图片
            setTimeout(() => {
              this.startChat(true);
            }, 300); // 添加短暂延迟确保UI更新
          } else {
            uni.showToast({
              title: uploadRes.msg || '上传图片失败',
              icon: 'none'
            });
          }
        }).catch(err => {
          uni.hideLoading();
          uni.showToast({
            title: '上传图片失败',
            icon: 'none'
          });
          console.error('上传图片失败:', err);
        });
      } catch (err) {
        uni.hideLoading();
        uni.showToast({
          title: '上传图片失败',
          icon: 'none'
        });
        console.error('上传图片失败:', err);
      }
    },
    
    // 处理图片裁剪成功 (老的方法，保留作为备用)
    async handleImageSuccess(result) {
      try {
        // 显示加载中提示
        uni.showLoading({
          title: '正在上传图片...'
        });
        
        console.log('裁剪图片成功:', result);
        
        // 上传图片到服务器
        const uploadRes = await this.http.upload({
          filePath: result.tempFilePath
        });
        
        uni.hideLoading();
        
        if (uploadRes.code === 0) {
          this.imageUrl = uploadRes.data;
          
          // 添加图片消息到聊天历史
          const userMessage = {
            role: 'user',
            content: this.imageUrl,
            isImage: true,
            canRetry: true // 允许重试裁剪后的图片
          };
          this.chatHistory.push(userMessage);
          this.scrollToBottom();
          
          // 自动开始分析图片
          setTimeout(() => {
            this.startChat(true);
          }, 300); // 添加短暂延迟确保UI更新
        } else {
          uni.showToast({
            title: uploadRes.msg || '上传图片失败',
            icon: 'none'
          });
        }
      } catch (err) {
        uni.hideLoading();
        uni.showToast({
          title: '上传图片失败',
          icon: 'none'
        });
        console.error('上传图片失败:', err);
      }
    },
    
    // 取消图片选择
    handleCancel() {
      console.log('取消选择图片');
    },
    
    // 预览图片
    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    },
    
    // 开始聊天
    async startChat(isImage = false) {
      if ((!this.userInput && !isImage && !this.imageUrl) || this.chatting) return;
      
      this.chatting = true;
      
      // 如果是图片搜题
      const currentInput = isImage ? this.imageUrl : this.userInput;
      
      // 如果不是图片搜题，添加文本消息到聊天历史
      if (!isImage) {
        const userMessage = {
          role: 'user',
          content: currentInput,
          canRetry: false
        };
        this.chatHistory.push(userMessage);
      }
      
      this.userInput = '';
      
      try {
        console.log('开始发送对话请求...');
        
        this.scrollToBottom();
        
        const assistantMessage = {
          role: 'assistant',
          content: '',
          isTyping: true
        };
        this.chatHistory.push(assistantMessage);
        
        console.log('调用AI接口，发送内容:', currentInput);
        
        const { content } = await extractVideoText(
          currentInput,
          this.chatHistory.slice(0, -1),
          ({ chat_id, conversation_id, content }) => {
            console.log('流式返回:', { chat_id, conversation_id, has_content: !!content });
            
            if (chat_id && conversation_id) {
              this.currentChatId = chat_id;
              this.currentConversationId = conversation_id;
            }
            
            if (content) {
              assistantMessage.content = content;
              this.scrollToBottom();
            }
          },
          this.botId
        );
        
        console.log('对话请求完成');
        
        assistantMessage.isTyping = false;
        this.chatting = false;
        this.currentChatId = '';
        this.currentConversationId = '';
        this.imageUrl = ''; // 清空图片URL
        
        this.scrollToBottom();
          } catch(error) {
        console.error('聊天失败:', error);
        
        // 详细记录错误信息
        if (error.response) {
          console.error('API返回错误:', error.response.status, error.response.data);
        }
        
        // 移除正在输入的消息
        if (this.chatHistory.length > 0 && this.chatHistory[this.chatHistory.length - 1].role === 'assistant') {
          this.chatHistory.pop();
        }
        
        // 恢复用户输入
        if (!isImage) {
          this.userInput = currentInput;
        }
        
        // 允许重试
        if (this.chatHistory.length > 0) {
          const lastMessage = this.chatHistory[this.chatHistory.length - 1];
          if (lastMessage.role === 'user') {
            lastMessage.canRetry = true;
          }
        }
        
        // 处理特定错误
        if (error.code === 4015) {
          uni.showModal({
            title: '服务未启用',
            content: '搜题服务暂未启用，请稍后再试',
            showCancel: false
          });
        } else if (error.message !== '对话已取消') {
          // 其他错误显示通用提示
          uni.showModal({
            title: '分析失败',
            content: '题目分析失败，请检查网络或服务器状态后重试',
            showCancel: false
          });
        }
      } finally {
        // 重置状态
        this.chatting = false;
        this.currentChatId = '';
        this.currentConversationId = '';
      }
    },
    
    // 取消对话
    async cancelChat() {
      if (!this.currentChatId || !this.currentConversationId) {
        return;
      }
      
      try {
        const result = await cancelConversation(this.currentChatId, this.currentConversationId);
        if (result === true) {
          if (this.chatHistory.length > 0 && this.chatHistory[this.chatHistory.length - 1].role === 'assistant') {
            this.chatHistory.pop();
          }
          if (this.chatHistory.length > 0) {
            const lastMessage = this.chatHistory[this.chatHistory.length - 1];
            if (lastMessage.role === 'user') {
              lastMessage.canRetry = true;
            }
          }
          
          uni.showToast({
            title: '已取消响应',
            icon: 'none'
          });
        } else {
          if (result.reason === 'STATUS_NOT_CANCELABLE') {
            uni.showToast({
              title: '当前状态不支持取消',
              icon: 'none'
            });
          } else {
            uni.showToast({
              title: '取消失败，请重试',
              icon: 'none'
            });
          }
        }
      } catch(error) {
        console.error('取消失败:', error);
        uni.showToast({
          title: '取消失败，请重试',
          icon: 'none'
        });
      } finally {
        this.chatting = false;
        this.currentChatId = '';
        this.currentConversationId = '';
      }
    },
    
    // 复制消息
    copyMessage(content) {
      uni.setClipboardData({
        data: content,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
        }
      });
    },
    
    // 滚动到底部
    scrollToBottom() {
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select('.chat-list').boundingClientRect(res => {
          if (res) {
            this.scrollTop = res.height;
          }
        }).exec();
      }, 200);
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },

    // 键盘高度变化处理
    onKeyboardheightchange(e) {
      this.scrollToBottom();
    },

    // 重试聊天
    async retryChat(content) {
      if (this.chatting) return;
      
      if (content.startsWith('http')) {
        // 如果是图片URL，重新发送图片
        this.imageUrl = content;
        this.startChat(true);
      } else {
        // 如果是文字，重新发送文字
        this.userInput = content;
        await this.startChat();
      }
    },

    // 删除消息
    deleteMessage(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这条消息吗？',
        success: (res) => {
          if (res.confirm) {
            // 如果是用户消息且下一条是助手回复，则一起删除
            if (this.chatHistory[index].role === 'user' && 
                index + 1 < this.chatHistory.length && 
                this.chatHistory[index + 1].role === 'assistant') {
              this.chatHistory.splice(index, 2);
            } else {
              // 如果是助手消息且上一条是用户提问，则一起删除
              if (this.chatHistory[index].role === 'assistant' && 
                  index > 0 && 
                  this.chatHistory[index - 1].role === 'user') {
                this.chatHistory.splice(index - 1, 2);
              } else {
                this.chatHistory.splice(index, 1);
              }
            }
          }
        }
      });
    }
  }
}
</script>

<style>
page,body{
  height: 100%;
  background-color: #f7f8fc;
}
</style>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f7f8fc;
  position: relative;
}
  
/* 聊天容器 */
.chat-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 聊天列表 */
.chat-list {
  padding: 30rpx;
}
    
/* 聊天项 */
.chat-item {
  margin-bottom: 40rpx;
        
  .message-wrapper {
    display: flex;
    align-items: flex-start;
    position: relative;
  }
          
  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    margin: 0 20rpx;
    flex-shrink: 0;
            
    image {
      width: 100%;
      height: 100%;
    }
  }
          
  .message {
    max-width: 70%;
    position: relative;
            
    &.ai-message {
      background-color: #fff;
      border-radius: 20rpx;
      padding: 24rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
      flex: 1;
      min-width: 200rpx;
              
      .typing-indicator {
        display: flex;
        justify-content: center;
        margin-top: 10rpx;
                
        .dot {
          width: 8rpx;
          height: 8rpx;
          background-color: #5B6EF9;
          border-radius: 50%;
          margin: 0 4rpx;
          animation: typing 1.4s infinite ease-in-out;
          
          &:nth-child(1) {
            animation-delay: 0s;
          }
                  
          &:nth-child(2) {
            animation-delay: 0.2s;
          }
                  
          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }
    }
    
    &.user-message {
      .user-content {
        background: linear-gradient(135deg, #4A90E2, #5B6EF9);
        color: #fff;
        padding: 24rpx;
        border-radius: 20rpx;
        word-break: break-all;
        line-height: 1.5;
        font-size: 28rpx;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        min-width: 100rpx;
        
        .uploaded-image {
          width: 400rpx;
          max-width: 100%;
          border-radius: 8rpx;
          display: block;
          margin: 0;
        }
      }
    }
  }
            
  .message-actions {
    display: flex;
    margin-top: 12rpx;
    justify-content: flex-start;
    gap: 20rpx;
    
    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f0f2f7;
      border-radius: 30rpx;
      padding: 10rpx 20rpx;
      font-size: 24rpx;
      color: #666;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      transition: all 0.2s ease;
                
      &::after {
        border: none;
      }
                
      &:active {
        transform: scale(0.96);
      }
      
      u-icon {
        margin-right: 8rpx;
      }
    }
  }
}

/* 取消按钮 */
.cancel-btn-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
  margin-bottom: 40rpx;
  
  .btn-stop {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff3b30, #ff6b6b);
                  color: #fff;
    font-size: 24rpx;
    padding: 16rpx 30rpx;
    border-radius: 30rpx;
    box-shadow: 0 4rpx 16rpx rgba(255, 59, 48, 0.3);
    transition: all 0.2s ease;
    
    &:active {
      transform: scale(0.96);
    }
    
    &::after {
      border: none;
    }
  }
}

/* 空状态 */
.empty-state {
  height: 100%;
    display: flex;
  flex-direction: column;
  justify-content: center;
    align-items: center;
  
  .empty-content {
      display: flex;
    flex-direction: column;
      align-items: center;
    padding: 60rpx;
    
    .empty-image {
      width: 300rpx;
      height: 300rpx;
      margin-bottom: 40rpx;
    }
    
    .empty-title {
      font-size: 42rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .empty-desc {
      font-size: 28rpx;
        color: #999;
      margin-bottom: 60rpx;
      text-align: center;
    }
    
    .empty-btns {
      display: flex;
      gap: 30rpx;
      
      .take-photo-btn, .choose-photo-btn {
      display: flex;
      align-items: center;
      justify-content: center;
        padding: 24rpx 40rpx;
        border-radius: 40rpx;
        font-size: 28rpx;
        box-shadow: 0 8rpx 16rpx rgba(90, 110, 249, 0.2);
        transition: all 0.2s ease;
      
      &:active {
          transform: scale(0.96);
        }
        
        u-icon {
          margin-right: 10rpx;
        }
      }
      
      .take-photo-btn {
        background: linear-gradient(135deg, #4A90E2, #5B6EF9);
        color: #fff;
      }
      
      .choose-photo-btn {
        background: linear-gradient(135deg, #5B6EF9, #8C69FF);
    color: #fff;
      }
    }
  }
}

/* 输入框容器 */
.input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 2rpx solid #f0f2f7;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom, 0));
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  
  .input-wrapper {
  display: flex;
  align-items: center;
    
    .action-btns {
      display: flex;
      margin-right: 20rpx;
      
      .action-btn {
        width: 70rpx;
        height: 70rpx;
        border-radius: 50%;
        background-color: #f0f2f7;
      display: flex;
      align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        margin-right: 16rpx;
        
        &:active {
          background-color: #e3e5ec;
        }
      }
    }
    
    .input-field {
      flex: 1;
      background-color: #f7f8fc;
      border-radius: 36rpx;
      padding: 20rpx 30rpx;
      font-size: 28rpx;
      color: #333;
      max-height: 180rpx;
      transition: all 0.2s ease;
      
      &:focus {
        background-color: #f0f2f7;
      }
    }
    
    .send-btn {
      width: 70rpx;
      height: 70rpx;
    border-radius: 50%;
      background: linear-gradient(135deg, #4A90E2, #5B6EF9);
    display: flex;
    align-items: center;
    justify-content: center;
      margin-left: 20rpx;
      box-shadow: 0 4rpx 12rpx rgba(90, 110, 249, 0.2);
      transition: all 0.2s ease;
    
    &:active {
        transform: scale(0.96);
      }
      
      &:disabled {
      background: #e0e0e0;
        box-shadow: none;
      }
      
      &::after {
        border: none;
      }
    }
  }
}

@keyframes typing {
  0%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-6rpx);
    opacity: 1;
  }
}

// 调整用户头像和消息的排列位置
.chat-item.user {
  .message-wrapper {
    flex-direction: row-reverse;
  }
  
  .message-actions {
    justify-content: flex-end;
  }
  
  .message.user-message {
    margin-right: 0;
  }
}

.chat-item.assistant {
  .message.ai-message {
    margin-left: 0;
  }
}
</style> 