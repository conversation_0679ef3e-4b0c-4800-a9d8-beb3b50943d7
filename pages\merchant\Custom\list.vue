<template>
	<view>
		<z-paging :show-scrollbar="false" refresher-only  refresher-background='#fffff00' ref="paging" :use-page-scroll='true' @onRefresh="onRefresh">
			<view slot="top">
				<u-navbar
					title="自定义服务"
					:autoBack="true"
					:placeholder="true"
				>
					<view slot="right" class="navbar-right">
						<view class="add-btn" @click="goToAdd">添加</view>
					</view>
				</u-navbar>
			</view>
			
			<template #refresher="{refresherStatus}">
				<custom-refresher :status="refresherStatus" color="#000" />
			</template>
			
			<template #loadingMoreNoMore>
				<custom-nomore />
			</template>
			
			<view v-if="loading">
				<x-skeleton type="waterfall" :loading="true" :configs="{
					gridColumns: 1,
					headHeight: '200rpx',
					textRows: 2,
					gridRows: 8,
					textShow: false
				}">
					<view></view>
				</x-skeleton>
			</view>
			
			<view v-else>
				<view class="service-container">
					<view class="service-card" 
						v-for="(item, index) in serviceList" 
						:key="item.id"
						hover-class="service-card-hover"
						:style="{ backgroundImage: `url(${item.bgImage || '/static/image/customimg.png'})` }"
						@click="showServiceActions(item)"
					>
						<view class="service-content" @click="goToServiceDetail(item)">
							<view class="service-title">{{item.name || '自定义服务名称'}}</view>
							<!-- <view class="service-period">创建时间: {{item.createTime || '2023/10/28'}}</view> -->
						</view>
						<view class="service-action" @click.stop>
							<u-icon name="more-dot-fill" size="24" color="#fff" @click="showServiceActions(item)"></u-icon>
						</view>
					</view>
				</view>
				
				<u-empty
					v-if="!loading && (!serviceList || serviceList.length === 0)"
					text="暂无服务信息"
					mode="list"
					icon="../../static/empty/list.png"
				>
				</u-empty>
			</view>
			
			<u-toast ref="uToast"></u-toast>
			
			
			<!-- 服务操作菜单 -->
			<u-action-sheet 
				:actions="serviceActions" 
				:show="showServiceActionSheet"
				@select="handleServiceAction"
				@close="showServiceActionSheet=false"
				cancel-text="取消"
				border-radius="16"
				safe-area-inset-bottom>
			</u-action-sheet>
		</z-paging>
	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				loading: true,
				serviceList: [], // 服务列表
				childrenList: [], // 孩子列表
				showActionSheet: false, // 控制动作面板显示
				childrenActionList: [], // 动作面板中的孩子列表选项
				currentChildName: '', // 当前选中的孩子名称
				currentService: null, // 当前操作的服务
				serviceActions: [
					// { name: '修改', color: '#2BCBD4', fontSize: '32rpx', subname: '编辑服务信息' ,value:1},
					{ name: '删除', color: '#f56c6c', fontSize: '32rpx', subname: '删除此服务' ,value:2}
				], // 服务操作菜单选项
				showServiceActionSheet: false, // 控制服务操作菜单显示
			}
		},
		onShow() {
			// 页面加载时获取服务列表
			that=this;
			this.getServiceList();
		},
		methods: {
			// 获取服务列表
			getServiceList() {
				this.loading = true;
				this.merhttp.ajax({
					url: this.merhttp.api.simplelist,
					method: 'GET',
					data: {
						offeringType: 2 // 固定参数
					},
					success: (res) => {
						this.loading = false;
						if (res.code === 0) {
							this.serviceList = res.data || [];
							// 处理返回的数据
							this.serviceList.forEach(item => {
								// 使用backgroundMember作为背景图
								item.bgImage = item.backgroundMember ;
								// 服务名称映射
								item.name = item.offeringName || '自定义服务名称';
								// 格式化创建时间
								item.createTime = item.createTime ? this.$u.timeFormat(new Date(item.createTime), 'yyyy/mm/dd') : '暂无日期';
							});
						} else {
							uni.showToast({
								title: res.msg || '获取服务列表失败',
								icon: 'none'
							});
						}
						
						// 完成下拉刷新
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					},
					fail: () => {
						this.loading = false;
						uni.showToast({
							title: '获取服务列表失败',
							icon: 'none'
						});
						
						// 完成下拉刷新
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					},
					complete: () => {
						// 确保在任何情况下都会执行complete
						if (this.loading) {
							this.loading = false;
							if (this.$refs.paging) {
								this.$refs.paging.complete();
							}
						}
					}
				});
			},
			
			// 跳转到添加服务页面
			goToAdd() {
				uni.navigateTo({
					url: `/pages/merchant/Custom/add`
				});
			},
			
			// 编辑服务
			editService(item) {
				uni.navigateTo({
					url: `/pages/merchant/Custom/add?id=${item.id}&mode=edit`
				});
			},
			
			// 跳转到服务详情页
			goToServiceDetail(item) {
				uni.navigateTo({
					url: `/pages/myserve/serviceDetail?id=${item.id}`
				});
			},
			
			
			onRefresh() {
				console.log(123)
				// 调用获取服务列表方法
				this.getServiceList();
				// 不需要在这里调用complete，因为getServiceList内部会处理
			},
			
			// 显示服务操作菜单
			showServiceActions(item) {
				// 阻止事件冒泡到父元素
				uni.hideKeyboard(); // 确保键盘被隐藏，防止事件冒泡问题
				this.currentService = item;
				this.showServiceActionSheet = true;
			},
			
			// 处理服务操作
			handleServiceAction(index) {
				console.log(index,this.currentService)
				if (!this.currentService) return;
				// 根据索引执行不同操作
				switch(index.value) {
					case 1: // 修改
						this.editService(this.currentService);
						break;
					case 2: // 删除
						this.deleteService(this.currentService);
						break;
				}
			},
			
			// 删除服务
			deleteService(item) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该服务吗？',
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: '删除中...'
							});
							
							this.merhttp.ajax({
								url: this.merhttp.api.offeringdelete+'?id='+item.id,
								method: 'DELETE',
								data: {
									id: item.id
								},
								success: (res) => {
									uni.hideLoading();
									if (res.code === 0) {
										uni.showToast({
											title: '删除成功',
											icon: 'success'
										});
										// 从列表中删除
										const index = this.serviceList.findIndex(service => service.id === item.id);
										if (index !== -1) {
											this.serviceList.splice(index, 1);
										}
									} else {
										uni.showToast({
											title: res.msg || '删除失败',
											icon: 'none'
										});
									}
								},
								fail: () => {
									uni.hideLoading();
									uni.showToast({
										title: '删除失败',
										icon: 'none'
									});
								}
							});
						}
					}
				});
			}
		}
	}
</script>

<style>
	page, body {
		background-color: #f3f3f3;
	}
</style>

<style lang="scss" scoped>
	.navbar-right {
		padding-right: 30rpx;
	}
	
	.add-btn {
		font-size: 28rpx;
		color: #2BCBD4;
	}
	
	.service-container {
		width: 100%;
		padding: 30rpx;
	}
	
	.service-card {
		margin-bottom: 30rpx;
		border-radius: 16rpx;
		background-size: 100% 100%;
		width: 100%;
		height: 250rpx;
		padding: 30rpx;
		padding-left: 200rpx;
		box-sizing: border-box;
		color: #000;
		position: relative;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		display: flex;
		justify-content: space-between;
		align-items: center;
		transition: all 0.2s;
		background-size: cover;
	}
	
	.service-card-hover {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.service-content {
		flex: 1;
		margin-left: 30rpx;
	}
	
	.service-title {
		font-size: 32rpx;
		font-weight: bold;
		// margin-bottom: 20rpx;
	}
	
	.service-period {
		font-size: 26rpx;
		opacity: 0.9;
	}
	
	.service-action {
		margin-right: 20rpx;
	}
</style>