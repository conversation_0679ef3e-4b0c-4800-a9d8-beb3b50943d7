<template>
	<view>
		<!-- <u-navbar title="ai英语" height="144"  :placeholder='true' :auto-back="true">
			<view slot="right" class="search-icon" @tap="sendMessage">
				<u-icon name="mic" size="25" color="#333"></u-icon>
			</view>
		</u-navbar> -->
		
		<web-view allow='microphone' src="https://bxcs.boxuehao.cn/ai/" @message="handleMessage"  :update-title='false'
			:webview-styles='obj' ref="webview" @onPostMessage='handleMessage' style=""></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				detail:'',
				obj: {
					top: 0
					// webview的窗口背景色
					// navigationBarBackgroundColor: '#ffffff',
					// 导航栏背景色
					// navigationBarTextStyle: 'black',
					// 导航栏标题颜色，仅支持 black / white
					// navigationBarTitleText: 'ai英语',
					// 导航栏标题文字内容
					// navigationStyle: 'default',
					// 导航栏样式，仅支持 default / custom
				},
				messageQueue: [], // 存储待发送的消息队列
				webviewReady: false // 标记webview是否已准备好
			}
		},
		onLoad(option) {
			console.log('页面加载，接收到option:', option);
			
			try {
				// 确保option存在且有detail属性
				if (option && option.detail) {
					this.detail = JSON.parse(option.detail);
					console.log('解析后的detail数据:', this.detail);
					
					// 将消息加入队列，而不是立即发送
					// this.queueMessage(this.detail);
				} else {
					console.warn('未接收到detail数据或数据格式不正确');
				}
			} catch (error) {
				console.error('解析option.detail时出错:', error);
				uni.showToast({
					title: '数据解析错误',
					icon: 'none'
				});
			}
		},
		// 页面准备就绪
		onReady() {
			console.log('页面准备就绪');
			// 标记webview已准备好
			setTimeout(() => {
				this.webviewReady = true;
				// 处理队列中的消息
				// this.processMessageQueue();
			}, 1000); // 给webview额外的时间加载
		},
		methods: {
			
			onNavigationBarButtonTap(e) {
				console.log("点击导航栏按钮");
				// 也发送detail数据
				this.queueMessage(this.detail);
			},
			// 接收来自网页的消息
			handleMessage(event) {
				console.log('收到网页消息：', event.detail);
				uni.showToast({
					title: '收到网页消息：' + JSON.stringify(event.detail.data),
					icon: 'none',
					duration: 3000
				});
			},

			// 将消息添加到队列
			queueMessage(message) {
				console.log('将消息添加到队列:', message);
				this.messageQueue.push(message);
				
				// 如果webview已准备好，立即处理消息队列
				if (this.webviewReady) {
					this.processMessageQueue();
				}
			},
			
			// 处理消息队列
			processMessageQueue() {
				console.log('处理消息队列, 队列长度:', this.messageQueue.length);
				if (this.messageQueue.length > 0) {
					// 取出队列中的第一条消息
					const message = this.messageQueue.shift();
					// 发送消息
					this.sendMessageToWebview(message);
					
					// 继续处理队列中的下一条消息
					if (this.messageQueue.length > 0) {
						setTimeout(() => {
							this.processMessageQueue();
						}, 100);
					}
				}
			},

			// 向网页发送消息
			sendMessage(detail) {
				// 将消息添加到队列
				this.queueMessage(detail);
			},
			
			// 实际发送消息到webview
			sendMessageToWebview(detail) {
				console.log('实际发送消息到webview:', detail);
				try {
					// 获取当前web-view实例
					const currentWebview = this.$scope.$getAppWebview();
					// 获取web-view对象
					const webviewObject = currentWebview.children()[0];
					
					if (!webviewObject) {
						console.error('webview对象未找到');
						uni.showToast({
							title: 'webview未准备好',
							icon: 'none'
						});
						return;
					}

					// 准备要发送的数据
					const messageData = detail || {
						type: 'go',
						message: '来自UniApp的消息',
						time: new Date().toLocaleString()
					};

					// 使用JSON.stringify处理数据，并确保字符串在JavaScript中正确转义
					const jsonString = JSON.stringify({
						type: 'UniAppMessage',
						data: messageData
					});
					
					// 发送消息到网页 - 使用一种更安全的方式传递JSON
					webviewObject.evalJS(`
						try {
							window.postMessage(
								JSON.parse('${jsonString.replace(/'/g, "\\'")}'),
								'*'
							);
							console.log('UniApp消息已发送到网页', ${JSON.stringify(messageData)});
						} catch(e) {
							console.error('消息发送失败:', e);
						}
					`);

					uni.showToast({
						title: '消息已发送',
						icon: 'success'
					});
				} catch (error) {
					console.error('发送消息到webview时出错:', error);
					uni.showToast({
						title: '发送消息失败',
						icon: 'none'
					});
				}
			},

			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
</style>