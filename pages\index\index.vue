<template>
	<view>
		<!-- #ifdef APP-PLUS -->
		<z-paging :show-scrollbar="false" refresher-background='#fffff00' ref="paging" refresher-only
			@onRefresh="onRefresh" :use-page-scroll='true' style="margin-top: -88upx;">
		<!-- #endif -->
			<!-- #ifdef H5 -->
			<z-paging :show-scrollbar="false" refresher-background='#fffff00' ref="paging" refresher-only
				@onRefresh="onRefresh" :use-page-scroll='true' style="margin-top: -88upx;">
			<!-- #endif -->
				<view slot="top">
					<view class="index" v-if="!showPrivacyPopup">
						<wu-app-update></wu-app-update>
					</view>
					<u-navbar :title="topNavStyle.title" :height="topNavStyle.height" :titleStyle='topNavStyle.Tstyle'
						:left-icon-color="topNavStyle.Lstyle" :bgColor="topNavStyle.style" :auto-back="false"
						:placeholder='true' left-icon="" style="z-index: 1;">
					</u-navbar>
					<!-- 	<u-navbar title="" height="0" :bgColor="bgColor" :placeholder='true' left-icon="">
				</u-navbar>
				<my-nav title='勃学超市' :pageScrollTop='pageScrollTop'></my-nav> -->
					<!-- 头部nav -->
				</view>
				<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
				<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->

				<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

				<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
				<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
				<template #refresher="{refresherStatus}">
					<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
					<custom-refresher :status="refresherStatus" />
				</template>
				<!-- 自定义没有更多数据view -->
				<template #loadingMoreNoMore>
					<!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
					<custom-nomore />
				</template>
				<view v-if="loading" @click="goUrl(99)">
					<x-skeleton type="banner" :loading="true">
						<view></view>
					</x-skeleton>
					<x-skeleton type="menu" :loading="true">
						<view></view>
					</x-skeleton>
					<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 4,
						headHeight: '200rpx',
						textRows: 1,
						gridRows:1
					}">
						<view></view>
					</x-skeleton>
					<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 1,
						headHeight: '200rpx',
						textRows: 1,
						gridRows:3,
						textShow:false
					}">
						<view></view>
					</x-skeleton>
					<x-skeleton type="list" :loading="true">
						<view></view>
					</x-skeleton>
				</view>
				<view v-if='!loading'>
					<view class="content">
						<view class="user-info">
							<view class="user-info__left dis-ali" v-if="childList.childName">
								<view>
									<image class="user-info__avatar" :src="childList.headPortrait" mode="aspectFill">
									</image>
								</view>
								<view class="user-info__text ml5">
									<view class="user-info__text--name">{{childList.childName}}</view>
									<view class="user-info__text--grade">{{childList.gradeTitle}}</view>
								</view>
							</view>
							<view v-else></view>
							<view class="user-info__sign"
								:class="{'user-info__sign--signed': signInData.todaySignIn, 'animate-sign': !signInData.todaySignIn}"
								@click="goUrl(4)">
								{{signInData.todaySignIn ? '已签到' : '签到'}}
							</view>
						</view>
						<view style="position: relative;width: 100%;margin-top: 30upx;">
							<view ref="typingText" class="text_box_view" :class="{'has-new-report': hasNewReport}"
								@click="hasNewReport && goToReports()">
								{{ displayedText }}
								<text v-if="hasNewReport && typingComplete" class="report-hint">【您有一份新的学习报告，点击查看】</text>
							</view>
							<image class="robot-image animate-robot" src="../../static/kfindex.png" mode="heightFix"
								@click="switchAiContent"></image>
						</view>


						<view class="menu-wrapper">
							<view class="box_tab" style="padding-top: 20upx;">
								<!-- 添加兑换码入口 -->
								<!-- <view class="menu-item exchange-btn" @click="goToExchange">
									<image class="menu-item__icon" src="../../static/keyboard-fill.png" mode=""></image>
									<view class="text-overflow" style="width: 100upx;text-align: center;">
										兑换中心
									</view>
								</view> -->
								<view class="menu-item " v-for="(item,index) in dataList" @click="goUrl(1,item)"
									v-if="more?(index||index==0):index<10">
									<image class="menu-item__icon" :src="item.menuIcon" mode=""></image>
									<view class="text-overflow" style="width: 100upx;text-align: center;">
										{{index==9?more?'收起':item.menuName:item.menuName}}
									</view>
								</view>
								<view class="menu-item--placeholder" v-for="item in 4"></view>
							</view>
						</view>
						<view class="dis-ali mt10 new_view">
							<!-- <view style="position: absolute;top: 30rpx;left: 30rpx;z-index: 9999;">
								<text style="font-size: 30rpx;color: #000;margin-right: 20rpx;">伴学成长</text>
								<text style="font-size: 24rpx;color: #fff;">GROW UP</text>
							</view> -->
							<view class="dis-ali">
								<view class="xueqing_view" style="" @click="goUrl('baogao')">
									学情报告
									<view class="title">查看报告</view>
								</view>
								<view class="dis-ali flex-column"
									style="justify-content: space-between;height: 335upx;margin-left: 20upx;">
									<view class="child_view" style="" @click="goUrl('childList')">
										我的孩子
										<view class="title">管理孩子</view>
									</view>
									<view class="serve_view" @click="goUrl('myserve')">
										我的服务
										<view class="title1">查看服务</view>
									</view>
								</view>
							</view>
							<view class="baogao_view" style="" @click="goUrl('duxue')">
								督学机记录
								<view class="title">查看记录</view>
							</view>
							<view class="duxue_view" @click="goUrl('shangye')">
								商业合作
								<view class="title1">合作洽谈</view>
							</view>
						</view>
						<!-- 门店展示 -->
						<view class="store-display" v-if="childList.childName">
							<view class="store-title">门店展示</view>
							<view class="store-card" @click="goToStore">
								<view class="store-info">
									<view class="store-avatar">
										<image :src="storeData.logo || '../../static/avatar.jpg'" mode="aspectFill">
										</image>
									</view>
									<view class="store-details">
										<view class="store-name">{{storeData.name || '勃学超市'}}</view>
										<view class="store-phone">{{storeData.phone || '暂无联系方式'}}</view>
									</view>
								</view>
								<view class="store-button">进店</view>
							</view>
						</view>

						<!-- 近期活动 -->
						<view class="title_nav" v-if="activityList.length > 0">
							<span>挑战未来</span>
						</view>

						<view class="activity_banner" v-if="activityList.length > 0">
							<u-swiper :list="activityList" keyName="bannerUrl" height="280upx" :autoplay="true"
								:interval="3000" :duration="500" :circular="true" bgColor="transparent" radius="10"
								:indicator="true" indicatorMode="dot" @click="clickSwiper">

							</u-swiper>
						</view>
						<!-- 近期分享 -->
						<view class="title_nav">
							<span>成长进行时</span>
						</view>
						<view class="scroll-list">
							<template v-if="shareList.length > 0">
								<scroll-view :scroll-left='0' :scroll-x="true" class="scroll-view">
									<view class="scroll-content">
										<view class="scroll-item dis-ali" v-for="item in shareList"
											@click="goUrl('share',item.id)">
											<image :src="item.bannerUrl" class="scroll-image" mode="aspectFill"></image>
											<view class="scroll-title">{{item.title}}</view>
										</view>
									</view>
								</scroll-view>
							</template>
						</view>
						<!-- 近期直播 -->
						<!-- <view class="title_nav">
						<span>近期直播</span>
						<view class="title_nav__more">更多直播</view>
					</view> -->

						<!-- <view class="live-list">
						<view class="live-item" v-for="item in 2" @click="goUrl(99)">
							<view>
								<image class="live-item__image" src="../../static/image/signBack.png" mode="aspectFill">
								</image>
							</view>
							<view class="live-item__content">
								<view class="live-item__title">直播标题</view>
								<view class="live-item__time">直播时间：2024/11/20 12:00:00</view>
								<view class="live-item__icons">
									<view class="menu-item" v-for="item in 2">
										<image class="menu-item__icon"
											src="../../static/1cc249a937fde3dc41171d7e9b610ea.png"></image>
										AI学习机
									</view>
								</view>
							</view>
						</view>
					</view> -->

						<!-- 成长计划 -->
						<view class="title_nav">
							<span>适合你的成长计划</span>
							<!-- <view class="title_nav__more">查看全部</view> -->
						</view>
						<template v-if="growthPlanList.length > 0">
							<view class="plan-banner" v-for="(item,index) in growthPlanList" @click='goUrl(7,item.id)'
								v-if="index<2">
								<image :src="item.bannerUrl||''" mode="aspectFill"></image>
							</view>
						</template>
						<!-- 学习列表 -->
						<view v-if="!hide">
							<view class="title_nav" style="margin: 10px ;">
								<span>大家都在看</span>
								<!-- <view class="title_nav__more">查看全部</view> -->
							</view>
							<view class="study_box">

								<!-- 	<u-tabs :list="list1" @click="changeTab" lineHeight='0' :activeStyle="{
								color: '#303133',
								fontWeight: 'bold',
								transform: 'scale(1.05)'
							}"></u-tabs> -->
								<template v-if="courseList && courseList.length > 0">
									<view class="study-item" v-for="(item,index) in courseList" :key="item.id"
										@click="goUrl('goods', item.id)" v-if="showmore?index:index<3">
										<view>
											<image class="study-item__image" :src="item.picUrl" mode="aspectFill">
											</image>
										</view>
										<view class="study-item__content">
											<view class="study-item__title text-overflow">{{item.name}}</view>
											<view class="study-item__subtitle text-line-overflow">{{item.introduction}}
											</view>
											<view class="study-item__tag">{{item.categoryName}}</view>
										</view>
										<view class="study-item__detail">查看详情</view>
									</view>
									<view @click="changeMore"
										style="display: flex;align-items: center;justify-content: center;color: rgb(129, 127, 127);font-size: 26upx;margin-top: 20upx;">
										{{showmore?'收起':'查看更多'}}
									</view>
								</template>
								<view v-else>
									<view style="padding: 40rpx 0;">
										<u-empty text='课程正在路上~' mode="list"
											icon="../../static/empty/list.png"></u-empty>
									</view>
								</view>
							</view>
						</view>
						<!-- 组件部分 -->
						<u-toast ref="uToast"></u-toast>
						<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
							:loading="false"></u-loading-page>
						<my-bottom></my-bottom>
					</view>
				</view>
			</z-paging>

			<view style="position: fixed; z-index: 1; pointer-events: none;">
				<drag-button v-if="!loading" :isDock="true" :existTabBar="true" @btnClick="btnClick"
					@btnTouchstart="btnTouchstart" @btnTouchend="btnTouchend" style="pointer-events: auto;" />
			</view>
			<!-- 机器人 -->
			<no-child-tip />
			<image-mask ref="imageMask" />

			<yinsi-mask :show="showPrivacyPopup" @agree="onAgreePrivacy" @reject="onRejectPrivacy" />

	</view>
</template>

<script>
	var that
	import dragButton from "@/components/drag-button/drag-button.vue";
	import NoChildTip from '@/components/no-child-tip/no-child-tip.vue'
	import YinsiMask from '@/pages/loading/yinsi-mask.vue'
	export default {
		components: {
			dragButton,
			NoChildTip,
			YinsiMask
		},
		computed: {
			topNavStyle() {
				let r = this.scrollTop / 100
				return {
					style: `rgba(255,255,255,${r>=1?1:r})`,
					Tstyle: `color:${r>1?'#000':'#fff'}`,
					Lstyle: r > 1 ? '#000' : '#fff',
					title: r > 1 ? '勃学超市' : '',
					height: r > 1 ? '44' : '44',
				}
			}
		},
		data() {
			return {
				// 页面状态
				loading: true,
				scrollTop: 0,
				bgColor: 'rgba(255,255,255,0.01)',
				// 打字机效果
				fullText: '欢迎来到勃学超市，我是你的学习助手小勃',
				displayedText: '',
				typingInterval: null,
				typingIndex: 0,
				typingComplete: false,
				hasNewReport: false,
				// 列表数据
				dataList: [], // 金刚区菜单
				activityList: [], // 活动列表
				childList: [], // 孩子信息
				growthPlanList: [], // 成长计划
				shareList: [], // 分享列表
				// 课程相关
				list1: [{
					name: '大家都在学',
					id: 0
				}],
				currentCategoryId: 0,
				courseList: [],
				aiview: '',
				currentAiIndex: 0, // 当前显示的数据索引
				more: false,
				showPrivacyPopup: false, // 隐私协议弹窗显示状态
				signInData: {
					todaySignIn: false
				},
				hasNetwork: false, // 网络状态
				showNetworkPopup: false, // 网络设置弹窗显示状态
				showmore: false,
				hide: uni.getStorageSync('hide'),
				storeData: {}, // 门店数据
			}
		},
		onLoad() {
			that = this
			const isAgreePrivacy = uni.getStorageSync('isAgreePrivacy');
			// #ifdef APP-PLUS
			var agree = plus.runtime.isAgreePrivacy();
			console.log(agree)
			if (!agree) {
				this.showPrivacyPopup = true;
				uni.hideTabBar()
			} else {
				this.checkNetworkStatus()
			}
			// #endif
			// #ifdef H5
			if (!isAgreePrivacy) {
				this.showPrivacyPopup = true;
				uni.hideTabBar()
			} else {
				this.checkNetworkStatus()
			}
			// #endif

		},
		// 生命周期方法
		// onLoad() {
		// 	that = this

		// 	const isAgreePrivacy = uni.getStorageSync('isAgreePrivacy');
		// 	// #ifdef APP-PLUS
		// 	var agree = plus.runtime.isAgreePrivacy();
		// 	console.log(agree)
		// 	if (!agree) {
		// 		// #endif
		// 		// #ifdef H5
		// 		if (!isAgreePrivacy) {
		// 			// #endif
		// 			this.showPrivacyPopup = true;
		// 			uni.hideTabBar()
		// 		} else {
		// 			this.checkNetworkStatus()
		// 		}
		// 		}
		// 	},
		onShow() {
			// 恢复打字机效果
			if (this.displayedText && this.displayedText.length > 0 && !this.typingComplete) {
				this.startTypingEffect()
			}
			console.log(getApp().globalData.login)
			if (getApp().globalData.login) {
				this.getchild()
				this.getSignInStatus();
				this.checkNewReports();
				this.getStoreInfo() // 获取门店信息
			}
		},

		onHide() {
			this.clearTypingEffect()
		},

		beforeDestroy() {
			this.clearTypingEffect()
		},

		methods: {
			changeMore() {
				this.showmore = !this.showmore
			},
			// 智能体说话
			speak() {
				console.log('1111')
			},
			// 初始化方法
			initPage() {
				setTimeout(() => {
					this.loading = false
					this.getAI()
				}, 1500)
			},

			initData() {
				this.getHomeMenu()
				this.getRecentActivity()
				this.getGrowthPlan()
				this.getShare()
				this.getGoodClass()
			},
			// 获取智能体
			async getAI() {
				try {
					const res = await that.http.ajax({
						url: that.http.api.agentconfigget,
						method: 'GET',
						data: {
							id: 1
						}
					})
					if (res.code === 0) {
						this.aiview = res.data.content || ''
						this.fullText = this.aiview[this.currentAiIndex]
						this.startTypingEffect()
					}
				} catch (err) {
					console.error('获取成长计划失败:', err)
				}
			},
			// 数据获取方法
			async getHomeMenu() {
				if (!this.hasNetwork) return;
				try {
					const res = await that.http.ajax({
						url: that.http.api.homemenu,
						method: 'GET'
					})
					if (res.code === 0) {
						this.dataList = res.data || []
					}
				} catch (err) {
					console.error('获取金刚区失败:', err)
				}
			},

			async getRecentActivity() {
				if (!this.hasNetwork) return;
				try {
					const res = await that.http.ajax({
						url: that.http.api.recentactivity,
						method: 'GET'
					})
					if (res.code === 0) {
						this.activityList = res.data.list || []
					}
				} catch (err) {
					console.error('获取近期活动失败:', err)
				}
			},

			async getGrowthPlan() {
				try {
					const res = await that.http.ajax({
						url: that.http.api.getTopCourseClassify,
						method: 'GET',
					})
					if (res.code === 0) {
						this.growthPlanList = res.data || []
					}
				} catch (err) {
					console.error('获取成长计划失败:', err)
				}
			},

			async getShare() {
				try {
					const res = await that.http.ajax({
						url: that.http.api.recentshare,
						method: 'GET'
					})
					if (res.code === 0) {
						this.shareList = res.data.list || []
					}
				} catch (err) {
					console.error('获取近期分享失败:', err)
				}
			},

			async getGoodClass() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.categoryhomepage,
						method: 'GET'
					})

					if (res.code === 0) {
						const categories = res.data || []
						this.list1 = [{
								name: '大家都在学',
								id: 0
							},
							...categories.map(item => ({
								name: item.name,
								id: item.id
							}))
						]

						// 默认获取"大家都在学"的数据
						this.getCourseList(0)
					}
				} catch (err) {
					console.error('获取课程分类失败:', err)
				}
			},

			async getCourseList(categoryId) {
				try {
					// 如果是"大家都一起学"分类
					if (categoryId === 0) {
						const res = await this.http.ajax({
							url: this.http.api.spurecommend,
							method: 'GET'
						})

						if (res.code === 0) {
							this.courseList = res.data.list || []
						}
					} else {
						// 其他分类使用原有接口
						const res = await this.http.ajax({
							url: this.http.api.spupage,
							method: 'GET',
							data: {
								categoryId: categoryId,
								pageSize: 10,
								pageNo: 1
							}
						})

						if (res.code === 0) {
							this.courseList = res.data.list || []
						}
					}
				} catch (err) {
					console.error('获取课程列表失败:', err)
				}
			},

			// 检查是否有新报告
			async checkNewReports() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.selectReportPageList,
						method: 'GET',
						data: {
							pageNo: 1,
							pageSize: 1,
							timeType: 1 // 近一周
						}
					})

					if (res.code === 0) {
						let list = [];
						if (Array.isArray(res.data)) {
							list = res.data;
						} else if (res.data && res.data.list) {
							list = res.data.list;
						}

						// 如果有报告，显示提醒
						if (list.length > 0) {
							// 检查是否是今天的新报告
							const today = new Date().toLocaleDateString();
							const reportDate = new Date(list[0].rptTime).toLocaleDateString();

							// 如果是今天的报告，显示提醒
							if (today === reportDate) {
								this.hasNewReport = true;
							}
						}
					}
				} catch (err) {
					console.error('获取报告列表失败:', err)
				}
			},

			// 跳转到报告页面
			goToReports() {
				uni.navigateTo({
					url: '/pages/report/aireport'
				})
			},
			// 打字机效果
			startTypingEffect() {
				this.clearTypingEffect()
				// 如果已经显示完整，不需要再次开始打字效果
				if (this.displayedText === this.fullText) {
					this.typingComplete = true;
					return;
				}

				// 如果已经有部分内容，继续从那里开始
				if (!this.displayedText) {
					this.displayedText = '';
					this.typingIndex = 0;
				}

				this.typingComplete = false;
				this.typingInterval = setInterval(() => {
					if (this.typingIndex < this.fullText.length) {
						this.displayedText += this.fullText[this.typingIndex]
						this.typingIndex++
					} else {
						this.clearTypingEffect()
						this.typingComplete = true;
					}
				}, 100)
			},

			clearTypingEffect() {
				if (this.typingInterval) {
					clearInterval(this.typingInterval)
					this.typingInterval = null
				}
			},

			// 切换AI内容
			switchAiContent() {
				// 清除当前打字效果
				if (this.typingInterval) {
					clearInterval(this.typingInterval)
				}

				// 更新索引
				this.currentAiIndex = (this.currentAiIndex + 1) % this.aiview.length

				// 设置新的文本并开始打字效果
				this.fullText = this.aiview[this.currentAiIndex]
				this.displayedText = ''
				this.typingIndex = 0
				this.typingComplete = false;
				this.startTypingEffect()
			},

			// 事件处理方法
			changeTab(item) {
				this.currentCategoryId = item.id
				this.getCourseList(item.id)
			},

			clickSwiper(index) {
				var a = this.activityList[index].id
				console.log(a)
				this.goUrl('activity', a)
				// uni.navigateTo({
				// 	url: '/pages/activity/activity?id='+a
				// })
			},

			btnClick(e) {
				console.log('按钮点击', e)
				uni.navigateTo({
					url: '/pages/ai/ailist'
				})
			},

			async onRefresh() {
				await this.initData()
				this.$refs.paging.complete()
			},

			// 导航方法
			goUrl(type, value) {
				// uni.navigateTo({
				// 	url: '/pages/investment/lottie?url=https://bxcs.boxuehao.cn/bxcs/hua.json'
				// })
				// return;
				if (getApp().globalData.login && !this.childList.childName && (type != 1)) {
					// #ifdef APP-PLUS
					uni.navigateTo({
						url: '/pages/loading/image-mask'
					})
					// #endif

					// #ifdef H5
					this.$refs.imageMask.showMask()
					// #endif
					return;
				}
				console.log('导航方法', type, value)
				switch (type) {
					case 1: // 金刚区跳转
						let url
						if (value.linkType == 'page') url = `/pages/detail/detail?id=${value.menuCode}&type=1`
						if (value.linkType == 'category' && value.homePageModel == 'single') url =
							`/pages/detail/all?id=${value.id}&title=${value.menuName}`
						if (value.linkType == 'category' && value.homePageModel == 'multi') url =
							`/pages/detail/allbook?id=${value.id}&title=${value.menuName}&code=${value.menuCode}`
						// if (value.url == 'detail') url = '/pages/detail/detail?id='
						// if (value.url == 'aidetail') url = '/pages/detail/aidetail?id='
						// if (value.url == 'more') return this.more = !this.more
						// if (value.url == 'nodetail') url = '/pages/loading/fulture-mask?id='
						uni.navigateTo({
							url: url
						})
						break
					case 4: // 签到
						uni.navigateTo({
							url: '/pages/clockIn/clockIn'
						})
						break
					case 6: // 课程详情
						uni.navigateTo({
							url: `/pages/course/detail?id=${value}`
						})
						break
					case 7: // 成长分类点击
						uni.switchTab({
							url: `/pages/growthPlan/growthPlan?id=${value}`
						})
						break
						break
					case 'share': // 分享
						uni.navigateTo({
							url: `/pages/detail/sharedetail?id=${value}`
						})
						break
					case 'activity': // 活动
						uni.navigateTo({
							url: `/pages/activity/activity?id=${value}`
						})
						break
					case 99: // 骨架屏点击
						return
					case 'leanRecord': // 学习记录
						uni.navigateTo({
							url: '/pages/learn/record'
						})
						break
					case 'goods': // 商品
						uni.navigateTo({
							url: '/pages/loading/fulture-mask'
						})
						break
					case 'childList': // 孩子列表
						uni.navigateTo({
							url: '/pages/mychilren/mychilren'
						})
						break
					case 'myserve': // 孩子服务
						uni.navigateTo({
							url: '/pages/myserve/myserve'
						})
						break
					case 'baogao': // 报告
						uni.navigateTo({
							url: '/pages/report/aireport'
						})
						break
					case 'duxue': // 督学记录
						uni.navigateTo({
							url: '/pages/report/machine'
						})
						break
					case 'shangye': // 商业转化
						uni.navigateTo({
							url: '/pages/investment/cooperate'
						})
						break
				}
			},

			handleNavigation(menuCode) {
				const path = this.getApplyPath(menuCode)
				console.log('跳转路径', path)
				if (path) {
					uni.navigateTo({
						url: path
					})
				}
			},

			getApplyPath(menuCode) {
				const pathMap = {
					'course': '/pages/course/list',
					'growth': '/pages/growth/list',
					'share': '/pages/share/list',
					'learn': '/pages/learn/index',
					'activity': '/pages/activity/list',
					'message': '/pages/message/list',
					'collect': '/pages/user/collect',
					'order': '/pages/order/list',
					'coupon': '/pages/user/coupon',
					'setting': '/pages/setting/index'
				}
				return pathMap[menuCode]
			},

			// 按钮触摸事件
			btnTouchstart(e) {
				console.log('按钮触摸开始', e)
			},

			btnTouchend(e) {
				console.log('按钮触摸结束', e)
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},

			// 获取门店信息
			async getStoreInfo() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.getbymember,
						method: 'GET'
					})
					if (res.code === 0) {
						this.storeData = res.data || {}
					}
				} catch (err) {
					console.error('获取门店信息失败:', err)
				}
			},

			// 跳转到门店详情
			goToStore() {
				if (!this.storeData || !this.storeData.id) {
					uni.showToast({
						title: '门店信息暂未开放',
						icon: 'none'
					})
					return
				}

				uni.navigateTo({
					url: `/pages/detail/aidetail?id=${this.storeData.id}`
				})
			},

			onAgreePrivacy() {
				this.showPrivacyPopup = false;
				this.checkNetworkStatus()

			},

			onRejectPrivacy() {
				this.showPrivacyPopup = false;
				this.checkNetworkStatus()

			},

			getSignInStatus() {
				if (!this.hasNetwork) return;

				this.http.ajax({
					url: this.http.api.recordsummary,
					method: 'GET'
				}).then(res => {
					if (res.code === 0 && res.data) {
						this.signInData = res.data;
					}
				}).catch(err => {
					console.error('获取签到状态失败:', err);
				});
			},

			// 检查网络状态
			checkNetworkStatus() {
				// #ifdef APP-PLUS
				uni.getNetworkType({
					success: (res) => {
						console.log('网络状态:', res.networkType);
						if (res.networkType === 'none') {
							this.hasNetwork = false;
							uni.navigateTo({
								url: '/pages/404/404'
							});
						} else {
							this.hasNetwork = true;
							this.initPage();
							this.initData();
						}
					}
				});

				// 监听网络状态变化
				uni.onNetworkStatusChange((res) => {
					console.log('网络状态变化:', res.networkType);
					if (res.networkType === 'none') {
						this.hasNetwork = false;
						uni.navigateTo({
							url: '/pages/404/404'
						});
					} else {
						if (!this.hasNetwork) {
							uni.showToast({
								title: '网络已连接',
								icon: 'none',
								duration: 2000
							});
							this.hasNetwork = true;
							this.initPage();
							this.initData();
						}
					}
				});
				// #endif

				// #ifndef APP-PLUS
				this.hasNetwork = true;
				this.initPage();
				this.initData();
				// #endif
			},

			// 跳转到兑换页面
			goToExchange() {
				uni.navigateTo({
					url: '/pages/exchange/exchange'
				})
			},

			// 获取孩子信息
			async getchild() {
				try {
					const res = await that.http.ajax({
						url: that.http.api.getChild,
						method: 'GET'
					})
					if (res.code === 0 && res.data.length > 0) {
						// Check if there's at least one item with isCurrent = 1
						const currentChild = res.data.find(child => child.isCurrent === 1);

						if (currentChild) {
							this.childList = currentChild;
						} else {
							this.childList = res.data[0]
						}
					}
				} catch (err) {
					console.error('获取孩子信息失败:', err)
				}
			}
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #f3f3f3;
		background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 60%;
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>
	.content {
		padding: 33rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.user-info {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 50upx;

		&__left {
			display: flex;
			align-items: center;
		}

		&__avatar {
			width: 84rpx;
			height: 84rpx;
			border-radius: 50%;
		}

		&__text {
			color: #fff;
			margin-left: 20rpx;

			&--name {
				font-size: 30rpx;
			}

			&--grade {
				font-size: 24rpx;
			}
		}

		&__sign {
			color: #fff;
			// width: 100upx;
			// height: 100upx;
			// text-align: right;
			font-size: 28rpx;
			z-index: 40;
		}
	}

	.robot-image {
		height: 402rpx;
		position: absolute;
		right: 50rpx;
		top: -50upx;
		// bottom: 100rpx;
		// z-index: 1;
		transform-origin: bottom center;
		animation: float 3s ease-in-out infinite;

		&:active {
			animation: bounce 0.3s ease;
		}
	}

	.menu-item {
		font-size: 24rpx;
		flex-direction: column;
		width: 120rpx;
		display: flex;
		align-items: center;
		margin-top: 25rpx;

		&--placeholder {
			width: 120rpx;
		}

		&__icon {
			width: 76rpx;
			height: 76rpx;
			margin-bottom: 15rpx;
		}
	}

	.live-item {
		width: 100%;
		margin-bottom: 30rpx;
		display: flex;

		&__image {
			width: 220rpx;
			height: 292rpx;
			border-radius: 25rpx;
		}

		&__content {
			flex: 1;
			margin-left: 20rpx;
			height: 280rpx;
			padding: 20rpx 0 0;
			display: flex;
			flex-direction: column;
		}

		&__title {
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 10rpx;
		}

		&__time {
			font-size: 24rpx;
			margin-bottom: 20rpx;
		}

		&__icons {
			display: flex;
			align-items: center;
		}
	}

	.plan-banner {
		margin-bottom: 20upx;
		width: 100%;
		height: 260rpx;
		border-radius: 20rpx;
		overflow: hidden;

		image {
			width: 700rpx;
			height: 260rpx;
		}
	}

	.study-item {
		width: 100%;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;

		&__image {
			width: 175rpx;
			height: 175rpx;
			border-radius: 25rpx;
		}

		&__content {
			padding-bottom: 5upx;
			flex: 1;
			margin-left: 20rpx;
			height: 175rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}

		&__title {
			width: 300upx;
			font-size: 32rpx;
			font-weight: bold;
		}

		&__subtitle {
			font-size: 24rpx;
			width: 290upx;
			margin-bottom: 20rpx;
		}

		&__tag {
			border: 1rpx solid #6C6C6C;
			width: fit-content;
			font-size: 22upx;
			padding: 4rpx 20rpx;
			border-radius: 4rpx;
			text-align: center;
			// margin-bottom: 5upx;
		}

		&__detail {
			width: 140rpx;
			height: 70rpx;
			background: #00C1CC;
			color: #fff;
			border-radius: 90rpx;
			font-size: 24rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.title_nav {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 32rpx;
		font-weight: 700;
		margin: 40rpx 0;

		&__more {
			font-size: 24rpx;
			color: #6C6C6C;
			font-weight: 350;
		}
	}

	.box_tab {
		width: 100%;
		background-color: #fbfcfd;
		display: flex;
		align-items: center;
		justify-content: space-around;
		padding-bottom: 30upx;
		border-radius: 30upx;
		flex-wrap: wrap;
		padding: 30upx 30upx 30upx 30upx;
		position: relative;
		z-index: 0;
	}

	// 标题栏
	.study_box {
		width: 100%;
		justify-content: space-around;
		padding-bottom: 30upx;
		border-radius: 30upx;
		flex-wrap: wrap;
		flex-direction: column;
		background-color: #fff;
		padding: 20upx;
		margin-top: 30upx;
	}

	.activity_banner {
		width: 100%;
		height: 280rpx;
		border-radius: 20rpx;
		overflow: hidden;
		// margin-bottom: 20rpx;

		/deep/ .u-swiper {
			border-radius: 20rpx;
			overflow: hidden;
		}

		/deep/ .u-swiper-image {
			width: 100%;
			height: 100%;
			border-radius: 20rpx;
		}
	}

	.text_box_view {
		margin: 30upx;
		width: 426rpx;
		min-height: 125rpx;
		max-height: 200upx;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		border: 1rpx solid #FFFFFF;
		padding: 20upx;
		color: #fff;
		font-size: 24upx;
		align-self: start;
		line-height: 1.5;
		overflow: auto;
		position: relative;
		transition: all 0.3s ease;

		&.has-new-report {
			border: 2rpx solid #FFD700;
			box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
			animation: borderPulse 2s infinite;
		}
	}

	.report-hint {
		color: #FFD700;
		font-weight: bold;
		display: block;
		margin-top: 10rpx;
		animation: textPulse 1.5s infinite;
	}

	@keyframes borderPulse {
		0% {
			border-color: #FFD700;
			box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.3);
		}

		50% {
			border-color: #FFA500;
			box-shadow: 0 0 15rpx rgba(255, 165, 0, 0.5);
		}

		100% {
			border-color: #FFD700;
			box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.3);
		}
	}

	@keyframes textPulse {
		0% {
			opacity: 0.8;
		}

		50% {
			opacity: 1;
		}

		100% {
			opacity: 0.8;
		}
	}

	.menu-wrapper {
		position: relative;
		width: 100%;
	}

	// .menu-item {
	// 	display: flex;
	// 	align-items: center;
	// 	justify-content: center;
	// 	&--placeholder {
	// 		width: 120rpx;
	// 	}
	// }

	.live-list {
		width: 100%;
		justify-content: space-around;
		border-radius: 30rpx;
		flex-wrap: wrap;
		flex-direction: column;
	}

	.live-item {
		&__icons {
			display: flex;
			align-items: center;
		}
	}

	.study-item {
		margin-top: 20upx;
		margin-bottom: 0;

		&__subtitle {
			font-size: 24rpx;
			margin-bottom: 20rpx;
		}
	}

	.box_list {
		width: 100%;
		background-color: #fff;
		justify-content: space-around;
		padding: 30upx 0;
		border-radius: 20upx;
		flex-wrap: wrap;
		height: 100%;
		// padding: 30upx 30upx 0 30upx;
	}

	.scroll-list {
		background-color: #f5f5f5;
		padding: 0;
		width: 100%;
	}

	.scroll-view {
		width: 100%;
		white-space: nowrap;
	}

	.scroll-content {
		display: inline-flex;
		padding: 0rpx 0rpx;
	}

	.scroll-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-right: 10rpx;

		&:last-child {
			margin-right: 0rpx;
		}
	}

	.scroll-image {
		width: 271rpx;
		height: 359rpx;
		margin-bottom: 15rpx;
		border-radius: 20rpx;
	}

	.scroll-title {
		font-size: 26rpx;
		color: #333;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		width: 220rpx;
	}

	/* 悬浮动画 */
	@keyframes float {

		0%,
		100% {
			transform: translateY(0) rotate(-2deg);
		}

		25% {
			transform: translateY(-5rpx) rotate(2deg);
		}

		50% {
			transform: translateY(0) rotate(-2deg);
		}

		75% {
			transform: translateY(-5rpx) rotate(2deg);
		}
	}

	/* 点击反馈动画 */
	@keyframes bounce {
		0% {
			transform: scale(1);
		}

		50% {
			transform: scale(0.9);
		}

		100% {
			transform: scale(1);
		}
	}

	/* 添加鼠标悬停效果 */
	@media (hover: hover) {
		.robot-image:hover {
			animation: wave 1s ease-in-out;
		}
	}

	/* 摇摆动画 */
	@keyframes wave {

		0%,
		100% {
			transform: rotate(0deg);
		}

		25% {
			transform: rotate(-2deg);
		}

		75% {
			transform: rotate(2deg);
		}
	}

	.privacy-popup {
		width: 600rpx;
		padding: 40rpx;
		border-radius: 20rpx;

		.privacy-title {
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
			margin-bottom: 30rpx;
		}

		.privacy-content {
			font-size: 28rpx;
			line-height: 1.6;
			color: #666;
			margin-bottom: 40rpx;

			.privacy-link {
				color: #00C1CC;
				display: inline;
			}
		}

		.privacy-btns {
			display: flex;
			justify-content: space-between;
			gap: 20rpx;

			.privacy-btn {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border-radius: 40rpx;
				font-size: 28rpx;

				&--reject {
					background: #f5f5f5;
					color: #666;
				}

				&--agree {
					background: #00C1CC;
					color: #fff;
				}
			}
		}
	}

	.user-info {
		&__sign {
			&--signed {
				// background: #f5f5f5;
				// color: #999;
			}
		}
	}

	@keyframes pulse {
		0% {
			transform: scale(1);
		}

		50% {
			transform: scale(1.15);
		}

		100% {
			transform: scale(1);
		}
	}

	.animate-sign {
		animation: pulse 2s infinite;

		&:active {
			animation: none;
			transform: scale(0.95);
		}
	}

	.network-popup {
		width: 600rpx;
		padding: 40rpx;
		border-radius: 20rpx;
		background: #fff;

		.network-title {
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
			margin-bottom: 30rpx;
		}

		.network-content {
			font-size: 28rpx;
			line-height: 1.6;
			color: #666;
			margin-bottom: 40rpx;
			text-align: center;
		}

		.network-btns {
			display: flex;
			justify-content: space-between;
			gap: 20rpx;

			.network-btn {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border-radius: 40rpx;
				font-size: 28rpx;

				&--cancel {
					background: #f5f5f5;
					color: #666;
				}

				&--confirm {
					background: #00C1CC;
					color: #fff;
				}
			}
		}
	}

	/* 门店展示样式 */
	.store-display {
		width: 100%;
		height: 300rpx;

		// border-radius: 20rpx;
		overflow: hidden;
		margin-top: 30rpx;
		background-image: url('../../static/image/<EMAIL>');
		background-size: 100% 100%;
		background-position: center;
		padding: 20rpx 40rpx 0;
	}

	.store-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #fff;
		margin-bottom: 40rpx;
		padding-left: 15rpx;
	}

	.store-card {
		background-color: rgba(255, 255, 255, 0.9);
		border-radius: 15rpx;
		padding: 30rpx 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.store-info {
		display: flex;
		align-items: center;
	}

	.store-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		overflow: hidden;
		background-color: #f5f5f5;
		margin-right: 20rpx;
	}

	.store-avatar image {
		width: 100%;
		height: 100%;
	}

	.store-details {
		display: flex;
		flex-direction: column;
	}

	.store-name {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
	}

	.store-phone {
		font-size: 24rpx;
		color: #666;
	}

	.store-button {
		background-color: #00C1CC;
		color: #fff;
		padding: 10rpx 30rpx;
		border-radius: 30rpx;
		font-size: 24rpx;
	}

	.new_view {
		position: relative;
		display: flex;
		flex-wrap: wrap;
		padding: 20upx 0upx 20upx;
		justify-content: space-between;
		align-items: center;
		// background-image: url('../../static/image/my/<EMAIL>');
		background-size: 100% 100%;
		width: 100%;

		// height: 360upx;
		.xueqing_view {
			font-size: 35rpx;
			background-image: url('../../static/image/my/xueqing.png');
			width: 335upx;
			background-size: 100% 100%;
			height: 335upx;
			display: flex;
			flex-direction: column;
			// justify-content: center;
			padding-left: 30upx;
			padding-top: 30upx;

			.title {
				background-color: #FF49AF;
			}
		}

		.child_view {
			font-size: 28rpx;
			background-image: url('../../static/image/my/<EMAIL>');
			width: 335upx;
			background-size: 100% 100%;
			height: 160upx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding-left: 30upx;

			.title {
				background-color: #FF7A04;
			}
		}

		.serve_view {
			font-size: 28rpx;
			background-image: url('../../static/image/my/<EMAIL>');
			width: 335upx;
			background-size: 100% 100%;
			height: 160upx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding-left: 30upx;

			.title1 {
				background-color: #29A58F;
			}
		}

		.baogao_view {
			margin-top: 20upx;
			font-size: 28rpx;
			background-image: url('../../static/image/my/<EMAIL>');
			width: 335upx;
			background-size: 100% 100%;
			height: 160upx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding-left: 30upx;

			.title1 {
				background-color: #C051FF;
			}
		}

		.duxue_view {
			margin-top: 20upx;
			font-size: 28rpx;
			background-image: url('../../static/image/my/<EMAIL>');
			width: 335upx;
			background-size: 100% 100%;
			height: 160upx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding-left: 30upx;

			.title1 {
				background-color: #C051FF;
			}
		}

		.title {
			width: 105rpx;
			height: 30rpx;
			background: #609FFF;
			border-radius: 30rpx 30rpx 30rpx 30rpx;
			font-size: 18rpx;
			color: #FFFFFF;
			text-align: center;
			line-height: 30upx;
			margin-top: 15upx;
		}

		.title1 {
			width: 105rpx;
			height: 30rpx;
			background-color: #FDA131;
			border-radius: 30rpx 30rpx 30rpx 30rpx;
			font-size: 18rpx;
			color: #FFFFFF;
			text-align: center;
			line-height: 30upx;
			margin-top: 15upx;
		}
	}
</style>