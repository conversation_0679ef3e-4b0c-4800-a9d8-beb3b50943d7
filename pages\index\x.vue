<template>
	<view>
		<z-paging :show-scrollbar="false" refresher-background='#fffff00' ref="paging" refresher-only
			@onRefresh="onRefresh" :use-page-scroll='true'>
			<view slot="top">
				<u-navbar title="" height="0" :bgColor="bgColor" :placeholder='true' left-icon="">
				</u-navbar>
				<my-nav title='勃学超市' :pageScrollTop='pageScrollTop'></my-nav>
				<!-- 头部nav -->
			</view>
			<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
			<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->

			<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

			<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
			<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
			<template #refresher="{refresherStatus}">
				<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
				<custom-refresher :status="refresherStatus" />
			</template>
			<!-- 自定义没有更多数据view -->
			<template #loadingMoreNoMore>
				<!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
				<custom-nomore />
			</template>
			<view v-if="loading"  @click="goUrl(99)">
				<x-skeleton type="banner" :loading="true">
					<view></view>
				</x-skeleton>
				<x-skeleton type="menu" :loading="true">
					<view></view>
				</x-skeleton>
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 4,
						headHeight: '200rpx',
						textRows: 1,
						gridRows:1
					}">
					<view></view>
				</x-skeleton>
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 1,
						headHeight: '200rpx',
						textRows: 1,
						gridRows:3,
						textShow:false
					}">
					<view></view>
				</x-skeleton>
				<x-skeleton type="list" :loading="true">
					<view></view>
				</x-skeleton>
			</view>
			<view v-if='!loading'>
				<view class="content"></view>
				<view style="flex-direction: column;padding-bottom: 120upx;width: 100%;padding:33upx;"
					class="dis-ali jc_cen ">
					<view class="dis-ali jc_bet" style="width: 100%;">
						<view class="dis-ali ">
							<view>
								<image src="../../static/1cc249a937fde3dc41171d7e9b610ea.png" mode="aspectFill"
									style="width: 84upx;height: 84upx;"></image>
							</view>
							<view class="ml5">
								<view style="color: #fff;font-size: 30upx;">学生名称</view>
								<view style="color: #fff;font-size: 24upx;">5年级</view>
							</view>
						</view>
						<view class="com-fff com-fontsize-26" style="z-index: 9999;" @click="goUrl(4)">
							签到
						</view>
					</view>
					<view ref="typingText" class="text_box_view" style="">
						{{ displayedText }}
					</view>
					<view style="position: relative;width: 100%;">
						<view style="" class="dis-ali box_tab">
							<view class="dis-ali"
								style="font-size: 26upx;flex-direction: column;width: 120upx;margin-bottom: 30upx;"
								v-for="(item,index) in dataList.type0" @click="goUrl(1,item.menuCode)">
								<image :src="item.menuIcon" style="width: 76upx;height: 76upx;margin-bottom: 15upx;"
									mode=""></image>
								{{item.menuName}}
							</view>
							<view style="width: 120upx;" v-for="item in 4"></view>
						</view>
						<image src="../../static/kfindex.png" mode="heightFix"
							style="height: 402upx;position: absolute;right: 30upx;top: -280upx;z-index: 1;" class="">
						</image>
					</view>

					<view class="dis-ali jc_bet title_nav" style="">
						<span>近期活动</span>
						<view style="font-size: 24upx;color:#6C6C6C;font-weight: 350;">更多活动</view>
					</view>
					<!-- 近期活动 -->
					<view class="mb10 activity_banner" v-for="item in dataList.type2">
						<image :src="item.menuIcon" mode="aspectFill" style="width: 700upx;height: 280upx;"></image>

					</view>
					<!-- banner -->
					<view class="dis-ali jc_bet title_nav" style="">
						<span>近期直播</span>
						<view style="font-size: 24upx;color:#6C6C6C;font-weight: 350;">更多直播</view>
					</view>
					<!-- 近期直播 -->
					<view
						style="width: 100%;justify-content: space-around;border-radius: 30upx;flex-wrap: wrap;flex-direction:column;"
						class="dis-ali ">

						<view class="dis-ali" style="font-size: 26upx;width: 120upx;margin-bottom: 30upx;width: 100%;"
							v-for="item in 2" @click="goUrl(99)">
							<view>
								<image src="../../static/image/signBack.png"
									style="width: 220upx;height: 292upx;border-radius: 25upx;" mode="aspectFill">
								</image>
							</view>
							<view class=" flex-column jc_bet"
								style="display: flex;margin-left: 20upx;height: 280upx;padding: 20upx 0 0;">
								<view class="com-fontsize-32 font-bold">直播标题</view>
								<view class="com-fontsize-24">直播时间：2024/11/20 12:00:00</view>
								<view class="dis-ali">
									<view class="dis-ali" style="font-size: 26upx;flex-direction: column;width: 120upx;"
										v-for="item in 2">
										<image src="../../static/1cc249a937fde3dc41171d7e9b610ea.png"
											style="width: 76upx;height: 76upx;margin-bottom: 15upx;" mode=""></image>
										AI学习机
									</view>
								</view>

							</view>
						</view>

					</view>
					<!-- 直播列表 -->
					<view class="dis-ali jc_bet title_nav" style="">
						<span>适合你的成长计划</span>
						<view style="font-size: 24upx;color:#6C6C6C;font-weight: 350;">查看全部</view>
					</view>
					<!-- 计划title -->
					<view style="width: 100%;height: 280upx;border-radius: 30upx;overflow: hidden;">
						<image src="../../static/image/20210126173020cb11b0.png" mode="aspectFill"
							style="width: 700upx;height: 280upx;"></image>
					</view>
					<!-- 计划列表 -->
					<view class="dis-ali study_box">
						<u-tabs :list="list1" @click="changeTab" lineHeight='0' :activeStyle="{
				    color: '#303133',
				    fontWeight: 'bold',
				    transform: 'scale(1.05)'
				}"></u-tabs>
						<view class="dis-ali" style="font-size: 26upx;width: 120upx;margin-bottom: 30upx;width: 100%;"
							v-for="item in 2" @click="goUrl(6)">
							<view>
								<image src="../../static/image/signBack.png"
									style="width: 175upx;height: 175upx;border-radius: 25upx;" mode="aspectFill">
								</image>
							</view>
							<view class="dis-ali jc_bet" style="width: 100%;">
								<view class=" flex-column jc_bet"
									style="display: flex;margin-left: 20upx;height: 175upx;">
									<view class="com-fontsize-32 font-bold">直播标题</view>
									<view class="com-fontsize-24 mb20">趣味学历史</view>
									<view style="border:1upx solid #6C6C6C;" class="dis-ali jc_cen">
										历史故事
									</view>

								</view>
								<view style="width: 140rpx;
												height: 70rpx;
										background: #D8D8D8;
								border-radius: 90rpx 90rpx 90rpx 90rpx;font-size: 24upx;" class="dis-ali jc_cen">
									查看详情
								</view>
							</view>
						</view>
					</view>
					<!-- 学习列表 -->
					<u-toast ref="uToast"></u-toast>
					<!-- toast -->
					<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
						:loading="false"></u-loading-page>
					<!-- 加载页 -->
					<my-bottom></my-bottom>
					<!-- </bottom> -->
				</view>
			</view>
		</z-paging>

		<view>
			<drag-button :isDock="true" :existTabBar="true" @btnClick="btnClick" @btnTouchstart="btnTouchstart"
				@btnTouchend="btnTouchend" />
		</view>
		<!-- 机器人 -->
	</view>
</template>

<script>
	var that
	import dragButton from "@/components/drag-button/drag-button.vue";
	export default {
		components: {
			dragButton
		},
		data() {
			return {
				currentTab:0,
				loading: true,
				status: 'loadmore',
				list1: [{
					name: '大家都在学',
					value:1
				}, {
					name: '兴趣阅读',
				}, {
					name: '节气文化'
				}, {
					name: '科学实验'
				}],
				page: 1,
				fullText: '向企业提供C端用户拉新、引流、拓客、销售的营销管理工具向企业提供C端用户拉新、引流、拓客、销售的营销管理工具',
				displayedText: '',
				typingInterval: null,
				typingIndex: 0,
				pageScrollTop: 0, // 页面滚动距离
				bgColor: 'rgba(255,255,255,0.01)',
				navtitle: '',
				height: 0,
				dataList: [],
				login: 0
			}
		},
		onLoad() {
			that = this;
					// uni.hideTabBar()
			// this.getHomeMenu()
			// this.getlist()
			setTimeout(() => this.loading = false, 3000)
			// setTimeout(function () {
			// 			console.log('start pulldown');
			// 		}, 1000);
			// 		uni.startPullDownRefresh();
			// this.startTyping();
		},
		onPageScroll(e) {
			// console.log(e)
			this.pageScrollTop = Math.floor(e.scrollTop);
			// this.$refs.paging.updatePageScrollTop(e.scrollTop);
		},
		onReachBottom(e) {
			// console.log(e)
			// this.$refs.paging.pageReachBottom()
		},
		// onPullDownRefresh() {
		// 		console.log('refresh');
		// 		setTimeout(function () {
		// 			uni.stopPullDownRefresh();
		// 		}, 1000);
		// 	},
		onShow() {
			this.getHomeMenu();
			this.startTyping();
		},
		beforeDestroy() {
			if (this.typingInterval) {
				clearInterval(this.typingInterval);
			}
		},
		onHide() {
			// if (!getApp().globalData.login) {
			// 	console.log('hello')
			// 	uni.reLaunch({
			// 		url: '/pages/sign/sign'
			// 	})
			// }
			if (this.typingInterval) {
				clearInterval(this.typingInterval);
			}
		},
		methods: {
			getHomeMenu() {
				that.http.ajax({
					url: that.http.api.homemenu,
					method: 'POST',
					success: (res) => {
						console.log(res)
						if (!res.code) {
							console.log(123)
							return;
						}
						if (res.code == 1000) {
							this.getGoodClass()
							this.loading = false
							let classifiedData = {
								type0: [],
								type2: [],
								type3: []
							};
							// 遍历原始数组，根据type值将对象添加到对应的数组中
							res.data.dtos.forEach(item => {
								if (item.indexMark === 0) {
									classifiedData.type0.push(item);
								} else if (item.indexMark === 2) {
									classifiedData.type2.push(item);
								} else if (item.indexMark === 1) {
									classifiedData.type1.push(item);
								}

							});
							this.dataList = classifiedData
							console.log(this.dataList)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			onRefresh() {
				// 告知z-paging下拉刷新结束，这样才可以开始下一次的下拉刷新
				setTimeout(() => {
					// 1.5秒之后停止刷新动画
					this.$refs.paging.complete();
				}, 1500)
			},
			startTyping() {
				this.typingInterval = setInterval(() => {
					if (this.typingIndex < this.fullText.length) {
						this.displayedText += this.fullText.charAt(this.typingIndex);
						this.typingIndex++;
					} else {
						clearInterval(this.typingInterval);
					}
				}, 100); // 调整这个时间间隔来控制打字速度
			},
			btnClick(e) {
				uni.navigateTo({
					url: '/pages/helpList/helpList'
				})
			},
			btnTouchstart(e) {
				console.log(e)
			},
			btnTouchend(e) {
				console.log(e)
			},
			changeTab(e) {
				console.log(e)
			},
			goUrl(type, value) {
				if (type == 1) {
					uni.navigateTo({
						url: '/pages/detail/detail?id=' + value,
						animationType: 'pop-in',
						animationDuration: 3000
					})
				}
				if (type == 2) {
					var detail = uni.getStorageSync('info')

					if (detail.is_auth == 1) {
						uni.navigateTo({
							url: '/pages/apply/apply_three'
						})
						return;
					}

					uni.navigateTo({
						url: '/pages/apply/apply'
					})
				}
				if (type == 4) {
					uni.navigateTo({
						url: '/pages/clockIn/clockIn'
					})
				}
				if (type == 6) {
					uni.navigateTo({
						url: '/pages/courseDetail/courseDetail'
					})
				}
				if (type == 7) {
					var a = uni.getStorageSync('config')
					uni.navigateTo({
						url: '/pages/detail/detail?id=' + a.agreement_about + '&type=1'
					})
				}
				if (type == 8) {
					uni.navigateTo({
						url: '/pages/loan/loan'
					})
				}
				if (type == 99) {
					uni.navigateTo({
						url: '/pages/detail/detail'
					})
				}
			},
			gosign() {

				uni.navigateTo({
					url: '/pages/sign/sign'
				})
			},
			getGoodClass(id) {
				this.http.ajax({
					url: that.http.api.getCategoryList,
					success:(res)=> {
						if (res.code == 1000) {
							that.list1.concat(res.data.items.items)
							console.log(that.list1)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			gotoDetail(id) {
				uni.navigateTo({
					url: '/pages/detail/detail?id=' + id + "&type=1"
				})
			},
			tabChange(index) {

				this.tabId = index.id
				that.page = 0
				that.list = []
				this.status = 'loadmore'
				this.getlist(index.id)
			}
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #f3f3f3;
		background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 60%;
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>
	.content {}

	.title_nav {
		font-size: 32upx;
		font-weight: 700;
		align-self: start;
		margin-left: 30upx;
		margin: 40upx 0;
		width: 100%;
	}

	.box_tab {
		width: 100%;
		background-color: #fbfcfd;
		justify-content: space-around;
		padding-bottom: 30upx;
		border-radius: 30upx;
		flex-wrap: wrap;
		padding: 30upx 30upx 0 30upx;
		position: relative;
		z-index: 5;
	}

	// 标题栏
	.study_box {
		width: 100%;
		justify-content: space-around;
		padding-bottom: 30upx;
		border-radius: 30upx;
		flex-wrap: wrap;
		flex-direction: column;
		background-color: #fff;
		padding: 20upx;
		margin-top: 30upx;
	}

	.activity_banner {
		width: 100%;
		height: 280upx;
		border-radius: 30upx;
		overflow: hidden;
	}

	.text_box_view {
		margin: 30upx;
		width: 426rpx;
		min-height: 185rpx;
		height: auto;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		border: 1rpx solid #FFFFFF;
		padding: 20upx;
		color: #fff;
		font-size: 24upx;
		align-self: start;
		line-height: 1.5;
		overflow: auto;
	}
</style>