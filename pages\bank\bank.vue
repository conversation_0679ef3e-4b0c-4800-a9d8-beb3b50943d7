<template>
	<view>


		<view class="content"></view>
		
		<view class="dis-ali jc_cen" v-if="list.length==0">
			<image src="../../static/image/icon2/68eb1949de8f4b69e1de9a1fbf2e02c.png" mode="widthFix" style="width: 400upx;"></image>
		</view>
		<view style="flex-direction: column;" class="dis-ali jc_cen" >
			<view style="" >
				<view
					style="font-size: 30upx;font-weight: 700;margin: 30upx 0 0;padding-bottom: 30upx;"
					class="dis-ali">
					<span style="">我的银行卡</span>
				</view>
				<view v-for="(item,index) in list" class="dis-ali" style="justify-content: space-between;background-color: #ff303e;width: 710upx;height: 200upx;border-radius: 10upx;padding: 30upx;color: #fff;line-height: 1.7;margin-bottom: 20upx;">
					<view style="" >
						<view>
							{{item.bank_name}}
						</view>
						<view class="com-fontsize-24" style="color: #e8e8e8;">
							储蓄卡
						</view>
						<view class="com-fontsize-36">
							{{item.account_number}}
						</view>
					</view>
					<view @click="trash(item.id)">
						<u-icon name="trash" color="#fff"></u-icon>
						
					</view>
				</view>
				
				
			</view>




			

			<view @click="goUrl(1)"
				style="font-size: 34upx;upx;color: #fff;background-color: #397afe;width: 710upx;height: 100upx;border-radius: 10upx;display: flex;align-items: center;justify-content: center;margin-top: 30upx;">
				<u-icon name="plus-circle-fill"  color="#fff" size="20" style="margin-top: 5upx;margin-right: 15upx;"></u-icon>
				<span>添加银行卡</span>
				
			</view>
			
			
			
			









			
		</view>

	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				list1: [
					'../../static/image/20230208195156793b17.png',
					'../../static/image/202302081952019c0ab4.png',
				],
				show: false,
				btn: [],
				show1: false,
				tips: '',
				value: '',
				show2: false,
				list:[]
			}
		},
		watch: {
			value(newValue, oldValue) {
				// console.log('v-model', newValue);
			}
		},
		onShow() {
			that = this;
			this.http.ajax({
				url:that.http.api.banklist,
				data:{
					page:1,
					pageSize:100
				},
				success(res){ 
				
					if(res.code==200){
						
						that.list=res.data.list
						if(that.list.length==Number(res.data.totalCount)){
					
							that.status='nomore'
						}
					}else{
						uni.showToast({
							title:res.message,
							icon:'none'
						})
					}
				}
			})
		},
		methods: {
			trash(id){
				
				this.http.ajax({
					url:that.http.api.bankdelete,
					data:{
						id:id
					},
					method:'POST',
					success(res){ 
					
						if(res.code==200){
								that.http.ajax({
									url:that.http.api.banklist,
									data:{
										page:1,
										pageSize:100
									},
									success(res){ 
									
										if(res.code==200){
											
											that.list=res.data.list
											if(that.list.length==Number(res.data.totalCount)){
										
												that.status='nomore'
											}
										}else{
											uni.showToast({
												title:res.message,
												icon:'none'
											})
										}
									}
								})
							
						}else{
							uni.showToast({
								title:res.message,
								icon:'none'
							})
						}
					}
				})
				
			},
			ChangeIntegral() {

			},
			codeChange(text) {
				this.tips = text;
			},
			change(e) {
				console.log('change', e);
			},
			goUrl(type) {
				if (type == 1) {
					uni.navigateTo({
						url: '/pages/bank/add'
					})
				}
			},
		}
	}
</script>
<style>
	page,
	body {
		background-color: #f3f3f3;
		/* background-color: #fff; */
		/* height: 100%; */
	}
</style>

<style lang="scss">
	.content {
		// z-index: 1;
		// position: absolute;
		// top: 0;
		// width: 100%;
		// // height: 500upx;
		// background-image: linear-gradient(180deg,#397afe,#f3f3f3);
	}

	.btn {
		font-size: 34upx;
		color: #fff;
		background-color: #397afe;
		width: 500upx;
		height: 100upx;
		border-radius: 10upx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30upx;
	}
</style>