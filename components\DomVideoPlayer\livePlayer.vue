<!-- eslint-disable -->
<template>
  <view
    class="player-wrapper"
    :id="videoWrapperId"
    :parentId="id"
    :randomNum="randomNum"
    :change:randomNum="domVideoPlayer.randomNumChange"
    :viewportProps="viewportProps"
    :change:viewportProps="domVideoPlayer.viewportChange"
    :videoSrc="videoSrc"
    :change:videoSrc="domVideoPlayer.initVideoPlayer"
    :command="eventCommand"
    :change:command="domVideoPlayer.triggerCommand"
    :func="renderFunc"
    :change:func="domVideoPlayer.triggerFunc"
    :platform="platform"
    :change:platform="domVideoPlayer.platformChange"
  />
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      default: ''
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    loop: {
      type: Boolean,
      default: false
    },
    controls: {
      type: Boolean,
      default: false
    },
    objectFit: {
      type: String,
      default: 'contain'
    },
    muted: {
      type: <PERSON><PERSON>an,
      default: false
    },
    playbackRate: {
      type: Number,
      default: 1
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    poster: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      randomNum: Math.floor(Math.random() * 100000000),
      videoSrc: '',
      // 父组件向子组件传递的事件指令（video的原生事件）
      eventCommand: null,
      // 父组件传递过来的，对 renderjs 层的函数执行（对视频控制的自定义事件）
      renderFunc: {
        name: null,
        params: null
      },
      // 提供给父组件进行获取的视频属性
      currentTime: 0,
      duration: 0,
      playing: false,
      // 添加平台信息
      platform: ''
    }
  },
  watch: {
    // 监听视频资源地址更新
    src: {
      handler(val) {
        if (!val) return
        setTimeout(() => {
          this.videoSrc = val
        }, 0)
      },
      immediate: true
    }
  },
  computed: {
    videoWrapperId() {
      return `video-wrapper-${this.randomNum}`
    },
    // 聚合视图层的所有数据变化，传给renderjs的渲染层
    viewportProps() {
      return {
        autoplay: this.autoplay,
        muted: this.muted,
        controls: this.controls,
        loop: this.loop,
        objectFit: this.objectFit,
        poster: this.poster,
        isLoading: this.isLoading,
        playbackRate: this.playbackRate
      }
    }
  },
  mounted() {
    // 获取平台信息
    try {
      const systemInfo = uni.getSystemInfoSync();
      this.platform = systemInfo.platform.toLowerCase();
      console.log('当前平台:', this.platform);
    } catch (e) {
      console.error('获取平台信息失败:', e);
      this.platform = ''; // 默认为空
    }
  },
  // 方法
  methods: {
    // 传递事件指令给父组件
    eventEmit({ event, data }) {
      this.$emit(event, data)
    },
    // 修改view视图层的data数据
    setViewData({ key, value }) {
      key && this.$set(this, key, value)
    },
    // 重置事件指令
    resetEventCommand() {
      this.eventCommand = null
    },
    // 播放指令
    play() {
      this.eventCommand = 'play'
    },
    // 暂停指令
    pause() {
      this.eventCommand = 'pause'
    },
    // 重置自定义函数指令
    resetFunc() {
      this.renderFunc = {
        name: null,
        params: null
      }
    },
    // 自定义函数 - 移除视频
    remove(params) {
      this.renderFunc = {
        name: 'removeHandler',
        params
      }
    },
    // 自定义函数 - 全屏播放
    fullScreen(params) {
      this.renderFunc = {
        name: 'fullScreenHandler',
        params
      }
    },
    // 自定义函数 - 跳转到指定时间点
    toSeek(sec, isDelay = false) {
      this.renderFunc = {
        name: 'toSeekHandler',
        params: { sec, isDelay }
      }
    }
  }
}
</script>


<script module="domVideoPlayer" lang="renderjs">
const PLAYER_ID = 'DOM_VIDEO_PLAYER'
export default {
  data() {
    return {
      num: '',
      videoEl: null,
      loadingEl: null,
      // 延迟生效的函数
      delayFunc: null,
      renderProps: {},
      // 标记是否已经加载了hls.js或flv.js
      hlsJsLoaded: false,
      flvJsLoaded: false,
      // 流媒体对象
      hls: null,
      flvPlayer: null,
      // 添加黑屏检测相关变量
      isStreamEnded: false,
      blackScreenCheckTimer: null,
      lastPlaybackTime: 0,
      stuckCounter: 0,
      noDataTimer: null,
      // 添加平台信息
      platform: ''
    }
  },
  computed: {
    playerId() {
      return `${PLAYER_ID}_${this.num}`
    },
    wrapperId() {
      return `video-wrapper-${this.num}`
    },
    // 是否为iOS设备
    isIOS() {
      return this.platform === 'ios';
    },
    // 是否为Android设备
    isAndroid() {
      return this.platform === 'android';
    }
  },
  methods: {
    // 接收平台信息
    platformChange(platform) {
      this.platform = platform;
      console.log('renderjs接收到平台信息:', platform);
    },
    
    isApple() {
      // 使用传递的平台信息
      return this.isIOS;
    },
    async initVideoPlayer(src) {
      this.delayFunc = null
      await this.$nextTick()
      if (!src) return
      
      // 如果视频元素已存在，则切换视频源
      if (this.videoEl) {
        if (!this.isApple() && this.loadingEl) {
          this.loadingEl.style.display = 'block'
        }
        
        // 销毁现有的流媒体播放器
        this.destroyStreamingPlayers();
        
        // 处理不同视频格式
        this.handleVideoSource(src);
        return
      }

      // 首先创建loading元素，确保它先于视频元素显示
      if (!this.isApple() && this.renderProps.isLoading) {
        this.createLoading()
      }

      // 创建视频元素
      const videoEl = document.createElement('video')
      this.videoEl = videoEl
      
      const { autoplay, muted, controls, loop, playbackRate, objectFit, poster } = this.renderProps
      videoEl.autoplay = autoplay
      videoEl.controls = controls
      videoEl.loop = loop
      videoEl.muted = muted
      videoEl.playbackRate = playbackRate
      videoEl.id = this.playerId
      videoEl.setAttribute('preload', 'auto')
      videoEl.setAttribute('playsinline', true)
      videoEl.setAttribute('webkit-playsinline', true)
      videoEl.setAttribute('controlslist', 'nodownload')
      videoEl.setAttribute('disablePictureInPicture', true)
      videoEl.style.objectFit = objectFit
      poster && (videoEl.poster = poster)
      videoEl.style.width = '100%'
      videoEl.style.height = '100%'

      // 插入视频元素 - 确保视频元素添加到loading元素之前
      const playerWrapper = document.getElementById(this.wrapperId)
      playerWrapper.insertBefore(videoEl, playerWrapper.firstChild)

      // 开始监听视频相关事件
      this.listenVideoEvent()
      
      // 处理视频源
      this.handleVideoSource(src);
    },
    
    // 处理不同格式的视频源
    async handleVideoSource(src) {
      if (!src) return;
      
      // 显示加载中状态
      if (this.loadingEl) {
        this.loadingEl.style.display = 'block'
      }
      
      // 检测视频格式
      if (src.includes('.m3u8')) {
        // HLS 格式
        this.setupHlsPlayer(src);
      } else if (src.includes('.flv')) {
        // FLV 格式
        this.setupFlvPlayer(src);
      } else {
        // 标准格式或其他格式
        this.videoEl.src = src;
        console.log('使用标准视频播放:', src);
      }
    },
    
    // 设置HLS播放器
    async setupHlsPlayer(src) {
      // 检查是否原生支持HLS
      const canPlayHLS = this.videoEl.canPlayType('application/vnd.apple.mpegurl') || 
                         this.videoEl.canPlayType('application/x-mpegURL');
                         
      if (canPlayHLS) {
        // 原生支持HLS
        console.log('使用原生HLS支持播放:', src);
        this.videoEl.src = src;
        
        // 为iOS设备添加特殊的流结束检测
        if (this.isApple()) {
          this.setupIOSStreamEndDetection();
        }
        return;
      }
      
      // 检测是否是荣耀手机 - 可能存在兼容性问题
      // 注意：由于无法识别具体型号，我们将对所有Android设备应用相同的优化
      const isAndroidDevice = this.isAndroid;
      // 记录日志用，但不再作为判断依据
      const isHonorDevice = false;
      
      // 使用hls.js播放HLS
      try {
        if (!window.Hls) {
          if (!this.hlsJsLoaded) {
            // 动态加载hls.js
            console.log('正在加载hls.js...');
            await this.loadScript('https://cdn.jsdelivr.net/npm/hls.js@latest');
            this.hlsJsLoaded = true;
          } else {
            console.log('等待hls.js加载完成...');
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
        
        if (window.Hls && window.Hls.isSupported()) {
          // 销毁现有的HLS实例
          if (this.hls) {
            this.hls.destroy();
          }
          
          console.log('使用hls.js播放:', src);
          
          // 荣耀手机使用更保守的配置
          const hlsConfig = isAndroidDevice ? {
            debug: false,
            enableWorker: false, // 禁用worker避免额外线程问题
            maxBufferLength: 30,  // 降低缓冲长度
            maxMaxBufferLength: 60,
            fragLoadingMaxRetry: 2, // 降低重试次数以更快检测问题
            manifestLoadingMaxRetry: 2,
            levelLoadingMaxRetry: 2,
            startLevel: 0, // 从最低质量开始
            defaultAudioCodec: undefined, // 让浏览器自动选择
            progressive: true // 尝试渐进式加载
          } : {
            debug: false,
            enableWorker: true
          };
          
          this.hls = new window.Hls(hlsConfig);
          this.hls.loadSource(src);
          this.hls.attachMedia(this.videoEl);
          
          this.hls.on(window.Hls.Events.MANIFEST_PARSED, () => {
            if (this.renderProps.autoplay) {
              this.videoEl.play();
            }
            
            // 启动黑屏和流结束检测
            this.setupStreamEndDetection();
          });
          
          // 增强错误处理，特别关注流结束相关错误
          this.hls.on(window.Hls.Events.ERROR, (event, data) => {
            console.log('HLS错误:', data);
            
            // 荣耀手机特殊处理 - 更快识别解码器问题
            if (isHonorDevice && data.type === window.Hls.ErrorTypes.MEDIA_ERROR) {
              console.log('荣耀手机检测到媒体错误，可能是解码器问题');
              // 仅尝试恢复一次，避免卡死循环
              if (this.stuckCounter <= 1) {
                console.log('荣耀手机尝试恢复媒体错误');
                this.hls.recoverMediaError();
                this.stuckCounter++;
              } else {
                console.log('荣耀手机媒体错误恢复失败，可能是格式不兼容');
                this.handleStreamEnded('荣耀手机解码器兼容性问题');
                return;
              }
            }
            
            // 检查是否是流结束相关错误
            if (data.type === window.Hls.ErrorTypes.NETWORK_ERROR && 
                (data.details === window.Hls.ErrorDetails.MANIFEST_LOAD_ERROR || 
                 data.details === window.Hls.ErrorDetails.MANIFEST_LOAD_TIMEOUT || 
                 data.details === window.Hls.ErrorDetails.MANIFEST_PARSING_ERROR || 
                 data.details === window.Hls.ErrorDetails.LEVEL_LOAD_ERROR || 
                 data.details === window.Hls.ErrorDetails.LEVEL_EMPTY_ERROR)) {
              
              console.log('检测到HLS流可能已结束', data.details);
              this.handleStreamEnded('HLS流错误: ' + data.details);
              return;
            }
            
            if (data.fatal) {
              switch(data.type) {
                case window.Hls.ErrorTypes.NETWORK_ERROR:
                  console.log('HLS网络错误，尝试恢复...');
                  this.hls.startLoad();
                  break;
                case window.Hls.ErrorTypes.MEDIA_ERROR:
                  console.log('HLS媒体错误，尝试恢复...');
                  this.hls.recoverMediaError();
                  break;
                default:
                  // 无法恢复的错误
                  this.destroyStreamingPlayers();
                  this.$ownerInstance.callMethod('eventEmit', { event: 'error', data: data });
                  // 可能是流结束
                  this.handleStreamEnded('无法恢复的HLS错误');
                  break;
              }
            }
          });
          
          // 添加流结束检测
          this.hls.on(window.Hls.Events.BUFFER_EOS, () => {
            console.log('HLS缓冲区结束标记');
            this.handleStreamEnded('HLS缓冲区结束');
          });
        } else {
          console.error('HLS不被支持，回退到直接播放');
          this.videoEl.src = src;
          
          // 为iOS设备添加特殊的流结束检测
          if (this.isApple()) {
            this.setupIOSStreamEndDetection();
          }
        }
      } catch (e) {
        console.error('HLS播放器设置失败:', e);
        // 回退到尝试直接播放
        this.videoEl.src = src;
        
        // 为iOS设备添加特殊的流结束检测
        if (this.isApple()) {
          this.setupIOSStreamEndDetection();
        }
      }
    },
    
    // 添加iOS设备的流结束检测
    setupIOSStreamEndDetection() {
      console.log('设置iOS流结束检测');
      
      // 清除现有定时器
      this.clearStreamDetectionTimers();
      
      // 监听特定错误
      const iOSErrorHandler = (e) => {
        console.log('iOS视频错误:', e);
        // iOS上的MEDIA_ERR_NETWORK 和 MEDIA_ERR_SRC_NOT_SUPPORTED 通常表示流结束
        if (e && e.target && e.target.error) {
          console.log('iOS视频错误代码:', e.target.error.code);
          this.handleStreamEnded('iOS视频错误: ' + e.target.error.code);
        }
      };
      this.videoEl.addEventListener('error', iOSErrorHandler);
      
      // 设置卡住检测 - iOS在流结束时通常会卡在最后一帧
      this.blackScreenCheckTimer = setInterval(() => {
        if (!this.videoEl) return;
        
        // 如果正在播放但时间没有变化，可能是卡住了
        if (!this.videoEl.paused) {
          if (this.lastPlaybackTime === this.videoEl.currentTime) {
            this.stuckCounter++;
            console.log('视频可能卡住了，计数:', this.stuckCounter);
            
            // 如果连续5秒没有变化，可能是流结束了
            if (this.stuckCounter >= 5) {
              console.log('检测到视频卡住超过5秒，可能是流结束');
              this.handleStreamEnded('视频卡住');
            }
          } else {
            // 重置计数器
            this.stuckCounter = 0;
            this.lastPlaybackTime = this.videoEl.currentTime;
          }
        }
      }, 1000);
      
      // 设置无数据检测
      this.noDataTimer = setTimeout(() => {
        if (this.videoEl && this.videoEl.readyState <= 2 && !this.videoEl.paused) {
          console.log('视频加载状态低且未暂停，可能是流结束:', this.videoEl.readyState);
          this.handleStreamEnded('视频无数据');
        }
      }, 10000); // 10秒后检查
    },
    
    // 通用流结束检测
    setupStreamEndDetection() {
      // 清除现有定时器
      this.clearStreamDetectionTimers();
      
      // 使用传递的平台信息
      const isAndroidDevice = this.isAndroid;
      // 我们无法确定具体机型，但可以记录日志
      const isHonorDevice = false;
      
      // 安卓设备使用更短的检测间隔和更低的阈值
      const checkInterval = isAndroidDevice ? 800 : 1000; // 毫秒
      const stuckThreshold = isAndroidDevice ? 5 : 10;    // 卡住次数阈值
      
      // 添加卡住检测
      this.blackScreenCheckTimer = setInterval(() => {
        if (!this.videoEl) return;
        
        // 检查视频是否卡住
        if (!this.videoEl.paused && this.lastPlaybackTime === this.videoEl.currentTime) {
          this.stuckCounter++;
          // 只在计数达到一定阈值时才输出日志，避免频繁打印
          if (this.stuckCounter >= 3 || isAndroidDevice) {
            console.log('视频可能卡住了，计数:', this.stuckCounter);
            // 如果是荣耀设备，额外记录日志
            if (isHonorDevice) {
              console.log('荣耀设备视频卡住');
            }
          }
          
          // 如果连续多次没有变化，可能是流结束了
          // 安卓设备使用更低的阈值
          if (this.stuckCounter >= stuckThreshold) {
            console.log('检测到视频卡住超过阈值，可能是流结束');
            this.handleStreamEnded(isHonorDevice ? '荣耀设备视频卡住' : '视频卡住');
          }
        } else if (!this.videoEl.paused) {
          // 重置计数器
          this.stuckCounter = 0;
          this.lastPlaybackTime = this.videoEl.currentTime;
        }
        
        // 检查视频是否处于无数据状态
        if (this.videoEl.readyState <= 2 && !this.videoEl.paused && this.videoEl.currentTime > 0) {
          // 降低日志输出频率
          if (this.stuckCounter % 3 === 0) {
            console.log('视频处于无数据状态:', this.videoEl.readyState);
          }
          this.stuckCounter++;
          
          // 增加无数据状态的容忍度
          // 安卓设备使用更低的阈值
          if (this.stuckCounter >= (isAndroidDevice ? 4 : 6)) {
            console.log('检测到视频无数据状态持续，可能是流结束');
            this.handleStreamEnded(isHonorDevice ? '荣耀设备视频无数据' : '视频无数据');
          }
        }
      }, checkInterval);
    },
    
    // 处理流结束事件
    handleStreamEnded(reason) {
      if (this.isStreamEnded) return; // 避免重复触发
      
      console.log('处理流结束:', reason);
      this.isStreamEnded = true;
      
      // 清除定时器
      this.clearStreamDetectionTimers();
      
      // 针对安卓设备的错误进行特殊处理
      let isAndroidSpecificError = reason && 
          (reason.includes("PIPELINE_ERROR_EXTERNAL_RENDERER_FAILED") ||
           reason.includes("MEDIA_ELEMENT_ERROR: Format error") ||
           reason.includes("Format error") ||
           reason.includes("DEMUXER_ERROR_COULD_NOT_PARSE") ||
           reason.includes("荣耀手机") ||
           (reason.includes("错误代码: 4") && !this.isApple()));
           
      // 发送事件给父组件
      this.$ownerInstance.callMethod('eventEmit', { 
        event: 'sourceEnded', 
        data: isAndroidSpecificError ? 'android_format_error' : reason
      });
      
      // 触发结束事件，显示直播结束提示
      this.$ownerInstance.callMethod('eventEmit', { event: 'ended' });
    },
    
    // 清除流检测定时器
    clearStreamDetectionTimers() {
      if (this.blackScreenCheckTimer) {
        clearInterval(this.blackScreenCheckTimer);
        this.blackScreenCheckTimer = null;
      }
      
      if (this.noDataTimer) {
        clearTimeout(this.noDataTimer);
        this.noDataTimer = null;
      }
      
      this.stuckCounter = 0;
      this.lastPlaybackTime = 0;
    },
    
    // 销毁流媒体播放器
    destroyStreamingPlayers() {
      // 销毁HLS播放器
      if (this.hls) {
        this.hls.destroy();
        this.hls = null;
      }
      
      // 销毁FLV播放器
      if (this.flvPlayer) {
        this.flvPlayer.destroy();
        this.flvPlayer = null;
      }
      
      // 清除流检测定时器
      this.clearStreamDetectionTimers();
    },
    
    // 设置FLV播放器
    async setupFlvPlayer(src) {
      try {
        if (!window.flvjs) {
          if (!this.flvJsLoaded) {
            // 动态加载flv.js
            console.log('正在加载flv.js...');
            await this.loadScript('https://cdn.jsdelivr.net/npm/flv.js@latest');
            this.flvJsLoaded = true;
          } else {
            console.log('等待flv.js加载完成...');
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
        
        if (window.flvjs && window.flvjs.isSupported()) {
          // 销毁现有的FLV实例
          if (this.flvPlayer) {
            this.flvPlayer.destroy();
          }
          
          console.log('使用flv.js播放:', src);
          this.flvPlayer = window.flvjs.createPlayer({
            type: 'flv',
            url: src
          });
          this.flvPlayer.attachMediaElement(this.videoEl);
          this.flvPlayer.load();
          
          // 添加FLV播放器特定事件处理
          // 监听FLV的首帧渲染事件
          let hasRenderedFirstFrame = false;
          this.flvPlayer.on(window.flvjs.Events.MEDIA_INFO, () => {
            console.log('FLV媒体信息已加载');
          });
          
          this.flvPlayer.on(window.flvjs.Events.STATISTICS_INFO, () => {
            if (!hasRenderedFirstFrame && this.loadingEl) {
              hasRenderedFirstFrame = true;
              console.log('FLV首帧已渲染，隐藏loading');
              setTimeout(() => {
                this.loadingEl.style.display = 'none';
              }, 500);
            }
          });
          
          if (this.renderProps.autoplay) {
            this.flvPlayer.play();
          }
          
          // 错误处理
          this.flvPlayer.on(window.flvjs.Events.ERROR, (errorType, errorDetail) => {
            console.error('FLV错误:', errorType, errorDetail);
            this.$ownerInstance.callMethod('eventEmit', { event: 'error', data: {errorType, errorDetail} });
          });
        } else {
          console.error('FLV不被支持，回退到直接播放');
          this.videoEl.src = src;
        }
      } catch (e) {
        console.error('FLV播放器设置失败:', e);
        // 回退到尝试直接播放
        this.videoEl.src = src;
      }
    },
    
    // 动态加载JavaScript库
    loadScript(url) {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.async = true;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    },
    
    // 创建 loading
    createLoading() {
      const { isLoading } = this.renderProps
      if (!this.isApple() && isLoading) {
        const loadingEl = document.createElement('div')
        this.loadingEl = loadingEl
        loadingEl.className = 'loading-wrapper'
        loadingEl.style.position = 'absolute'
        loadingEl.style.top = '0'
        loadingEl.style.left = '0'
        loadingEl.style.zIndex = '99999'
        loadingEl.style.width = '100%'
        loadingEl.style.height = '100%'
        loadingEl.style.backgroundColor = '#000000'
        loadingEl.style.display = 'block'
        document.getElementById(this.wrapperId).appendChild(loadingEl)

        // 创建 loading 动画
        const animationEl = document.createElement('div')
        animationEl.className = 'loading'
        animationEl.style.zIndex = '2'
        animationEl.style.position = 'absolute'
        animationEl.style.top = '50%'
        animationEl.style.left = '50%'
        animationEl.style.marginTop = '-15px'
        animationEl.style.marginLeft = '-15px'
        animationEl.style.width = '30px'
        animationEl.style.height = '30px'
        animationEl.style.border = '2px solid #FFF'
        animationEl.style.borderTopColor = 'rgba(255, 255, 255, 0.2)'
        animationEl.style.borderRightColor = 'rgba(255, 255, 255, 0.2)'
        animationEl.style.borderBottomColor = 'rgba(255, 255, 255, 0.2)'
        animationEl.style.borderRadius = '100%'
        animationEl.style.animation = 'circle infinite 0.75s linear'
        loadingEl.appendChild(animationEl)

        // 创建 loading 动画所需的 keyframes
        const style = document.createElement('style')
        const keyframes = `
          @keyframes circle {
            0% {
              transform: rotate(0);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        `
        style.type = 'text/css'
        if (style.styleSheet) {
          style.styleSheet.cssText = keyframes
        } else {
          style.appendChild(document.createTextNode(keyframes))
        }
        document.head.appendChild(style)
      }
    },
    
    // 监听视频相关事件
    listenVideoEvent() {
      // 存储第一次播放标志
      let hasPlayed = false

      // 播放事件监听
      const playHandler = () => {
        this.$ownerInstance.callMethod('eventEmit', { event: 'play' })
        this.$ownerInstance.callMethod('setViewData', {
          key: 'playing',
          value: true
        })

        // 如果是第一次播放，延迟隐藏loading元素，确保视频已经开始显示内容
        if (this.loadingEl && !hasPlayed) {
          hasPlayed = true
          setTimeout(() => {
            this.loadingEl.style.display = 'none'
          }, 800)
        } else if (this.loadingEl) {
          this.loadingEl.style.display = 'none'
        }
      }
      this.videoEl.removeEventListener('play', playHandler)
      this.videoEl.addEventListener('play', playHandler)

      // 暂停事件监听
      const pauseHandler = () => {
        this.$ownerInstance.callMethod('eventEmit', { event: 'pause' })
        this.$ownerInstance.callMethod('setViewData', {
          key: 'playing',
          value: false
        })
      }
      this.videoEl.removeEventListener('pause', pauseHandler)
      this.videoEl.addEventListener('pause', pauseHandler)

      // 结束事件监听
      const endedHandler = () => {
        this.$ownerInstance.callMethod('eventEmit', { event: 'ended' })
        this.$ownerInstance.callMethod('resetEventCommand')
      }
      this.videoEl.removeEventListener('ended', endedHandler)
      this.videoEl.addEventListener('ended', endedHandler)

      // 加载完成事件监听
      const canPlayHandler = () => {
        this.$ownerInstance.callMethod('eventEmit', { event: 'canplay' })
        this.execDelayFunc()
      }
      this.videoEl.removeEventListener('canplay', canPlayHandler)
      this.videoEl.addEventListener('canplay', canPlayHandler)

      // 加载失败事件监听
      const errorHandler = (e) => {
        if (this.loadingEl) {
          this.loadingEl.style.display = 'block'
        }
        
        let errorData = {};
        if (e && e.target && e.target.error) {
          const errorCode = e.target.error.code;
          const errorMessage = e.target.error.message || '';
          
          errorData = {
            code: errorCode,
            message: errorMessage
          };
          
          console.log('错误代码:', errorCode, '错误信息:', errorMessage);
          
          // MediaError.MEDIA_ERR_NETWORK = 2, 表示在下载过程中出现了网络错误
          // MediaError.MEDIA_ERR_DECODE = 3, 表示解码失败
          // 错误代码4在安卓设备上可能是各种格式错误，如：
          // - PIPELINE_ERROR_EXTERNAL_RENDERER_FAILED - 播放器渲染失败
          // - MEDIA_ELEMENT_ERROR: Format error - 媒体格式错误
          // 这两种错误在直播流结束时比较常见
          if (errorCode === 2 || errorCode === 3 || 
             (errorCode === 4 && (
               errorMessage.includes("PIPELINE_ERROR_EXTERNAL_RENDERER_FAILED") || 
               errorMessage.includes("MEDIA_ELEMENT_ERROR: Format error") ||
               errorMessage.includes("Format error")
             ))) {
            console.log('检测到流结束相关错误:', errorCode, errorMessage);
            // 在安卓设备上，遇到格式错误时不再尝试恢复，直接判定为流结束
            this.handleStreamEnded('错误代码: ' + errorCode + ', ' + errorMessage);
          }
        }
        
        this.$ownerInstance.callMethod('eventEmit', { 
          event: 'error',
          data: errorData
        });
      }
      this.videoEl.removeEventListener('error', errorHandler)
      this.videoEl.addEventListener('error', errorHandler)

      // loadedmetadata 事件监听
      const loadedMetadataHandler = () => {
        this.$ownerInstance.callMethod('eventEmit', { event: 'loadedmetadata' })
        // 获取视频的长度
        const duration = this.videoEl.duration
        this.$ownerInstance.callMethod('eventEmit', {
          event: 'durationchange',
          data: duration
        })

        this.$ownerInstance.callMethod('setViewData', {
          key: 'duration',
          value: duration
        })

        // 加载首帧视频 模拟出封面图
        this.loadFirstFrame()
      }
      this.videoEl.removeEventListener('loadedmetadata', loadedMetadataHandler)
      this.videoEl.addEventListener('loadedmetadata', loadedMetadataHandler)

      // 播放进度监听
      const timeupdateHandler = (e) => {
        const currentTime = e.target.currentTime
        this.$ownerInstance.callMethod('eventEmit', {
          event: 'timeupdate',
          data: currentTime
        })

        this.$ownerInstance.callMethod('setViewData', {
          key: 'currentTime',
          value: currentTime
        })

      }
      this.videoEl.removeEventListener('timeupdate', timeupdateHandler)
      this.videoEl.addEventListener('timeupdate', timeupdateHandler)

      // 倍速播放监听
      const ratechangeHandler = (e) => {
        const playbackRate = e.target.playbackRate
        this.$ownerInstance.callMethod('eventEmit', {
          event: 'ratechange',
          data: playbackRate
        })
      }
      this.videoEl.removeEventListener('ratechange', ratechangeHandler)
      this.videoEl.addEventListener('ratechange', ratechangeHandler)

      // 全屏事件监听
      if (this.isApple()) {
        const webkitbeginfullscreenHandler = () => {
          const presentationMode = this.videoEl.webkitPresentationMode
          let isFullScreen = null
          if (presentationMode === 'fullscreen') {
            isFullScreen = true
          } else {
            isFullScreen = false
          }
          this.$ownerInstance.callMethod('eventEmit', {
            event: 'fullscreenchange',
            data: isFullScreen
          })
        }
        this.videoEl.removeEventListener('webkitpresentationmodechanged', webkitbeginfullscreenHandler)
        this.videoEl.addEventListener('webkitpresentationmodechanged', webkitbeginfullscreenHandler)
      } else {
        const fullscreenchangeHandler = () => {
          let isFullScreen = null
          if (document.fullscreenElement) {
            isFullScreen = true
          } else {
            isFullScreen = false
          }
          this.$ownerInstance.callMethod('eventEmit', {
            event: 'fullscreenchange',
            data: isFullScreen
          })
        }
        document.removeEventListener('fullscreenchange', fullscreenchangeHandler)
        document.addEventListener('fullscreenchange', fullscreenchangeHandler)
      }
      
      // 添加监听 stalled 事件 - 当浏览器尝试获取媒体数据但数据不可用时触发
      const stalledHandler = () => {
        // 仅记录事件发生，不输出日志，减少干扰
        // 如果已经播放了一段时间，卡住可能意味着流结束
        if (this.videoEl.currentTime > 5) { // 5秒是个阈值，可以根据需要调整
          // 不立即判断为流结束，增加一个延迟检查
          setTimeout(() => {
            // 如果仍然卡住且未暂停
            if (!this.videoEl.paused && this.videoEl.readyState < 3) {
              console.log('视频持续卡住，可能是流结束');
              // 不立即结束，增加额外的stalled计数
              this.stuckCounter += 2; // 增加计数，但让主检测逻辑来决定是否结束
            }
          }, 5000); // 5秒后检查
        }
      };
      this.videoEl.removeEventListener('stalled', stalledHandler);
      this.videoEl.addEventListener('stalled', stalledHandler);
      
      // 添加监听 waiting 事件 - 当视频停止播放准备缓冲时触发
      const waitingHandler = () => {
        // 仅在需要时输出日志
        // 类似stalled的处理逻辑
        if (this.videoEl.currentTime > 5) {
          setTimeout(() => {
            // 如果仍然卡住且未暂停
            if (!this.videoEl.paused && this.videoEl.readyState < 3) {
              console.log('视频持续等待数据，可能是流结束');
              // 改为增加stuckCounter，让主检测流程统一处理
              this.stuckCounter += 3; // 增加waiting权重，但由主检测流程决定
              // 只有等待状态持续较长时间才判断为结束
              if (this.stuckCounter >= 10) {
                this.handleStreamEnded('视频waiting持续时间过长');
              }
            }
          }, 8000); // 增加等待容忍时间
        }
      };
      this.videoEl.removeEventListener('waiting', waitingHandler);
      this.videoEl.addEventListener('waiting', waitingHandler);

      // 增加视频帧可见性检测，特别针对安卓设备
      const visibleVideoHandler = () => {
        if (this.isAndroid && this.loadingEl && this.videoEl.readyState >= 3) {
          console.log('视频内容可见，隐藏loading');
          setTimeout(() => {
            this.loadingEl.style.display = 'none';
          }, 300);
        }
      };
      this.videoEl.removeEventListener('canplaythrough', visibleVideoHandler);
      this.videoEl.addEventListener('canplaythrough', visibleVideoHandler);
      
      // 对于安卓设备，增加额外的检测，确保loading被隐藏
      if (this.isAndroid) {
        const forceHideLoading = () => {
          if (this.loadingEl && this.videoEl && !this.videoEl.paused && this.videoEl.currentTime > 0) {
            console.log('安卓设备强制隐藏loading');
            this.loadingEl.style.display = 'none';
          }
        };
        // 播放一秒后检查，如果视频在播放但loading仍然显示，则强制隐藏
        setTimeout(forceHideLoading, 1000);
      }
    },
    // 加载首帧视频，模拟出封面图
    loadFirstFrame() {
      let { autoplay, muted } = this.renderProps
      // 保存原始的静音状态
      const originalMuted = muted

      // 确保在任何播放尝试前loading元素都可见
      if (!this.isApple() && this.loadingEl) {
        this.loadingEl.style.display = 'block'
      }
      
      if (this.isApple()) {
        // 苹果设备先强制静音播放，以避免出现在媒体控制中心
        this.videoEl.muted = true
        this.videoEl.play()
        
        // 如果原本不需要静音且需要自动播放，则延迟后恢复声音
        if (!originalMuted && autoplay) {
          // 增加日志调试
          console.log('苹果设备设置延迟恢复声音，原始静音状态:', originalMuted);
          
          // 使用更长的延迟时间，确保系统已经完成媒体类型判断
          setTimeout(() => {
            console.log('苹果设备尝试恢复声音');
            // 直接设置属性并尝试通过API调用确保生效
            this.videoEl.muted = false;
            
            // 尝试通过音量设置来确保声音可以播放
            if (this.videoEl.volume === 0) {
              this.videoEl.volume = 1.0;
            }
            
            // 通知父组件静音状态已更改
            this.$ownerInstance.callMethod('eventEmit', { 
              event: 'volumechange', 
              data: { muted: false, volume: this.videoEl.volume } 
            });
            
            console.log('苹果设备声音恢复状态:', !this.videoEl.muted, '音量:', this.videoEl.volume);
          }, 2500); // 延长至2.5秒
        }
        
        if (!autoplay) {
          this.videoEl.pause()
        }
      } else {
        // 针对安卓设备优化播放体验
        if (this.isAndroid) {
          console.log('安卓设备首帧加载优化');
          this.videoEl.muted = true;
          
          // 减少延迟，让视频更快开始加载
          setTimeout(() => {
            this.videoEl.play();
            // 恢复原始静音状态
            this.videoEl.muted = muted;
            
            // 设置一个定时器检查是否播放中，但loading仍然显示
            if (!autoplay) {
              setTimeout(() => {
                this.videoEl.pause();
                // 安卓设备在pause时可能需要手动隐藏loading
                if (this.loadingEl) {
                  this.loadingEl.style.display = 'none';
                }
              }, 100);
            } else {
              // 对于自动播放，设置额外检查确保loading正确隐藏
              setTimeout(() => {
                if (this.loadingEl && !this.videoEl.paused && this.videoEl.readyState >= 2) {
                  this.loadingEl.style.display = 'none';
                }
              }, 1000);
            }
          }, 10);
        } else {
          // 非安卓设备使用原有逻辑
          this.videoEl.muted = true
          setTimeout(() => {
            this.videoEl.play()
            this.videoEl.muted = muted
            if (!autoplay) {
              setTimeout(() => {
                this.videoEl.pause()
              }, 100)
            }
          }, 10)
        }
      }
    },
    triggerCommand(eventType) {
      if (eventType) {
        this.$ownerInstance.callMethod('resetEventCommand')
        this.videoEl && this.videoEl[eventType]()
      }
    },
    triggerFunc(func) {
      const { name, params } = func || {}
      if (name) {
        this[name](params)
        this.$ownerInstance.callMethod('resetFunc')
      }
    },
    removeHandler() {
      if (this.videoEl) {
        this.videoEl.pause()
        this.videoEl.src = ''
        this.$ownerInstance.callMethod('setViewData', {
          key: 'videoSrc',
          value: ''
        })
        // 销毁流媒体播放器
        this.destroyStreamingPlayers();
        this.videoEl.load()
      }
    },
    fullScreenHandler() {
      if (this.isApple()) {
        this.videoEl.webkitEnterFullscreen()
      } else {
        this.videoEl.requestFullscreen()
      }
    },
    toSeekHandler({ sec, isDelay }) {
      const func = () => {
        if (this.videoEl) {
          this.videoEl.currentTime = sec
        }
      }

      // 延迟执行
      if (isDelay) {
        this.delayFunc = func
      } else {
        func()
      }
    },
    // 执行延迟函数
    execDelayFunc() {
      this.delayFunc && this.delayFunc()
      this.delayFunc = null
    },
    viewportChange(props) {
      this.renderProps = props
      const { autoplay, muted, controls, loop, playbackRate } = props
      if (this.videoEl) {
        this.videoEl.autoplay = autoplay
        this.videoEl.controls = controls
        this.videoEl.loop = loop
        this.videoEl.muted = muted
        this.videoEl.playbackRate = playbackRate
      }
    },
    randomNumChange(val) {
      this.num = val
    }
  }
}
</script>

<style scoped>
.player-wrapper {
  overflow: hidden;
  height: 100%;
  padding: 0;
  position: relative;
}

/* 隐藏全屏模式下的控制栏 */
video::-webkit-media-controls {
  display: none !important;
}

video::-webkit-media-controls-enclosure {
  display: none !important;
}

video::-webkit-media-controls-panel {
  display: none !important;
}

video::-webkit-media-controls-play-button {
  display: none !important;
}

video::-webkit-media-controls-timeline {
  display: none !important;
}

video::-webkit-media-controls-current-time-display {
  display: none !important;
}

video::-webkit-media-controls-time-remaining-display {
  display: none !important;
}

video::-webkit-media-controls-mute-button {
  display: none !important;
}

video::-webkit-media-controls-volume-slider {
  display: none !important;
}

video::-webkit-media-controls-fullscreen-button {
  display: none !important;
}

/* 适配火狐浏览器 */
video::-moz-media-controls {
  display: none !important;
}

/* 适配IE浏览器 */
video::-ms-media-controls {
  display: none !important;
}

</style>
