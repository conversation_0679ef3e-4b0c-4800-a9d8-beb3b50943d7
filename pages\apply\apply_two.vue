<template>
	<view>


		<view class="content">
		<!-- <u-notice-bar text="不借款不收费,融信与联通共同组建,正规靠谱" bg-color="#e0ebff" color="#397afe" mode="closable"></u-notice-bar> -->
		<!-- <u-steps current="1" active-color="#397afe" class="mt20 mb20">
			<u-steps-item title="身份认证">
			</u-steps-item>
			<u-steps-item title="实名认证"></u-steps-item>
			<u-steps-item title="完善信息"></u-steps-item>
		</u-steps> -->
		<view style="width: 750upx;">
			<view class="dis-ali " style="justify-content:center;align-items: center;">
			</view>
			<view>
				<image src="../../static/image/rl.png" mode="widthFix" style="width: 750upx;"></image>
			</view>
		</view>
		



			<view class="dis-ali mt20 mb10" style="justify-content: flex-start;width: 680upx;">
				<u-checkbox-group iconPlacement='left' v-model="btn" placement="column" @change="ChangeIntegral">
					<u-checkbox name="1" shape='circle' size="18">

					</u-checkbox>

				</u-checkbox-group>
				<view style="font-size: 26upx;color: #939393;">
					我已阅读并同意<span
						style="color: #397afe;" @click="goUrl(3)">人脸信息处理授权协议</span><!-- <span style="color: #397afe;">融信生活隐私政策</span> -->
				</view>
			</view>

			<view @click="show = false,a()"
				style="font-size: 34upx;upx;color: #fff;background-color: #397afe;width: 680upx;height: 100upx;border-radius: 10upx;display: flex;align-items: center;justify-content: center;margin-top: 30upx;">
				下一步
			</view>
			<view style="font-size: 26upx;color: #939393;margin-top: 50upx;">
				人脸信息仅用于核实您的身份，我们会严格保密
			</view>
			<u-popup :show="show1" mode="center" round='5'>
				<view style="padding: 40upx;width: 600upx;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">关于个人信息处理规则说明</text>
					<view style="line-height: 1.7;text-indent: 2em;margin-top: 20upx;font-size: 28upx;">
						同意/允许融信生活通过语音/短信方式向我推荐我可能感兴趣的内容，包括但不限于:会员权益服务、促销、活动、产品等信息。 如不希望接收上述信息，可联系融信客服处理</view>

					<view @click="show = false"
						class='btn'>
						我知道了
					</view>

				</view>

			</u-popup>
			<u-popup :show="show" mode="center" round='5'>
				<view style="padding: 40upx;width: 600upx;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">关于个人信息处理规则说明</text>
					<view style="line-height: 1.7;text-indent: 2em;margin-top: 20upx;font-size: 28upx;">
						同意/允许融信生活通过语音/短信方式向我推荐我可能感兴趣的内容，包括但不限于:会员权益服务、促销、活动、产品等信息。 如不希望接收上述信息，可联系融信客服处理</view>
					<view @click="show = false"
						class='btn'>
						我知道了
					</view>
				</view>

			</u-popup>
			<u-popup :show="show2" mode="bottom" round='5' :closeable='true'>
				<view style="padding: 40upx;width: 100%;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">查看和下载身份证照片</text>
					<view>
						<image src="../../static/image/zfb.jpg" mode="widthFix" style="width: 710upx;"></image>
					</view>
					<view @click="show2 = false" class="btn">
						我知道了
					</view>
				</view>

			</u-popup>









			
		</view>
		</view>
	</view>
</template>

<script>
	var that
	export async function uniCloudVerify({ realName, idCard }) {
	    // 获取设备信息
	    const metaInfo = uni.getFacialRecognitionMetaInfo();
	    // 注册实人认证
			const uniCloudVerify = uniCloud.importObject('uni-cloud-verify');
	    // 获取CertifyId
	    const certifyidResult = await uniCloudVerify.getCertifyId({ realName, idCard, metaInfo })
	    if (certifyidResult.errCode != 0) return Promise.reject({ code: 500, msg: '获取certifyid失败，请联系管理员处理!' })
	    const certifyId = certifyidResult.certifyId;
	
	    // 开始认证
	    const startFacialRecognitionVerify = (certifyId) => {
	        return new Promise((resolve, reject) => {
	            uni.startFacialRecognitionVerify({
	                certifyId: certifyId,
	                success: async res => {
						console.log(res)
	                    const result = await uniCloudVerify.getAuthResult(certifyId);
	                    resolve(res);
	                },
	                fail: err => {
						console.log(err)
	                    // 验证失败，要扣钱的，certifyId已失效
	                    // err.errCode == '10013'
	                    // 验证中断，不扣钱
	                    // err.errCode == '10011'
	                    reject({ code: err.errCode, msg: err.errMsg })
	                }
	            });
	        })
	    }
	    // 开始认证
	    try {
	        const facialRecognitionVerifyValue = await startFacialRecognitionVerify(certifyId);
	    } catch (e) {
	        return Promise.reject(e);
	    }
	    // 获取结果
	    const authResult = await uniCloudVerify.getAuthResult(certifyId);
	    if (authResult.errCode != 0) return Promise.reject({ code: 500, msg: '获取认证信息失败，请联系管理员处理!' });
	    console.log(authResult, '认证后数据')
	    return Promise.resolve(authResult);
	}
	import face from "@/uni_modules/mcc-face2/index.js"
	import fun from "@/uni_modules/mcc-face2/static/dz.js"
	export default {
		data() {
			return {
				list1: [
					'../../static/image/20230208195156793b17.png',
					'../../static/image/202302081952019c0ab4.png',
				],
				show: false,
				btn: [],
				show1: false,
				tips: '',
				value: '',
				show2: false,
				realName:'',
				idCard:'',
			}
		},
		watch: {
			value(newValue, oldValue) {
				// console.log('v-model', newValue);
			}
		},
		onLoad(option) {
			that = this;
				this.realName=option.name
				this.idCard=option.number
		},
		
		methods: {
			a() {
				if (this.btn.length == 0) {
					this.$u.toast('阅读并同意相关协议')
					return;
				}
				 // #ifdef H5
				uni.navigateTo({
					url: '/pages/apply/apply_three'
				})
				return;
				// #endif
			
			
				const realName=this.realName
				const idCard=this.idCard
				console.log(realName,idCard)
				// const uniCloudVerify = uniCloud.importObject('uni-cloud-verify');
					uniCloudVerify({ realName, idCard }).then(res => {
				    // 提示成功
					console.log(res)
					that.http.ajax({
						url: that.http.api.memberedit,
						method: 'POST',
						data: {
							is_auth:1
						},
						success(res) {
							if (res.code == 200) {
								uni.showToast({
									title: res.message,
									icon: 'none'
								})
								that.http.info()
								uni.redirectTo({
									url: '/pages/apply/apply_three'
								})
								
							} else {
								uni.showToast({
									title: res.message,
									icon: 'none'
								})
							}
						}
					})
					
				}).catch((e) => {
				    // 提示失败
					console.log(e)
					
					if(e.code=='10013'){
						uni.showToast({
							title:'信息不匹配',
							icon:'none'
						})
					}else if(e=='Error'){
						uni.showToast({
							title:'请检查您的姓名与身份证号真实性',
							icon:'none'
						})
					}else{
						uni.showToast({
							title:e,
							icon:'none'
						})
					}
					
				})
			},
			
			ChangeIntegral() {

			},
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			change(e) {
				console.log('change', e);
			},
			goUrl(type) {
			
				if (type ==3) {
					var config=uni.getStorageSync('config')
					uni.navigateTo({
						url: '/pages/detail/detail?id='+config.agreement_renlian
					})
				}
			},
			gotoAnli() {

				uni.navigateTo({
					url: '/pages/train/train'
				})
			},


			aa(html) {
				const el = document.createElement('div')
				el.innerHTML = html
				return el.innerText
			},
			gotoDetail(id) {
				uni.navigateTo({
					url: '/pages/main/main?id=' + id
				})
			},
			tabChange(index) {

				this.tabId = index.id
				that.page = 0
				that.list = []
				this.status = 'loadmore'
				this.getlist(index.id)
			}
		}
	}
</script>
<style>
	page,
	body {
		background-color: #fff;
		/* background-color: #fff; */
		/* height: 100%; */
	}
</style>

<style lang="scss">
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		// z-index: 1;
		// position: absolute;
		// top: 0;
		// width: 100%;
		// // height: 500upx;
		// background-image: linear-gradient(180deg,#397afe,#f3f3f3);
	}

	.btn {
		font-size: 34upx;
		color: #fff;
		background-color: #397afe;
		width: 500upx;
		height: 100upx;
		border-radius: 10upx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30upx;
	}
</style>