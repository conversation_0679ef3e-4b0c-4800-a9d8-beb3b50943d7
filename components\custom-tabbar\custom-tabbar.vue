<template>
  <u-tabbar 
    :value="currentTab" 
    :fixed="true" 
    :border="false" 
    :placeholder="true" 
    :safeAreaInsetBottom="true"
    @change="onChange" 
    :activeColor="'#00C1CC'" 
    :inactiveColor="'#7d7e80'"
  >
    <u-tabbar-item v-for="(item,index) in tabbarList" :key="index" :text="item.text">
      <image 
        style='width:40upx;height:40upx;' 
        class="u-page__item__slot-icon" 
        slot="active-icon"
        :src="currentTab === index ? (showGif === index ? item.selectedIconPath : item.staticSelectedPath) : item.iconPath"
      ></image>
      <image 
        style='width:40upx;height:40upx;' 
        class="u-page__item__slot-icon" 
        slot="inactive-icon"
        :src="item.iconPath"
      ></image>
    </u-tabbar-item>
  </u-tabbar>
</template>

<script>
export default {
  name: 'custom-tabbar',
  props: {
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      currentTab: this.current,
      showGif: -1,
      tabbarList: [{
        text: '首页',
        iconPath: '/static/image/icons/tabBar_me_2x.png',
        selectedIconPath: '/static/sy3.gif',
        staticSelectedPath: '/static/image/icons/tabBar_activity_selected_0.png',
      },
      {
        text: '发现',
        iconPath: '/static/image/icons/tabBar_activity.png',
        selectedIconPath: '/static/sy3.gif',
        staticSelectedPath: '/static/image/icons/tabBar_activity_selected_0.png',
      },
      {
        text: '我的',
        iconPath: '/static/image/icons/tabBar_me_2x.png',
        selectedIconPath: '/static/sy3.gif',
        staticSelectedPath: '/static/image/icons/tabBar_me_selected_2x.png',
      }]
    }
  },
  watch: {
    // 监听 current 的变化，处理 gif 动画
    current: {
      handler(newVal) {
        this.currentTab = newVal;
        // 显示 gif
        this.showGif = newVal;
        
        // 300ms后切换到静态图
        setTimeout(() => {
          this.showGif = -1;
        }, 300);
      },
      immediate: true
    }
  },
  // 在 mounted 中预加载其他页面
   mounted() {
      // 获取当前页面路径
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const currentPath = `/${currentPage.route}`;
      
      // 预加载其他页面
      this.tabbarList.forEach(item => {
        if (item.pagePath !== currentPath) {
          uni.preloadPage({
            url: item.pagePath
          });
        }
      });
    },
  methods: {
    onChange(name) {
      if (this.currentTab === name) return; // 避免重复切换
      
      this.currentTab = name;
      this.$emit('update:current', name);
      
      // 显示 gif
      this.showGif = name;
      
      // 300ms后切换到静态图
      setTimeout(() => {
        this.showGif = -1;
      }, 300);

      let url = '';
      switch (name) {
        case 0:
          url = '/pages/index/index';
          break;
        case 1:
          url = '/pages/growthPlan/growthPlan';
          break;
        case 2:
          url = '/pages/my/my';
          break;
      }
      
      uni.reLaunch({
        url:url
      });
    }
  }
}
</script>