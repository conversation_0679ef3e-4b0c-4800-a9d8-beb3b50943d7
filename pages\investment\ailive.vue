<template>
	<view class="live-room-container" @click="toggleControlsVisible">
		<!-- 全屏视频播放器 - 使用DomVideoPlayer组件 -->
		<DomVideoPlayer
			id="videoPlayer"
			ref="domVideoPlayer"
			:src="videoInfo.url" 
			:autoplay="true"
			:controls="false"
			:muted="false"
			:isLoading="true"
			:objectFit="isLandscape ? 'contain' : 'contain'"
			@play="onVideoPlay"
			@pause="onVideoPause"
			@ended="onVideoEnded"
			@error="onVideoError"
			:class="['full-video-player', isLandscape ? 'landscape-video' : '']"
		/>
		<!-- 顶部悬浮控制栏 -->
		<view class="top-controls" :class="{'landscape-top-controls': isLandscape, 'controls-hidden': !isControlsVisible}" @touchstart="showControlsOnTouch">
			<view class="gradient-overlay" v-if="!isLandscape"></view>
			
			<view class="live-info" :class="{'landscape-info': isLandscape}">
				<view class="back-btn" @click="goBack">
					<u-icon name="arrow-left" color="#FFFFFF" size="28"></u-icon>
				</view>
				<view class="live-author-info" :class="{'landscape-author-info': isLandscape}">
					<view class="live-author-name">{{ videoInfo.name || '精彩内容' }}</view>
					<view class="live-title">{{ videoInfo.author || '项目负责人' }}</view>
				</view>
				<!-- <view class="follow-btn" @click="toggleFollow">
					{{ isFollowed ? '已关注' : '+ 关注' }}
				</view> -->
				
			</view>
		</view>
		
		<!-- 右侧悬浮控制栏 -->
		<view class="right-controls" :class="{'controls-hidden': !isControlsVisible}" v-if="!isLandscape">
			<view class="control-icon-item" @click="toggleLike">
				<view class="icon-wrapper" :class="{'active': isLiked}">
					<u-icon :name="isLiked ? 'heart-fill' : 'heart'" color="#FFFFFF" size="26"></u-icon>
				</view>
				<text>{{ formatCount(likeCount) }}</text>
			</view>
		<!-- 	<view class="control-icon-item" @click="showComments">
				<view class="icon-wrapper">
					<u-icon name="chat" color="#FFFFFF" size="26"></u-icon>
				</view>
				<text>{{ formatCount(commentCount) }}</text>
			</view> -->
			<!-- <view class="control-icon-item" @click="shareVideo">
				<view class="icon-wrapper">
					<u-icon name="share" color="#FFFFFF" size="26"></u-icon>
				</view>
				<text>分享</text>
			</view> -->
			<view class="control-icon-item" @click="toggleRecommend">
				<view class="icon-wrapper" :class="{'active': isRecommendOpen}">
					<u-icon name="list" color="#FFFFFF" size="26"></u-icon>
				</view>
				<text>推荐</text>
			</view>
		</view>
		
		<!-- 视频播放按钮 - 暂停时显示 -->
		<view class="play-btn-center" v-if="!isPlaying && !isInitialLoading" @click="togglePlay">
			<view class="play-btn-icon">
				<u-icon name="play-right-fill" color="#FFFFFF" size="50"></u-icon>
			</view>
		</view>
		
		<!-- 推荐视频侧边抽屉 -->
		<view class="recommend-drawer" :class="{'drawer-open': isRecommendOpen}" @touchstart.stop @click.stop>
			<view class="drawer-header" :style="{'padding-top': statusBarHeight + 44 + 'px'}">
				<text class="drawer-title">特别推荐</text>
				<view class="drawer-close" @click.stop="toggleRecommend">
					<u-icon name="close" color="#FFFFFF" size="30"></u-icon>
				</view>
			</view>
			
			<scroll-view scroll-y class="recommend-list" @touchstart.stop @click.stop>
				<view 
					class="recommend-item" 
					v-for="(item, index) in relatedVideos" 
					:key="index"
					@click.stop="playRelatedVideo(item)"
				>
					<view class="recommend-cover-container">
						<image class="recommend-cover" :src="item.videoThumbnailUrl" mode="widthFix"></image>
					</view>
					<view class="recommend-info">
						<view class="recommend-name">{{item.name}}</view>
						<view class="recommend-meta">
							<text class="recommend-author">{{item.author || '未知'}}</text>
							<text class="recommend-duration" v-if="item.duration">{{formatDuration(item.duration)}}</text>
						</view>
					</view>
				</view>
				
				<!-- 空状态 -->
				<view class="empty-recommend" v-if="!loading && relatedVideos.length === 0">
					<u-icon name="list" color="#666666" size="50"></u-icon>
					<text class="empty-text">暂无相关内容</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 评论弹窗 -->
		<u-popup mode="bottom" :show="showCommentPopup" @close="closeComments" :safeAreaInsetBottom='false'  round="20">
			<view class="comment-container">
				<view class="comment-header">
					<text class="comment-title">评论 {{ formatCount(commentCount) }}</text>
					<view class="comment-close" @click="closeComments">
						<u-icon name="close" color="#333333" size="24"></u-icon>
					</view>
				</view>
				<scroll-view scroll-y class="comment-list">
					<view class="comment-item" v-for="(item, index) in commentList" :key="index">
						<view class="comment-content">
							<view class="comment-user">{{ item.userName }}</view>
							<view class="comment-text">{{ item.content }}</view>
							<view class="comment-time">{{ item.time }}</view>
						</view>
						<view class="comment-like" @click="likeComment(index)">
							<u-icon :name="item.isLiked ? 'heart-fill' : 'heart'" :color="item.isLiked ? '#FF5F5F' : '#999'" size="20"></u-icon>
							<text :class="{'liked': item.isLiked}">{{ formatCount(item.likes) }}</text>
						</view>
					</view>
					
					<!-- 空状态 -->
					<view class="empty-comment" v-if="commentList.length === 0">
						<u-icon name="chat" color="#666666" size="50"></u-icon>
						<text class="empty-text">暂无评论，快来抢沙发吧</text>
					</view>
				</scroll-view>
				<view class="comment-input-area">
					<view class="comment-input-box">
						<input type="text" placeholder="说点什么..." v-model="commentText" />
					</view>
					<view class="comment-send-btn" :class="{'active': commentText.trim()}" @click="sendComment">
						发送
					</view>
				</view>
			</view>
		</u-popup>
		
		<!-- 播放结束提示 -->
		<view class="play-end-tips" v-if="isPlayEnded">
			<view class="play-end-content">
				<view class="play-end-title">播放结束</view>
				<view class="play-end-thumbnail" v-if="videoInfo.videoThumbnailUrl">
					<image :src="videoInfo.videoThumbnailUrl" mode="widthFix"></image>
					<view class="play-end-thumbnail-mask"></view>
				</view>
				<view class="play-end-btns">
					<view class="play-end-btn replay" @click="replayVideo">
						<u-icon name="reload" color="#FFFFFF" size="18"></u-icon>
						<text>重新播放</text>
					</view>
					<view class="play-end-btn next" @click="playNextVideo" v-if="relatedVideos.length > 0">
						<u-icon name="rewind-right-fill" color="#FFFFFF" size="18"></u-icon>
						<text>下一视频</text>
					</view>
					<view class="play-end-btn back" @click="goBack">
						<u-icon name="home" color="#FFFFFF" size="18"></u-icon>
						<text>返回首页</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载层 -->
		<view class="loading-layer" v-if="isInitialLoading">
			<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle" iconSize='30'
				 :loading="isInitialLoading">
			</u-loading-page>
		</view>
		
		<!-- 横屏提示动画 -->
		<view class="rotate-toast" v-if="showRotateHint">
			<view class="rotate-animation">
				<view class="phone-container">
					<view class="phone">
						<view class="phone-inner"></view>
						<view class="phone-button"></view>
					</view>
				</view>
			</view>
			<text class="rotate-text">旋转手机获得更好体验</text>
		</view>
		
		<u-toast ref="uToast"></u-toast>
		
		<!-- H5 分享弹窗 -->
		<!-- #ifdef H5 -->
		<u-popup @close="closeSharePopup" :show="showSharePopup" mode="bottom" border-radius="20">
			<view class="share-popup">
				<view class="share-title">分享至</view>
				<view class="share-options">
					<view class="share-item" @click="copyLink">
						<image src="/static/image/copyurl.png" mode="aspectFit"></image>
						<text>复制链接</text>
					</view>
				</view>
				<view class="share-cancel" @click="closeSharePopup">取消</view>
			</view>
		</u-popup>
		<!-- #endif -->
		
		<!-- 恢复观看进度提示 -->
		<u-popup :show="showResumePopup" :safeAreaInsetBottom='false' round='20' mode="center" :closeable="true" @close="closeResumePopup" closeIconPos="top-right" bgColor='#1E1E1E' >
			<view class="resume-container">
				<view class="resume-title">恢复上次观看</view>
				<view class="resume-content">
					<view class="resume-time">上次观看到 {{ formatDuration(lastPlayTime) }}</view>
					<view class="resume-desc">是否从上次观看的位置继续播放？</view>
				</view>
				<view class="resume-btns">
					<view class="resume-btn cancel" @click="startFromBeginning">从头开始</view>
					<view class="resume-btn confirm" @click="resumePlayback">继续观看</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import DomVideoPlayer from '@/components/DomVideoPlayer/livePlayer.vue'
	import UniShare from 'uni_modules/uni-share/js_sdk/uni-share.js';
	
	const uniShare = new UniShare();
	
	export default {
		components: {
			DomVideoPlayer
		},
		data() {
			return {
				videoId: null,
				videoInfo: {
					id: null,
					name: '',
					url: '',
					videoThumbnailUrl: '',
					author: '',
					authorAvatar: '',
					duration: 0,
					businessName: '',
					musicName: ''
				},
				loading: true,
				isInitialLoading: true,
				isPlaying: false,
				isPlayEnded: false,
				relatedVideos: [],
				isRecommendOpen: false,
				isLiked: false,
				isFollowed: false,
				likeCount: 0,
				commentCount: 0,
				showCommentPopup: false,
				commentText: '',
				commentList: [],
				videoPlayer: null,
				statusBarHeight: 20, // 状态栏高度，默认值
				hasShownErrorToast: false, // 用于控制错误提示只显示一次
				showSharePopup: false, // H5 分享弹窗显示状态
				currentUrl: '', // 当前页面完整URL用于分享
				poserPath: '', // 海报路径
				posterConfig: null, // 海报配置
				posterUrl: '', // 海报背景URL
				isLandscape: false, // 是否是横屏模式
				landscapeTimer: null, // 横屏模式下的自动隐藏计时器
				isControlsVisible: true, // 控制栏是否可见
				windowWidth: 0,
				windowHeight: 0,
				showRotateHint: false, // 是否显示旋转提示
				videoIsLandscape: false, // 视频是否是横屏尺寸
				heartbeatTimer: null, // 心跳定时器
				heartbeatInterval: 30, // 心跳间隔秒数
				showResumePopup: false, // 是否显示恢复观看弹窗
				lastPlayTime: 0, // 上次观看进度（秒）
				companyId: '', // 公司ID，可从全局配置获取
				currentPlayTime: 0, // 当前播放时间
				videoPlayTimer: null, // 记录视频播放时间的定时器
				controlsTimer: null, // 控制栏自动隐藏计时器
				isPaused: false, // 添加暂停状态标记
			}
		},
		computed: {
			// 格式化视频描述，支持话题标签高亮显示
			videoDescription() {
				if (!this.videoInfo.businessName) return '';
				// 将#话题#替换为带样式的span
				return this.videoInfo.businessName.replace(/#([^#]+)#/g, '<span style="color: #00C1CC; font-weight: bold;">#$1#</span>');
			}
		},
		onResize() {
			// 屏幕尺寸变化时，重新检测屏幕方向
			this.checkOrientation();
		},
		onLoad(options) {
			// 重置所有定时器状态，确保没有遗留的定时器
			this.heartbeatTimer = null;
			this.videoPlayTimer = null;
			this.controlsTimer = null;
			this.landscapeTimer = null;
		
			// 获取状态栏高度
			this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 20;
			
			// 解锁屏幕方向，允许横屏
			// #ifdef APP-PLUS
			plus.screen.unlockOrientation();
			// #endif
			
			// 初始检测屏幕方向
			this.checkOrientation();
			
			// 注册窗口大小变化监听
			uni.onWindowResize(this.windowResizeCallback);
			
			// 尝试获取公司ID
			try {
				this.companyId = getApp().globalData.companyId || '';
			} catch (e) {
				console.error('获取公司ID失败:', e);
			}
			
			// 处理传递过来的视频数据
			if (options.videoData) {
				try {
					// 解析视频数据
					const videoData = JSON.parse(decodeURIComponent(options.videoData));
					
					// 更新视频信息
					this.videoInfo = {
						id: videoData.id,
						name: videoData.name,
						url: videoData.url,
						videoThumbnailUrl: videoData.videoThumbnailUrl,
						author: videoData.author || '项目负责人',
						businessName: videoData.description
					};
					
					this.videoId = videoData.id;
					
					// 处理相关推荐视频数据
					if (options.relatedVideos) {
						try {
							this.relatedVideos = JSON.parse(decodeURIComponent(options.relatedVideos));
						} catch (e) {
							console.error('解析相关视频数据失败:', e);
						}
					} else {
						// 如果没有传递相关推荐，则调用API获取
						// this.getRelatedVideos();
					}
					
					// 检查是否有上次观看记录
					this.checkLastPlaybackTime();
					
					// 开始心跳
					this.startHeartbeat();
					
					// 开始记录播放时间
					this.startTrackingPlayTime();
					
					// 模拟数据
					this.likeCount = Math.floor(Math.random() * 10000) + 100;
					this.commentCount = Math.floor(Math.random() * 500) + 10;
					this.generateMockComments();
					
					// 延迟关闭加载状态，确保视频加载
					setTimeout(() => {
						this.isInitialLoading = false;
					}, 1000);
				} catch (e) {
					console.error('解析视频数据失败:', e);
					this.isInitialLoading = false;
				}
			}  else {
				this.loading = false;
				this.isInitialLoading = false;
				uni.showToast({
					title: '视频ID不存在',
					icon: 'none'
				});
			}
			
			// 获取当前页面URL用于分享
			this.getCurrentPageUrl();
		},
		onShow() {
			console.log('页面显示，恢复资源');
			// 如果之前是暂停状态，则恢复
			if (this.isPaused) {
				this.resumeAllResources();
				this.isPaused = false;
			}
		},
		onHide() {
			console.log('页面隐藏，暂停资源');
			// 暂停资源，而不是清理
			this.pauseAllResources();
			this.isPaused = true;
			
			// 保存当前播放进度
			this.savePlaybackTime();
			
			// 隐藏分享菜单
			this.$nextTick(function() {
				uniShare.hide();
			});
			
			return uniShare.isShow;
		},
		onUnload() {
			console.log('页面卸载，清理资源');
			// 完全清理所有定时器和心跳
			this.clearAllTimers();
			
			// 保存当前播放进度
			this.savePlaybackTime();
			
			// 恢复竖屏锁定
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('portrait-primary');
			// #endif
			
			// 隐藏分享菜单
			this.$nextTick(function() {
				uniShare.hide();
			});
			
			// 移除窗口大小监听
			uni.offWindowResize(this.windowResizeCallback);
			
			return uniShare.isShow;
		},
		onBackPress({from}) {
			console.log('返回按钮按下，清理资源');
			// 停止所有定时器和心跳
			this.clearAllTimers();
			
			// 保存当前播放进度
			this.savePlaybackTime();
			
			if (from == 'navigateBack') {
				this.$nextTick(function() {
					uniShare.hide();
				});
				return uniShare.isShow;
			}
		},
		mounted() {
			// 设置自动隐藏控制栏的定时器
			this.startAutoHideControls();
		},
		beforeDestroy() {
			console.log('组件销毁，清理资源');
			// 停止所有定时器和心跳
			this.clearAllTimers();
			
			// 保存当前播放进度
			this.savePlaybackTime();
			
			// 移除监听
			uni.offWindowResize(this.windowResizeCallback);
		},
		methods: {
			play(){
				this.$refs.cLottieRef.call('play')
			},
			// 开启视频心跳功能
			startHeartbeat() {
				// 先清除可能存在的定时器
				this.stopHeartbeat();
				
				// 如果没有视频ID，不启动心跳
				if (!this.videoId) return;
				
				// 先执行一次心跳
				this.sendHeartbeat();
				
				// 设置定时器，每隔指定时间发送一次心跳
				this.heartbeatTimer = setInterval(() => {
					this.sendHeartbeat();
				}, this.heartbeatInterval * 1000); // 转换为毫秒
			},
			
			// 停止心跳功能
			stopHeartbeat() {
				if (this.heartbeatTimer) {
					clearInterval(this.heartbeatTimer);
					this.heartbeatTimer = null;
					console.log('心跳定时器已清理');
				}
			},
			
			// 发送心跳请求
			sendHeartbeat() {
				this.syghttp.ajax({
					url: this.syghttp.api.liveVideoHeartbeat,
					method: 'GET',
					data: {
						liveId: this.videoId,
						time: this.heartbeatInterval,
						companyId: this.companyId
					},
					success: (res) => {
						console.log('心跳成功:', res);
					},
					fail: (err) => {
						console.error('心跳失败:', err);
					}
				});
			},
			
			// 开始记录播放时间
			startTrackingPlayTime() {
				// 先清除可能存在的定时器
				this.stopTrackingPlayTime();
				
				// 设置定时器，每秒更新当前播放时间
				this.videoPlayTimer = setInterval(() => {
					if (this.isPlaying) {
						// 更新计时变量
						this.currentPlayTime++;
						
						// 每5秒自动更新一次播放时间到存储
						if (this.currentPlayTime % 5 === 0) {
							this.savePlaybackTime();
						}
					}
				}, 1000);
			},
			
			// 停止记录播放时间
			stopTrackingPlayTime() {
				if (this.videoPlayTimer) {
					clearInterval(this.videoPlayTimer);
					this.videoPlayTimer = null;
					console.log('播放时间跟踪定时器已清理');
				}
			},
			
			// 保存当前播放进度到本地存储
			savePlaybackTime() {
				if (!this.videoId) return;
				
				// 获取视频播放器当前的播放时间
				const videoPlayer = this.$refs.domVideoPlayer;
				let currentTime = 0;
				
				// 直接从视频播放器获取时间，这是最准确的方式
				if (videoPlayer && videoPlayer.getCurrentTime) {
					currentTime = Math.floor(videoPlayer.getCurrentTime() || 0);
				} else {
					// 如果无法获取播放器时间，则使用计时变量
					currentTime = this.currentPlayTime;
				}
				
				console.log('保存播放进度:', currentTime);
				
				// 仅当播放时间大于5秒时才保存
				if (currentTime > 5) {
					try {
						// 存储格式：videoPlaybackTime_视频ID
						const key = `videoPlaybackTime_${this.videoId}`;
						const data = {
							time: currentTime,
							timestamp: Date.now(),
							videoName: this.videoInfo.name || '',
							videoThumbnail: this.videoInfo.videoThumbnailUrl || '',
							videoAuthor: this.videoInfo.author || ''
						};
						uni.setStorageSync(key, JSON.stringify(data));
						console.log('保存播放进度成功:', currentTime);
					} catch (e) {
						console.error('保存播放进度失败:', e);
					}
				}
			},
			
			// 检查是否有上次观看记录
			checkLastPlaybackTime() {
				if (!this.videoId) return;
				
				try {
					const key = `videoPlaybackTime_${this.videoId}`;
					const data = uni.getStorageSync(key);
					
					if (data) {
						const playbackData = JSON.parse(data);
						// 判断记录是否是在7天内的，超过7天则不提示恢复
						const now = Date.now();
						const recordTime = playbackData.timestamp || 0;
						const daysDiff = (now - recordTime) / (1000 * 60 * 60 * 24);
						
						// 如果观看记录在7天内，并且观看时长超过10秒，显示恢复提示
						if (daysDiff <= 7 && playbackData.time > 10) {
							this.lastPlayTime = playbackData.time;
							console.log('发现上次播放记录:', this.lastPlayTime, '秒');
							
							// 延迟显示恢复弹窗，确保视频加载完成
							setTimeout(() => {
								this.showResumePopup = true;
							}, 1500);
						}
					}
				} catch (e) {
					console.error('获取播放进度失败:', e);
				}
			},
			
			// 恢复上次播放 - 修改计时逻辑
			resumePlayback() {
				this.showResumePopup = false;
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer && videoPlayer.toSeek) {
					// 设置当前播放时间为上次播放时间，确保继续计时
					this.currentPlayTime = this.lastPlayTime;
					
					// 跳转到上次播放位置
					videoPlayer.toSeek(this.lastPlayTime);
					setTimeout(() => {
						videoPlayer.play();
					}, 100);
					
					console.log('从上次位置继续播放，当前时间设置为:', this.currentPlayTime);
				}
			},
			
			// 从头开始播放 - 确保重置计时器
			startFromBeginning() {
				this.showResumePopup = false;
				// 重置播放时间
				this.currentPlayTime = 0;
				
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer && videoPlayer.toSeek) {
					videoPlayer.toSeek(0);
					setTimeout(() => {
						videoPlayer.play();
					}, 100);
					
					console.log('从头开始播放，当前时间重置为0');
				}
			},
			
			// 生成模拟评论数据
			generateMockComments() {
				const names = ['小明同学', '快乐星球', '阳光下的微笑', '云端漫步', '星辰大海', '未来可期', '热爱生活', '读书郎'];
				
				const contents = [
					'这个视频太精彩了！',
					'学到了很多知识，感谢分享',
					'能出一期详细教程吗？',
					'支持原创，继续加油！',
					'期待更多优质内容',
					'非常实用的信息，收藏了',
					'讲解很清晰，容易理解',
					'第一次看这类视频，被吸引了',
					'这个主题很有意思，想了解更多',
					'内容很专业，学习了'
				];
				
				const times = ['刚刚', '1分钟前', '5分钟前', '10分钟前', '30分钟前', '1小时前', '2小时前', '今天', '昨天'];
				
				// 随机生成5-10条评论
				const count = Math.floor(Math.random() * 6) + 5;
				for (let i = 0; i < count; i++) {
					this.commentList.push({
						userName: names[Math.floor(Math.random() * names.length)],
						content: contents[Math.floor(Math.random() * contents.length)],
						time: times[Math.floor(Math.random() * times.length)],
						likes: Math.floor(Math.random() * 100),
						isLiked: Math.random() > 0.7
					});
				}
				
				// 按时间排序
				this.commentList.sort((a, b) => {
					const timeOrder = ['刚刚', '1分钟前', '5分钟前', '10分钟前', '30分钟前', '1小时前', '2小时前', '今天', '昨天'];
					return timeOrder.indexOf(a.time) - timeOrder.indexOf(b.time);
				});
			},
			
			// 格式化数字（1000 -> 1k）
			formatCount(count) {
				if (count < 1000) return count.toString();
				if (count < 10000) return (count / 1000).toFixed(1) + 'k';
				return (count / 10000).toFixed(1) + 'w';
			},
			
			// 格式化视频时长
			formatDuration(seconds) {
				if (!seconds) return '00:00';
				
				const minutes = Math.floor(seconds / 60);
				const remainingSeconds = Math.floor(seconds % 60);
				
				return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
			},
			
			// 播放相关视频
			playRelatedVideo(item) {
				// 关闭推荐抽屉
				this.isRecommendOpen = false;
				// 重置播放结束状态
				this.isPlayEnded = false;
				// 重置播放状态
				this.isInitialLoading = true;
				this.isPlaying = false;
				this.hasShownErrorToast = false;
				
				// 更新视频信息
				this.videoInfo = {
					id: item.id,
					name: item.name,
					url: item.url,
					videoThumbnailUrl: item.videoThumbnailUrl,
					author: item.author || '项目负责人',
					businessName: item.description
				};
				
				this.videoId = item.id;
				
				// 当前播放视频更新后，需要更新相关视频列表
				if (this.relatedVideos && this.relatedVideos.length > 0) {
					// 过滤出当前视频之外的视频作为相关推荐
					this.relatedVideos = this.relatedVideos.filter(video => video.id !== item.id);
				}
				
				// 如果相关视频列表为空或者数量太少，从API获取更多
				if (!this.relatedVideos || this.relatedVideos.length < 3) {
					// this.getRelatedVideos();
				}
				
				// 延迟关闭加载状态
				setTimeout(() => {
					this.isInitialLoading = false;
					
					// 确保视频播放
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && videoPlayer.play) {
						videoPlayer.play();
					}
				}, 500);
			},
			
			// 视频播放事件
			onVideoPlay() {
				this.isPlaying = true;
				this.isInitialLoading = false;
			},
			
			// 视频暂停事件
			onVideoPause() {
				this.isPlaying = false;
			},
			
			// 视频播放结束事件
			onVideoEnded() {
				// 显示播放结束提示
				this.isPlayEnded = true;
				this.isPlaying = false;
			},
			
			// 视频播放错误事件
			onVideoError(e) {
				console.error('Video Error:', e);
				// 如果视频实际正在播放，就不弹出错误提示
				if (this.isPlaying) return;
				
				// 避免多次显示错误提示
				if (!this.hasShownErrorToast) {
					this.hasShownErrorToast = true;
					this.isInitialLoading = false;
					
					// 尝试重新播放视频
					setTimeout(() => {
						const videoPlayer = this.$refs.domVideoPlayer;
						if (videoPlayer && videoPlayer.play) {
							videoPlayer.play();
						}
					}, 1000);
				}
			},
			
			// 切换播放/暂停
			togglePlay() {
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer) {
					if (this.isPlaying) {
						videoPlayer.pause();
					} else {
						videoPlayer.play();
					}
				}
			},
			
			// 返回上一页
			goBack() {
				// 完全清理所有资源
				this.clearAllTimers();
				
				// 保存播放进度
				this.savePlaybackTime();
				
				// 返回上一页
				uni.navigateBack();
			},
			
			// 切换推荐抽屉
			toggleRecommend() {
				this.isRecommendOpen = !this.isRecommendOpen;
				// 确保视频继续播放
				setTimeout(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
						videoPlayer.play();
					}
				}, 50);
			},
			
			// 切换点赞状态
			toggleLike() {
				this.isLiked = !this.isLiked;
				if (this.isLiked) {
					this.likeCount++;
					uni.showToast({
						title: '点赞成功',
						icon: 'none'
					});
				} else {
					this.likeCount--;
				}
				// 确保视频继续播放
				if (!this.isPlaying && !this.isPlayEnded) {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer) {
						videoPlayer.play();
					}
				}
			},
			
			// 切换关注状态
			toggleFollow() {
				this.isFollowed = !this.isFollowed;
				uni.showToast({
					title: this.isFollowed ? '关注成功' : '已取消关注',
					icon: 'none'
				});
				// 确保视频继续播放
				if (!this.isPlaying && !this.isPlayEnded) {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer) {
						videoPlayer.play();
					}
				}
			},
			
			// 显示评论
			showComments() {
				this.showCommentPopup = true;
				// 确保视频继续播放
				setTimeout(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
						videoPlayer.play();
					}
				}, 50);
			},
			
			// 关闭评论
			closeComments() {
				this.showCommentPopup = false;
				// 确保视频继续播放
				setTimeout(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
						videoPlayer.play();
					}
				}, 50);
			},
			
			// 点赞评论
			likeComment(index) {
				if (!this.commentList[index]) return;
				
				this.commentList[index].isLiked = !this.commentList[index].isLiked;
				if (this.commentList[index].isLiked) {
					this.commentList[index].likes++;
				} else {
					this.commentList[index].likes--;
				}
			},
			
			// 发送评论
			sendComment() {
				if (!this.commentText.trim()) return;
				
				// 添加新评论到列表
				this.commentList.unshift({
					userName: '我', // 用户名
					content: this.commentText,
					time: '刚刚',
					likes: 0,
					isLiked: false
				});
				
				// 增加评论计数
				this.commentCount++;
				
				// 清空输入框
				this.commentText = '';
				
				// 提示成功
				uni.showToast({
					title: '评论成功',
					icon: 'none'
				});
			},
			
			// 分享视频
			shareVideo() {
				// #ifdef H5
				this.showSharePopup = true;
				// #endif
				
				// #ifdef APP-PLUS
				uniShare.show({
					content: { //公共的分享参数配置
						type: 0,
						href: 'https://bxcs.boxuehao.cn/app/',
						// href: this.currentUrl,
						title: this.videoInfo.name || '精彩直播内容',
						summary: `来自${this.videoInfo.author || '项目负责人'}的精彩直播`,
						imageUrl:'/static/app-plus/sharemenu/logo.png'
					},
					menus: [{
							"img": "/static/app-plus/sharemenu/wechatfriend.png",
							"text": "微信好友",
							"share": {
								"provider": "weixin",
								"scene": "WXSceneSession"
							}
						},
						{
							"img": "/static/app-plus/sharemenu/hp.png",
							"text": "海报",
							"share": {
								"provider": "weixin",
								"scene": "WXSceneSession",
								"type": 2,
								"imageUrl": this.poserPath || 'https://bxcs.boxuehao.cn/bxcs/static/image/erweima.png'
							}
						},
						{
							"img": "/static/app-plus/sharemenu/wechatmoments.png",
							"text": "微信朋友圈",
							"share": {
								"provider": "weixin",
								"scene": "WXSceneTimeline"
							}
						},
						{
							"img": "/static/app-plus/sharemenu/copyurl.png",
							"text": "复制",
							"share": "copyurl"
						},
						{
							"img": "/static/app-plus/sharemenu/more.png",
							"text": "更多",
							"share": "shareSystem"
						}
					],
					cancelText: "取消分享",
				}, e => {
					console.log(e);
					// 确保视频继续播放
					setTimeout(() => {
						const videoPlayer = this.$refs.domVideoPlayer;
						if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
							videoPlayer.play();
						}
					}, 50);
				});
				// #endif
			},
			
			// 获取当前页面完整URL
			getCurrentPageUrl() {
				// #ifdef H5
				this.currentUrl = window.location.href;
				// #endif
				
				// #ifdef APP-PLUS
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				const route = currentPage.route;
				const options = currentPage.options;
				
				// 构建查询字符串
				let queryString = '';
				if (Object.keys(options).length > 0) {
					queryString = '?' + Object.keys(options)
						.map(key => `${key}=${options[key]}`)
						.join('&');
				}
				
				// 获取当前域名
				plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
					const domain = widgetInfo.origin || this.http.poserUrl;
					this.currentUrl = `${domain}/${route}${queryString}`;
				});
				// #endif
			},
			
			// 复制链接 (H5)
			copyLink() {
				// #ifdef H5
				uni.setClipboardData({
					data: window.location.href,
					success: () => {
						uni.showToast({
							title: '链接已复制',
							icon: 'success'
						});
						this.showSharePopup = false;
					}
				});
				// #endif
			},
			
			// 关闭H5分享弹窗
			closeSharePopup() {
				this.showSharePopup = false;
				// 确保视频继续播放
				setTimeout(() => {
					const videoPlayer = this.$refs.domVideoPlayer;
					if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
						videoPlayer.play();
					}
				}, 50);
			},
			closeResumePopup(){
				this.showResumePopup=false
			},
			// 重播视频
			replayVideo() {
				this.isPlayEnded = false;
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer && videoPlayer.toSeek) {
					videoPlayer.toSeek(0);
					setTimeout(() => {
						videoPlayer.play();
					}, 100);
				}
			},
			
			// 播放下一个视频
			playNextVideo() {
				if (this.relatedVideos && this.relatedVideos.length > 0) {
					// 随机选择一个视频播放，增加用户体验多样性
					const randomIndex = Math.floor(Math.random() * this.relatedVideos.length);
					const nextVideo = this.relatedVideos[randomIndex];
					
					// 播放选中的视频
					this.playRelatedVideo(nextVideo);
					
					// 从推荐列表中移除已播放的视频
					this.relatedVideos = this.relatedVideos.filter((_, index) => index !== randomIndex);
				} else {
					// 如果没有推荐视频，尝试重新获取
					// this.getRelatedVideos();
					uni.showToast({
						title: '正在获取更多视频...',
						icon: 'none'
					});
				}
			},
			
			// 窗口大小变化回调
			windowResizeCallback(res) {
				// 记录窗口尺寸
				this.windowWidth = res.size.windowWidth;
				this.windowHeight = res.size.windowHeight;
				
				// 检测横竖屏
				this.checkOrientation();
			},
			
			// 检测屏幕方向
			checkOrientation() {
				// 获取当前窗口大小
				const info = uni.getSystemInfoSync();
				this.windowWidth = info.windowWidth;
				this.windowHeight = info.windowHeight;
				
				// 判断是否为横屏模式（宽度大于高度）
				const isLandscapeNow = this.windowWidth > this.windowHeight;
				
				// 状态有变化时才处理
				if (this.isLandscape !== isLandscapeNow) {
					this.isLandscape = isLandscapeNow;
					
					if (this.isLandscape) {
						// 横屏模式
						this.isControlsVisible = true;
						// 横屏模式下，5秒后自动隐藏控制栏
						this.startAutoHideControls();
					} else {
						// 竖屏模式
						this.isControlsVisible = true;
						// 竖屏模式下，清除自动隐藏计时器
						this.clearAutoHideControls();
						
						// 如果视频是横屏尺寸，但手机是竖屏，显示提示
						if (this.videoIsLandscape) {
							this.showRotationHint();
						}
					}
					
					// 确保视频继续播放
					this.$nextTick(() => {
						const videoPlayer = this.$refs.domVideoPlayer;
						if (videoPlayer && !this.isPlaying && !this.isPlayEnded) {
							videoPlayer.play();
						}
					});
				}
			},
			
			// 开始自动隐藏控制栏的计时器 - 同时应用于竖屏和横屏模式
			startAutoHideControls() {
				this.clearAutoHideControls();
				this.isControlsVisible = true;
				this.controlsTimer = setTimeout(() => {
					this.isControlsVisible = false;
				}, 5000); // 5秒后自动隐藏
			},
			
			// 清除自动隐藏控制栏的计时器
			clearAutoHideControls() {
				if (this.controlsTimer) {
					clearTimeout(this.controlsTimer);
					this.controlsTimer = null;
					console.log('控制栏隐藏定时器已清理');
				}
				
				if (this.landscapeTimer) {
					clearTimeout(this.landscapeTimer);
					this.landscapeTimer = null;
					console.log('横屏控制栏定时器已清理');
				}
			},
			
			// 触摸屏幕时显示控制栏
			showControlsOnTouch() {
				this.isControlsVisible = true;
				this.startAutoHideControls(); // 重新开始计时
			},
			
			// 点击视频区域时切换控制栏显示状态
			toggleControlsVisible() {
				// 如果推荐列表抽屉打开，则不触发控制栏显示/隐藏
				if (this.isRecommendOpen) return;
				
				this.isControlsVisible = !this.isControlsVisible;
				if (this.isControlsVisible) {
					this.startAutoHideControls();
				}
			},
			
			// 显示旋转提示
			showRotationHint() {
				// 显示旋转提示
				this.showRotateHint = true;
				
				// 4秒后自动隐藏
				setTimeout(() => {
					this.showRotateHint = false;
				}, 4000);
			},
			
			// 添加一个统一的资源清理方法
			clearAllTimers() {
				// 停止心跳
				this.stopHeartbeat();
				
				// 停止记录播放时间
				this.stopTrackingPlayTime();
				
				// 清除控制栏自动隐藏定时器
				this.clearAutoHideControls();
				
				// 确保没有遗漏的定时器
				if (this.heartbeatTimer) {
					clearInterval(this.heartbeatTimer);
					this.heartbeatTimer = null;
				}
				
				if (this.videoPlayTimer) {
					clearInterval(this.videoPlayTimer);
					this.videoPlayTimer = null;
				}
				
				if (this.controlsTimer) {
					clearTimeout(this.controlsTimer);
					this.controlsTimer = null;
				}
				
				if (this.landscapeTimer) {
					clearTimeout(this.landscapeTimer);
					this.landscapeTimer = null;
				}
				
				console.log('所有定时器已清理');
			},
			
			// 添加暂停所有资源的方法
			pauseAllResources() {
				console.log('暂停所有资源');
				
				// 暂停视频播放
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer && videoPlayer.pause) {
					videoPlayer.pause();
				}
				
				// 暂停心跳，但不清除定时器对象
				if (this.heartbeatTimer) {
					clearInterval(this.heartbeatTimer);
					// 不设置为null，保留定时器引用以便恢复
				}
				
				// 暂停播放时间跟踪
				if (this.videoPlayTimer) {
					clearInterval(this.videoPlayTimer);
					// 不设置为null，保留定时器引用以便恢复
				}
				
				// 暂停控制栏自动隐藏
				this.clearAutoHideControls();
			},
			
			// 添加恢复所有资源的方法
			resumeAllResources() {
				console.log('恢复所有资源');
				
				// 恢复视频播放
				const videoPlayer = this.$refs.domVideoPlayer;
				if (videoPlayer && videoPlayer.play && this.isPlaying) {
					videoPlayer.play();
				}
				
				// 恢复心跳
				this.startHeartbeat();
				
				// 恢复播放时间跟踪
				this.startTrackingPlayTime();
				
				// 恢复控制栏自动隐藏
				this.startAutoHideControls();
			},
		}
	}
</script>

<style lang="scss" scoped>
	.live-room-container {
		width: 100%;
		// height: 100vh;
		position: relative;
		background-color: #000;
		overflow: hidden;
	}
	
	/* 全屏视频播放器 - 修改为覆盖整个屏幕 */
	.full-video-player {
		width: 100%;
		height: 100vh;
		z-index: 1;
		background-color: #000; 
		transition: all 0.3s ease;
		object-fit: cover; /* 确保视频覆盖整个区域 */
	}
	
	.landscape-video {
		object-position: center;
	}
	
	/* 横屏模式样式调整 */
	@media screen and (orientation: landscape) {
		.full-video-player {
			width: 100vw;
			height: 100vh;
		}
		
		.top-controls {
			padding: 20rpx;
			height: 60rpx;
		}
		
		.live-info {
			margin-top: 0;
			height: 60rpx;
			align-items: center;
		}
		
		.right-controls {
			display: none; /* 横屏模式下隐藏右侧控制栏 */
		}
		
		.live-author-info {
			max-width: 60vw;
		}
		
		.comment-container {
			max-height: 90vh;
		}
		
		.comment-list {
			max-height: 70vh;
		}
		
		.play-btn-center {
			transform: translate(-50%, -50%) scale(0.8);
		}
		
		.recommend-drawer {
			display: none; /* 横屏模式下隐藏推荐抽屉 */
		}
		
		.play-end-tips {
			.play-end-content {
				width: 50%;
			}
		}
	}
	
	/* 顶部控制栏 */
	.top-controls {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		padding: calc(var(--status-bar-height) + 30rpx) 20rpx 20rpx; /* 增加顶部边距 */
		z-index: 10;
		transition: all 0.3s ease;
	}
	
	.landscape-top-controls {
		padding: 20rpx;
		padding-top: 70rpx;
		background-color: rgba(0,0,0,0.85);
		height: 140rpx;
	}
	
	.controls-hidden {
		opacity: 0;
		transform: translateY(-100%);
	}
	
	.gradient-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-image: linear-gradient(to bottom, rgba(0,0,0,0.6), rgba(0,0,0,0));
		z-index: -1;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.live-info {
		margin-top: 40rpx; /* 增加顶部边距 */
		display: flex;
		align-items: center;
		transition: all 0.3s ease;
	}
	
	.live-author-info {
		flex: 1;
		margin-left: 10rpx;
		transition: all 0.3s ease;
	}
	
	.landscape-author-info {
		margin-left: 20rpx;
	}
	
	.live-author-name {
		font-size: 26rpx;
		color: #FFFFFF;
		font-weight: bold;
		margin-bottom: 6rpx;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
	}
	
	.live-title {
		font-size: 22rpx;
		color: rgba(255,255,255,0.8);
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
		max-width: 400rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.follow-btn {
		padding: 8rpx 24rpx;
		background-color: #FF5F5F;
		color: #FFFFFF;
		font-size: 22rpx;
		border-radius: 30rpx;
		font-weight: bold;
	}
	
	/* 右侧控制栏 - 添加隐藏动画 */
	.right-controls {
		position: absolute;
		right: 20rpx;
		bottom: 300rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		z-index: 10;
		transition: all 0.3s ease;
		
		&.controls-hidden {
			opacity: 0;
			transform: translateX(100%);
		}
	}
	
	.control-icon-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 40rpx;
		
		text {
			margin-top: 6rpx;
			font-size: 22rpx;
			color: #FFFFFF;
			text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
		}
		
		.icon-wrapper {
			width: 70rpx;
			height: 70rpx;
			border-radius: 50%;
			background-color: rgba(0, 0, 0, 0.3);
			display: flex;
			justify-content: center;
			align-items: center;
			
			&.active {
				background-color: rgba(255, 95, 95, 0.2);
				color: #FF5F5F;
			}
		}
	}
	
	/* 播放按钮 */
	.play-btn-center {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 10;
		
		.play-btn-icon {
			width: 110rpx;
			height: 110rpx;
			border-radius: 50%;
			background-color: rgba(0, 0, 0, 0.5);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	
	/* 推荐视频抽屉样式修改 */
	.recommend-drawer {
		position: absolute;
		right: -80vw;
		top: 0;
		width: 80vw;
		height: 100vh;
		background-color: #121212;
		z-index: 30;
		transition: transform 0.3s ease-out;
		display: flex;
		flex-direction: column;
	}
	
	.drawer-open {
		transform: translateX(-100%);
	}
	
	.drawer-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #333333;
	}
	
	.drawer-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #FFFFFF;
	}
	
	.drawer-close {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.recommend-list {
		flex: 1;
		padding: 0 30rpx;
	}
	
	.recommend-item {
		display: flex;
		flex-direction: column;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #333333;
		position: relative;
	}
	
	.recommend-cover-container {
		width: 100%;
		height: 204rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 12rpx;
	}
	
	.recommend-cover {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.recommend-info {
		width: 100%;
	}
	
	.recommend-name {
		font-size: 28rpx;
		color: #FFFFFF;
		margin-bottom: 8rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	
	.recommend-meta {
		display: flex;
		justify-content: space-between;
		font-size: 22rpx;
		color: #999999;
	}
	
	/* 评论弹窗 */
	.comment-container {
		background-color: #121212;
		border-radius: 30rpx 30rpx 0 0;
		max-height: 70vh;
		display: flex;
		flex-direction: column;
	}
	
	.comment-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #333333;
	}
	
	.comment-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #FFFFFF;
	}
	
	.comment-close {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.comment-list {
		flex: 1;
		max-height: 50vh;
		padding: 0 30rpx;
	}
	
	.comment-item {
		display: flex;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #333333;
	}
	
	.comment-content {
		flex: 1;
		overflow: hidden;
	}
	
	.comment-user {
		font-size: 26rpx;
		font-weight: bold;
		color: #FFFFFF;
		margin-bottom: 8rpx;
	}
	
	.comment-text {
		font-size: 26rpx;
		color: #CCCCCC;
		line-height: 1.4;
		margin-bottom: 8rpx;
	}
	
	.comment-time {
		font-size: 22rpx;
		color: #666666;
	}
	
	.comment-like {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-left: 30rpx;
		
		text {
			font-size: 22rpx;
			color: #666666;
			margin-top: 6rpx;
			
			&.liked {
				color: #FF5F5F;
			}
		}
	}
	
	.comment-input-area {
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		border-top: 1rpx solid #333333;
	}
	
	.comment-input-box {
		flex: 1;
		background-color: #222222;
		border-radius: 40rpx;
		padding: 15rpx 30rpx;
		
		input {
			width: 100%;
			height: 60rpx;
			font-size: 26rpx;
			color: #FFFFFF;
		}
	}
	
	.comment-send-btn {
		width: 120rpx;
		height: 60rpx;
		margin-left: 20rpx;
		background-color: #333333;
		border-radius: 30rpx;
		color: #CCCCCC;
		font-size: 26rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		
		&.active {
			background-color: #00C1CC;
			color: #FFFFFF;
		}
	}
	
	/* 空状态 */
	.empty-recommend, .empty-comment {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 100rpx 0;
	}
	
	.empty-text {
		font-size: 26rpx;
		color: #666666;
		margin-top: 20rpx;
	}
	
	/* 加载层 */
	.loading-layer {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		background-color: rgba(0,0,0,1);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 40;
	}
	
	/* 播放结束提示 - 改进UI设计 */
	.play-end-tips {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		background-color: rgba(0,0,0,0.8);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 30;
	}
	
	.play-end-content {
		background-color: rgba(25,25,25,0.95);
		border-radius: 20rpx;
		padding: 40rpx;
		width: 80%;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.5);
	}
	
	.play-end-title {
		font-size: 32rpx;
		color: #FFFFFF;
		margin-bottom: 30rpx;
		text-align: center;
		font-weight: bold;
	}
	
	.play-end-thumbnail {
		width: 100%;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 30rpx;
		position: relative;
		
		image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
		
		.play-end-thumbnail-mask {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0,0,0,0.4);
		}
	}
	
	.play-end-btns {
		display: flex;
		justify-content: space-around;
		width: 100%;
	}
	
	.play-end-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 16rpx 20rpx;
		border-radius: 10rpx;
		margin: 0 10rpx;
		
		text {
			margin-top: 8rpx;
			font-size: 24rpx;
			color: #FFFFFF;
		}
	}
	
	.replay {
		background-color: rgba(50,50,50,0.9);
	}
	
	.next {
		background-color: rgba(0,193,204,0.9);
	}
	
	.back {
		background-color: rgba(212,48,48,0.9);
	}
	
	.share-popup {
		padding: 30rpx;
		background-color: #121212;
	}
	
	.share-title {
		text-align: center;
		font-size: 28rpx;
		color: #CCCCCC;
		margin-bottom: 30rpx;
	}
	
	.share-options {
		display: flex;
		justify-content: space-around;
		padding: 20rpx 0 40rpx;
	}
	
	.share-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.share-item image {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 16rpx;
	}
	
	.share-item text {
		font-size: 24rpx;
		color: #FFFFFF;
	}
	
	.share-cancel {
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 28rpx;
		color: #FFFFFF;
		border-top: 1rpx solid #333333;
	}
	
	.share-cancel:active {
		background-color: #333333;
	}
	
	.landscape-info {
		margin-top: 0;
	}
	
	/* 横屏提示动画 */
	.rotate-toast {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 9999;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.7);
		border-radius: 24rpx;
		padding: 40rpx;
		width: 300rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.5);
		animation: fadeInOut 4s ease-in-out;
	}
	
	.rotate-text {
		color: white;
		font-size: 28rpx;
		margin-top: 30rpx;
		text-align: center;
	}
	
	@keyframes fadeInOut {
		0% { opacity: 0; }
		20% { opacity: 1; }
		80% { opacity: 1; }
		100% { opacity: 0; }
	}
	
	.rotate-animation {
		width: 160rpx;
		height: 160rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.phone-container {
		width: 100rpx;
		height: 100rpx;
		animation: rotatePhone 2s infinite ease-in-out;
		transform-style: preserve-3d;
	}
	
	@keyframes rotatePhone {
		0% {
			transform: rotateZ(0deg);
		}
		15% {
			transform: rotateZ(0deg);
		}
		30% {
			transform: rotateZ(90deg);
		}
		70% {
			transform: rotateZ(90deg);
		}
		85% {
			transform: rotateZ(0deg);
		}
		100% {
			transform: rotateZ(0deg);
		}
	}
	
	.phone {
		width: 80rpx;
		height: 140rpx;
		background-color: #333;
		border-radius: 16rpx;
		position: relative;
		transform: translateY(-20rpx);
		border: 2rpx solid #999;
		box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.3);
	}
	
	.phone-inner {
		position: absolute;
		top: 10rpx;
		left: 10rpx;
		right: 10rpx;
		bottom: 10rpx;
		background-color: #00C1CC;
		border-radius: 6rpx;
	}
	
	.phone-button {
		position: absolute;
		bottom: 6rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 24rpx;
		height: 24rpx;
		border-radius: 50%;
		background-color: #222;
		border: 1rpx solid #999;
	}
	
	/* 恢复观看提示弹窗样式 - 修复样式问题 */
	.resume-container {
		height:100%;
		background-color: #1E1E1E;
		padding: 40rpx 30rpx;
		width: 560rpx;
		border-radius: 20rpx;
		border: 1rpx solid #333333;
	}
	
	.resume-title {
		color: #FFFFFF;
		font-size: 34rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.resume-content {
		padding: 20rpx 0;
	}
	
	.resume-time {
		color: #00C1CC;
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20rpx;
	}
	
	.resume-desc {
		color: #CCCCCC;
		font-size: 28rpx;
		text-align: center;
	}
	
	.resume-btns {
		display: flex;
		justify-content: space-between;
		margin-top: 40rpx;
	}
	
	.resume-btn {
		width: 240rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 40rpx;
		font-size: 28rpx;
	}
	
	.resume-btn.cancel {
		background-color: #333333;
		color: #CCCCCC;
		border: 1rpx solid #444444;
	}
	
	.resume-btn.confirm {
		background-color: #00C1CC;
		color: #FFFFFF;
	}
</style> 