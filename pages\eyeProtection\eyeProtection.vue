<template>
	<view style="position: relative;height: 100%;">
		<view v-if="loading">
			<x-skeleton type="banner" :loading="true">
				<view></view>
			</x-skeleton>
			<x-skeleton type="menu" :loading="true">
				<view></view>
			</x-skeleton>
			<x-skeleton type="waterfall" :loading="true" :configs="{
					gridColumns: 4,
					headHeight: '200rpx',
					textRows: 1,
					gridRows:1
				}">
				<view></view>
			</x-skeleton>
			<x-skeleton type="waterfall" :loading="true" :configs="{
					gridColumns: 1,
					headHeight: '200rpx',
					textRows: 1,
					gridRows:3,
					textShow:false
				}">
				<view></view>
			</x-skeleton>
			<x-skeleton type="list" :loading="true">
				<view></view>
			</x-skeleton>
		</view>
		<view style="">
			<view style="">
				<image src="../../static/image/<EMAIL>" mode="aspectFill" style="width: 100%;height: 125upx;"></image>
			</view>
			<view style="padding-bottom: 120upx;">
				<view class="u-page__item">
					<u-collapse accordion :border='false'>
						<u-collapse-item 
							v-for="(item, index) in protocolList" 
							:key="index"
							:title="item.title"
						>
							
							<text class="u-collapse-content" v-html="formatRichText(item.content)"></text>
						</u-collapse-item>
						
					</u-collapse>
					
				</view>
			</view>
		</view>
	
		<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
			:loading="loading"></u-loading-page>
		<!-- 加载页 -->
	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				loading: true,
				protocolList: []
			}
		},
		onLoad() {
			that = this
			this.getProtocolList()
		},
		methods: {
			formatRichText(html) {
				let newContent = html.replace(/<img[^>]*>/gi, function(match, capture) {
					match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
					match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
					match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
					return match;
				});
				newContent = newContent.replace(/style="[^"]+"/gi, function(match, capture) {
					match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi,
						'max-width:100%;');
					return match;
				});
				newContent = newContent.replace(/<br[^>]*\/>/gi, '');
				newContent = newContent.replace(/\<img/gi, '<img style="max-width:99%;"');
				return newContent;
			},
			// 获取护眼攻略列表
			async getProtocolList() {
				try {
					const res = await that.http.ajax({
						url: that.http.api.protocolgetByType,
						method: 'GET',
						data: {
							type: 3
						}
					})
					
					if(res.code === 0) {
						this.protocolList = res.data || []
					} else {
						this.showError(res.msg || '获取护眼攻略失败')
					}
				} catch(err) {
					this.showError('获取护眼攻略失败')
					console.error('获取护眼攻略失败:', err)
				} finally {
					this.loading = false
				}
			},
			
			// 错误提示
			showError(message) {
				uni.showToast({
					title: message,
					icon: 'none'
				})
			}
		}
	}
</script>

<style>
	page,
	body {
		/* background-image: linear-gradient(180deg, #00C1CC, #f3f3f3); */
		background-size: 100% 30%;
		height: 100%;
	}
</style>

<style lang="scss" scoped>
	.u-page__item {
		background-color: #fff;
		// border-radius: 20upx;
		overflow: hidden;
		.u-collapse-content {
			line-height: 2.0;
			// text-indent: 2em;
		}
	}
	image {
		  vertical-align: middle !important;
		}
</style>