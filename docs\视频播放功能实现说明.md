# 视频播放功能实现说明

## 功能概述

为课程详情页面新增了视频播放功能，用户点击课时的"去观看"按钮时，会跳转到专门的视频播放页面进行观看。

## 实现方案

### 1. 文件结构

```
pages/hall/
├── videoPlayer.vue         # 完整版播放页面（使用阿里云播放器SDK）
├── videoPlayerSimple.vue   # 简化版播放页面（用于测试和演示）
└── hall.vue               # 大讲堂首页

pages/courseDetail/
└── courseDetail.vue        # 课程详情页面（修改了跳转逻辑）

docs/
└── 视频播放功能实现说明.md  # 本文档
```

### 2. 核心流程

#### 2.1 用户操作流程
1. 用户在课程详情页面查看课时列表
2. 点击某个课时的"去观看"按钮
3. 页面跳转到视频播放页面
4. 自动调用 `getVideoPlayAuth` 接口获取播放授权
5. 使用阿里云播放器播放视频

#### 2.2 技术实现流程
1. **参数传递**：课时ID、标题、时长、热度等信息
2. **授权获取**：调用后端接口获取播放授权
3. **播放器初始化**：使用阿里云播放器SDK
4. **事件处理**：播放、暂停、结束等事件

### 3. 接口调用

#### 3.1 获取播放授权接口
- **接口地址**：`/api/member/getVideoPlayAuth`
- **请求方法**：POST
- **请求参数**：
  ```json
  {
    "id": "课时ID"
  }
  ```
- **返回数据**：
  ```json
  {
    "code": 1000,
    "data": {
      "videoUrl": "阿里云视频ID",
      "playAuth": "播放授权码"
    }
  }
  ```

### 4. 页面跳转参数

从课程详情页面跳转到播放页面时传递的参数：

```javascript
const params = {
  id: chapter.id,                    // 课时ID（必需）
  title: encodeURIComponent(title),  // 视频标题
  duration: chapter.duration,        // 视频时长（秒）
  heat: chapter.heat                 // 视频热度
};
```

### 5. 阿里云播放器集成

#### 5.1 完整版播放页面 (videoPlayer.vue)
- 使用 renderjs 技术
- 集成阿里云播放器SDK
- 支持播放授权认证
- 完整的播放控制功能

#### 5.2 简化版播放页面 (videoPlayerSimple.vue)
- 用于测试和演示
- 显示获取到的授权信息
- 可以验证接口调用是否正常

### 6. 关键代码片段

#### 6.1 课程详情页面跳转逻辑
```javascript
// 跳转到视频播放页面
goToChapter(chapter) {
  if (!chapter.id) {
    uni.showToast({
      title: '课时信息错误',
      icon: 'none'
    });
    return;
  }
  
  // 构建跳转参数
  const params = {
    id: chapter.id,
    title: encodeURIComponent(chapter.title || '视频播放'),
  };
  
  // 添加可选参数
  if (chapter.duration) {
    params.duration = chapter.duration;
  }
  
  if (chapter.heat) {
    params.heat = chapter.heat;
  }
  
  // 构建URL参数字符串
  const queryString = Object.keys(params)
    .map(key => `${key}=${params[key]}`)
    .join('&');
  
  console.log('跳转到视频播放页面，参数:', params);
  
  uni.navigateTo({
    url: `/pages/hall/videoPlayerSimple?${queryString}`
  });
}
```

#### 6.2 播放授权获取
```javascript
// 获取播放授权
getPlayAuth() {
  if (!this.chapterId) {
    this.showError('缺少课时ID');
    return;
  }
  
  this.loading = true;
  this.error = false;
  
  this.syghttp.ajax({
    url: this.syghttp.api.getVideoPlayAuth,
    method: 'POST',
    data: {
      id: this.chapterId
    },
    success: (res) => {
      console.log('获取播放授权返回:', res);
      this.loading = false;
      
      if (res.code == 1000) {
        if (res.data) {
          this.playAuthData = res.data;
          // 初始化播放器
          this.initPlayer();
        } else {
          this.showError('播放授权数据为空');
        }
      } else {
        this.showError(res.msg || '获取播放授权失败');
      }
    },
    fail: (error) => {
      console.error('获取播放授权失败:', error);
      this.loading = false;
      this.showError('网络请求失败，请检查网络连接');
    }
  });
}
```

### 7. 页面配置

在 `pages.json` 中添加了播放页面的配置：

```json
{
  "path": "pages/hall/videoPlayerSimple",
  "style": {
    "navigationBarTitleText": "视频播放",
    "navigationStyle": "custom"
  }
}
```

### 8. 功能特点

1. **完整的错误处理**：网络错误、授权失败等情况的处理
2. **阿里云错误解析**：专门处理阿里云返回的错误信息
3. **加载状态显示**：获取授权时显示加载动画
4. **参数验证**：确保必需参数存在
5. **用户体验优化**：清晰的错误提示和重试功能
6. **调试信息**：在简化版页面显示详细的授权和错误信息
7. **智能错误映射**：将技术错误码转换为用户友好的提示
8. **建议操作提示**：为不同错误提供相应的解决建议

### 9. 后续优化建议

1. **完善阿里云播放器集成**：确保renderjs正常工作
2. **添加播放记录**：记录用户观看进度
3. **支持全屏播放**：横屏播放体验
4. **添加播放统计**：播放次数、观看时长等
5. **缓存优化**：播放授权的缓存机制

### 10. 阿里云错误处理

#### 10.1 错误处理工具 (utils/aliyunErrorHandler.js)
专门用于处理阿里云视频点播服务的错误响应：

- **统一错误解析**：自动识别和解析阿里云错误格式
- **错误码映射**：将技术错误码转换为用户友好的消息
- **重试判断**：自动判断错误是否可以重试
- **建议操作**：为不同错误提供相应的解决方案

#### 10.2 常见错误处理
- `InvalidVideo.NotFound`: 视频不存在或已被删除
- `Forbidden.IllegalStatus`: 视频状态异常，暂时无法播放
- `InvalidVideo.Disabled`: 视频已被禁用
- `InvalidVideo.NoSource`: 视频源文件不存在
- `Forbidden.Risk`: 视频存在风险，暂时无法播放

#### 10.3 成功响应示例
```json
{
  "code": 1000,
  "msg": "SUCCESS",
  "data": "{\"PlayAuth\":\"eyJ...\",\"VideoMeta\":{\"Status\":\"Normal\",\"VideoId\":\"104493dd6b5a71f0bfd54531858c0102\",\"Title\":\"勃学AI督学机 - 勃学AI督学机介绍\",\"Duration\":237.76,\"CoverURL\":\"http://...\"},\"RequestId\":\"563F2025-9927-58CD-82A3-FD5493E50D37\"}"
}
```

#### 10.4 错误信息示例
```json
{
  "code": 1000,
  "msg": "SUCCESS",
  "data": "{\"RequestId\":\"xxx\",\"Code\":\"InvalidVideo.NotFound\",\"Message\":\"The video does not exist.\"}"
}
```

### 11. 数据格式转换

#### 11.1 阿里云响应数据转换
成功获取播放授权后，系统会自动将阿里云的响应格式转换为标准格式：

**原始格式**：
```json
{
  "PlayAuth": "eyJ...",
  "VideoMeta": {
    "Status": "Normal",
    "VideoId": "104493dd6b5a71f0bfd54531858c0102",
    "Title": "视频标题",
    "Duration": 237.76,
    "CoverURL": "http://..."
  },
  "RequestId": "563F2025-9927-58CD-82A3-FD5493E50D37"
}
```

**转换后格式**：
```json
{
  "videoUrl": "104493dd6b5a71f0bfd54531858c0102",
  "playAuth": "eyJ...",
  "videoMeta": {
    "Status": "Normal",
    "VideoId": "104493dd6b5a71f0bfd54531858c0102",
    "Title": "视频标题",
    "Duration": 237.76,
    "CoverURL": "http://..."
  },
  "requestId": "563F2025-9927-58CD-82A3-FD5493E50D37"
}
```

### 12. 测试方法

1. 进入课程详情页面
2. 点击任意课时的"去观看"按钮
3. 观察是否正确跳转到播放页面
4. 检查是否正确调用了 `getVideoPlayAuth` 接口
5. 查看返回的授权信息或错误信息是否正确显示
6. 在简化版页面点击"🎬 初始化阿里云播放器"按钮
7. 测试错误情况下的重试功能
8. 验证错误提示是否用户友好

### 12. 错误排查

如果遇到播放问题，可以通过简化版播放页面查看详细的调试信息：
- 错误代码和消息
- 阿里云原始错误信息
- 是否可以重试
- 建议的解决操作

这个实现为视频播放功能提供了完整的基础架构和健壮的错误处理机制，可以根据实际需求进一步完善和优化。
