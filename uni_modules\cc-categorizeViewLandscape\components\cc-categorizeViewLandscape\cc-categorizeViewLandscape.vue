<template>
	<view class="content landscape">
		<!-- 右侧内容区域 -->
		<scroll-view scroll-with-animation scroll-y class="right-aside" @scroll="asideScroll"
			:scroll-top="tabScrollTop">
			<view class="t-list">
				<template v-if="tlist && tlist.length > 0">
					<!-- 遍历第二级分类 -->
					<view class="t-item" v-for="titem in tlist" :key="titem.id">
						<!-- 没有children的情况下直接显示二级分类课程 -->
						<template v-if="!titem.children || titem.children.length === 0">
							<!-- 分类标题信息 -->
							<view class="category-title-block" @click.stop="navToList(titem, 1)">
								<view class="category-header">
									<view class="category-title" style="flex-direction: column;align-items: flex-start;">
										<view class="com-fontsize-32 com-color">{{titem.courseClassifyName}}</view>
										<view class="category-meta">{{titem.participantCount || 0}}人参与</view>
									</view>
									<view class="detail_btn dis-ali jc_cen">查看详情</view>
								</view>
							</view>
							
							<!-- 课程内容区 -->
							<view class="course-section">
								<view class="category-header">
									<view class="category-title">
										<view class="category-dot"></view>
										<view class="category-count">共 {{titem.planItems ? titem.planItems.length : 0}} 个课程</view>
									</view>
									<view v-if="titem.planItems && titem.planItems.length > initialDisplayCount"
										class="category-toggle" @click.stop="toggleCourseDisplay(titem.id)">
										{{isExpanded(titem.id) ? '收起' : '查看全部'}}
										<u-icon :name="isExpanded(titem.id) ? 'arrow-up' : 'arrow-down'" size="18" color="#00C1CC"></u-icon>
									</view>
								</view>

								<view class="course-grid" :class="{'expanded': isExpanded(titem.id)}">
									<view v-for="(plan, planIndex) in getDisplayedCourses(titem)" :key="planIndex"
										@click.stop="navToList(plan.id, 2)" class="course-item">
										<image :src="plan.cover || '../../../../static/image/signBack.png'"
											class="course-image" mode="aspectFill"></image>
										<view class="course-info-container">
											<view class="course-name">{{plan.name}}</view>
											<view class="course-meta">
												<view class="course-info" v-if="plan.participantCount">
													<u-icon name="account" size="22" color="#999" class="meta-icon"></u-icon>
													{{plan.participantCount}}人参与
												</view>
												<view class="course-tag" v-if="plan.isHot">热门</view>
											</view>
										</view>
										<view class="course-arrow">
											<u-icon name="arrow-right" size="20" color="#ddd"></u-icon>
										</view>
									</view>

									<!-- 展示更多的提示 -->
									<view
										v-if="!isExpanded(titem.id) && titem.planItems && titem.planItems.length > initialDisplayCount"
										class="more-courses-hint" @click.stop="toggleCourseDisplay(titem.id)">
										<view class="hint-line"></view>
										<view class="hint-text">展开查看全部 {{titem.planItems.length}} 个课程</view>
										<view class="hint-line"></view>
									</view>
								</view>

								<view
									v-if="isExpanded(titem.id) && titem.planItems && titem.planItems.length > initialDisplayCount"
									class="toggle-button-bottom" @click.stop="toggleCourseDisplay(titem.id)">
									<view class="toggle-text">收起</view>
									<view class="toggle-icon">
										<u-icon name="arrow-up" size="20" color="#00C1CC"></u-icon>
									</view>
								</view>
							</view>
						</template>
						
						<!-- 有children的情况下显示三级分类 -->
						<template v-else>
							<!-- 二级分类标题 -->
							<view class="category-title-block">
								<view class="category-header">
									<view class="category-title">
										<view class="com-fontsize-32 com-color">{{titem.courseClassifyName}}</view>
										<view class="category-meta">{{titem.participantCount || 0}}人参与</view>
									</view>
								</view>
							</view>
							
							<!-- 遍历三级分类 -->
							<view class="sub-categories-container">
								<view v-for="sitem in titem.children" :key="sitem.id" class="sub-category">
									<view class="course-section">
										<!-- 分类标题 -->
										<view class="section-title">
											<text>{{sitem.courseClassifyName}}</text>
											<view class="detail_btn dis-ali jc_cen" @click.stop="navToList(sitem, 1)">查看详情</view>
										</view>

										<view class="category-header">
											<view class="category-title">
												<view class="category-dot"></view>
												<view class="category-count">共 {{sitem.planItems ? sitem.planItems.length : 0}} 个课程</view>
											</view>
											<view v-if="sitem.planItems && sitem.planItems.length > initialDisplayCount"
												class="category-toggle" @click.stop="toggleCourseDisplay(sitem.id)">
												{{isExpanded(sitem.id) ? '收起' : '查看全部'}}
												<u-icon :name="isExpanded(sitem.id) ? 'arrow-up' : 'arrow-down'" size="18" color="#00C1CC"></u-icon>
											</view>
										</view>

										<view class="course-grid landscape-grid" :class="{'expanded': isExpanded(sitem.id)}">
											<view v-for="(plan, pindex) in getDisplayedCourses(sitem)" :key="pindex"
												@click.stop="navToList(plan.id, 2)" class="course-item">
												<image :src="plan.cover || '../../../../static/image/signBack.png'"
													class="course-image" mode="aspectFill"></image>
												<view class="course-info-container">
													<view class="course-name">{{plan.name}}</view>
													<view class="course-meta">
														<view class="course-info" v-if="plan.participantCount">
															<u-icon name="account" size="22" color="#999" class="meta-icon"></u-icon>
															{{plan.participantCount}}人参与
														</view>
														<view class="course-tag" v-if="plan.isHot">热门</view>
													</view>
												</view>
												<view class="course-arrow">
													<u-icon name="arrow-right" size="20" color="#ddd"></u-icon>
												</view>
											</view>

											<!-- 展示更多的提示 -->
											<view
												v-if="!isExpanded(sitem.id) && sitem.planItems && sitem.planItems.length > initialDisplayCount"
												class="more-courses-hint" @click.stop="toggleCourseDisplay(sitem.id)">
												<view class="hint-line"></view>
												<view class="hint-text">展开查看全部 {{sitem.planItems.length}} 个课程</view>
												<view class="hint-line"></view>
											</view>
										</view>

										<view
											v-if="isExpanded(sitem.id) && sitem.planItems && sitem.planItems.length > initialDisplayCount"
											class="toggle-button-bottom" @click.stop="toggleCourseDisplay(sitem.id)">
											<view class="toggle-text">收起</view>
											<view class="toggle-icon">
												<u-icon name="arrow-up" size="20" color="#00C1CC"></u-icon>
											</view>
										</view>
									</view>
								</view>
							</view>
						</template>
					</view>
				</template>

				<!-- 空状态显示 -->
				<view v-else class="empty-state">
					<u-empty text='正在路上~' mode="list" icon="../../../../static/empty/list.png"></u-empty>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	props: {
		// 第一级数组
		flist: {
			type: Array,
			default: () => []
		},
		// 第二级数组
		slist: {
			type: Array,
			default: () => []
		},
		// 第三级数组
		tlist: {
			type: Array,
			default: () => []
		},
		currentId: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			sizeCalcState: false,
			tabScrollTop: 0,
			old: {
				scrollTop: 0
			},
			expandedIds: [], // 存储已展开的课程类别ID
			initialDisplayCount: 3 // 横屏模式下初始显示更多课程
		}
	},
	watch: {
		tlist(newVal, oldVal) {
			this.changeTlist()
		}
	},
	methods: {
		changeTlist() {
			console.log('tlist更新:', this.tlist)
		},
		scroll(e) {
			this.old.scrollTop = e.detail.scrollTop
		},
		// 一级分类点击
		tabtap(item) {
			// 触发父组件的事件
			this.$emit('tabtap', item)
		},
		// 右侧栏滚动
		asideScroll(e) {
			let scrollTop = e.detail.scrollTop;
		},
		// 导航到详情
		navToList(itemId, type) {
			this.$emit("itemClick", itemId, type);
		},
		// 获取要显示的课程
		getDisplayedCourses(titem) {
			if (!titem.planItems) return [];

			// 如果已展开或课程数量少于初始显示数量，则显示全部
			if (this.isExpanded(titem.id) || titem.planItems.length <= this.initialDisplayCount) {
				return titem.planItems;
			}

			// 否则只显示初始数量
			return titem.planItems.slice(0, this.initialDisplayCount);
		},
		// 判断是否已展开
		isExpanded(id) {
			return this.expandedIds.includes(id);
		},
		// 切换展开/收起状态
		toggleCourseDisplay(id) {
			if (this.expandedIds.includes(id)) {
				this.expandedIds = this.expandedIds.filter(i => i !== id);
			} else {
				this.expandedIds.push(id);
			}
		}
	}
}
</script>

<style lang='scss'>
	.content.landscape {
		height: 100%;
		background-color: #f8f8f8;
		display: flex;
	}

	.right-aside {
		flex: 1;
		overflow: hidden;
		height: 100%;
		background-color: #FDFDFD;
	}

	.t-list {
		display: flex;
		flex-direction: column;
		width: 100%;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.t-item {
		background-color: #fff;
		border-radius: 16rpx;
		margin-bottom: 40rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}
	
	.category-title-block {
		margin-bottom: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
		padding-bottom: 15rpx;
	}

	.sub-categories-container {
		display: flex;
		flex-direction: column;
	}
	
	.sub-category {
		width: 100%;
		padding: 10rpx;
		box-sizing: border-box;
	}
	
	.course-section {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 15rpx;
		box-sizing: border-box;
	}

	.category-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15rpx 0;
	}
	
	.category-title {
		display: flex;
		/* flex-direction: column; */
	}
	
	.category-meta {
		font-size: 24rpx;
		color: #999;
		margin-top: 6rpx;
	}

	.detail_btn {
		color: #00C1CC;
		padding: 8rpx 30rpx;
		background: #fff;
		border-radius: 90rpx;
		font-size: 26rpx;
		border: 2rpx solid #00C1CC;
	}

	.course-grid {
		display: flex;
		flex-direction: column;
		width: 100%;
		transition: all 0.3s ease;
		overflow: hidden;
	}
	
	.course-grid.landscape-grid {
		display: flex;
		flex-direction: column;
	}
	
	.course-grid.landscape-grid .course-item {
		width: 100%;
		margin-right: 0;
		margin-bottom: 20rpx;
	}

	.course-grid.expanded {
		max-height: none;
	}

	.course-item {
		width: 100%;
		margin-bottom: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		background-color: #fff;
		padding: 20rpx;
		border-radius: 12rpx;
		box-sizing: border-box;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
		transition: all 0.2s ease;
		position: relative;
		overflow: hidden;
		border-left: 4rpx solid #00C1CC;
	}

	.course-item:active {
		transform: scale(0.98);
		background-color: #fafafa;
	}

	.course-image {
		width: 100rpx;
		height: 100rpx;
		border-radius: 10rpx;
		margin-right: 20rpx;
		border: none;
		flex-shrink: 0;
		background-color: #fafafa;
	}

	.course-arrow {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
	}

	.course-info-container {
		flex: 1;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		justify-content: center;
		min-height: 80rpx;
	}

	.course-name {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
		line-height: 1.4;
		margin-bottom: 8rpx;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
		word-break: break-all;
	}

	.course-meta {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.course-info {
		font-size: 22rpx;
		color: #999;
		display: flex;
		align-items: center;
	}

	.meta-icon {
		margin-right: 5rpx;
	}

	.course-tag {
		font-size: 20rpx;
		color: #ff6b6b;
		background-color: rgba(255, 107, 107, 0.1);
		padding: 2rpx 10rpx;
		border-radius: 20rpx;
	}

	.more-courses-hint {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 20rpx 0;
		width: 100%;
		cursor: pointer;
	}

	.hint-line {
		height: 1rpx;
		width: 80rpx;
		background: linear-gradient(to right, transparent, #ddd);
	}

	.hint-line:last-child {
		background: linear-gradient(to left, transparent, #ddd);
	}

	.hint-text {
		font-size: 24rpx;
		color: #999;
		margin: 0 20rpx;
		white-space: nowrap;
	}

	.toggle-button-bottom {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 16rpx 0;
		margin-top: 10rpx;
		background-color: #f8f8f8;
		border-radius: 12rpx;
	}

	.toggle-icon {
		margin-left: 10rpx;
	}

	.toggle-text {
		font-size: 26rpx;
		color: #00C1CC;
	}

	.category-title {
		display: flex;
		align-items: center;
	}

	.category-dot {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		background-color: #00C1CC;
		margin-right: 10rpx;
	}

	.category-count {
		font-size: 24rpx;
		color: #666;
	}

	.category-toggle {
		font-size: 24rpx;
		color: #00C1CC;
		display: flex;
		align-items: center;
		background-color: rgba(0, 193, 204, 0.05);
		padding: 6rpx 14rpx;
		border-radius: 30rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-left: 10rpx;
		border-left: 6rpx solid #00C1CC;
		line-height: 1.2;
	}
	
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		padding: 80rpx 0;
	}
	
	.com-fontsize-32 {
		font-size: 32rpx;
		font-weight: bold;
	}
	
	.com-color {
		color: #333;
	}
	
	.dis-ali {
		display: flex;
		align-items: center;
	}
	
	.jc_cen {
		justify-content: center;
	}
</style> 