<template>
	<view>
		<view class="nav">
			<view class="ul">
				<view class="li" :class="active == 1 ? 'active':''" @click="activeBtn(1)">
					<image src="/static/image/icons/Frame@2x(3).png" ></image>
					<text>首页</text>
				</view>
				<view class="li" :class="active == 2 ? 'active':''" @click="activeBtn(2)">
					<image src="/static/image/icons/Frame@2x(4).png"></image>
					<text>课程</text>
				</view>
				<view class="li" :class="active == 3 ? 'active':''" @click="activeBtn(3)">
					<image src="/static/image/icons/Group132@2x(1).png"></image>
					<text>购物车</text>
				</view>
				<view class="li" :class="active == 4 ? 'active':''" @click="activeBtn(4)">
					<image src="/static/image/icons/Frame@2x(5).png"></image>
					<text>我的</text>
				</view>
			<!-- 	<view class="li" :class="active == 3 ? 'active':''" @click="activeBtn(3)">
					<image src="/static/images/nav/find.svg"></image>
					<text>بايقاش</text>
				</view>
				<view class="li" :class="active == 4 ? 'active':''" @click="activeBtn(4)">
					<image src="/static/images/nav/collection.svg"></image>
					<text>توپلام</text>
				</view>
				<view class="li" :class="active == 5 ? 'active':''" @click="activeBtn(5)">
					<image src="/static/images/nav/member.svg"></image>
					<text>مىنىڭ</text>
				</view> -->
				<view class="indicator">
					
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	export default {
		 props: {
		     active: {
		          type: Number,
		          default: 1
		        }
			},
		data() {
			return {
				
			}
		},
		onLaunch() {
			
		},
		methods: {
			activeBtn(e){
				var _this = this;
				_this.active = e;
				if(e==1){
					// uni.switchTab({
					// 	url:'/pages/index/index'
					// })
				}
				if(e==2){
					uni.switchTab({
						url:'/pages/course/course'
					})
				}
			},
		}
	}
</script>

<style  lang="scss" scoped>
*{
	padding: 0;
	margin: 0;
	box-sizing: border-box;
}
/* page{
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background: #222327;
} */
.nav{
	position: relative;
	width: 100%;
	height: 100upx;
	background: #fff;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 10px;
}
.ul{
	display: flex;
	width: 100vw;
	align-items: center;
	justify-content: space-around;
}
.li{
	height: 45px;
	position: relative;
	width: 90upx;
	z-index: 2;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	border-radius: 50%;
}
.li::before{
	content:"";
	position: absolute;
	top: 10px;
	left: 0;
	width: 100%;
	height: 100%;
	background: #00aaff;
	border-radius: 50%;
	filter: blur(5px);
	opacity: 0;
	transition: 0.5s;
	transition-delay: 0s;
}
.li image{
	width: 40upx;
	height: 40upx;
	margin-top: 5upx;
}
.li text{
	display: flex;
	align-items: center;
	// height: 25px;
	font-size: 22upx;
	font-weight: 300;
}
.active{
	
	border-radius: 50%;
	background: rgba(0, 170, 255, 0.34);
	transform: translateY(-34upx);
	transition-delay: 0.25s;
	transition: 1s;
}
.active::before{
	opacity: 0.3;
	transition-delay: 0.25s;
}
.indicator{
	left: 0;
	position: absolute;
	bottom: 20px;
	margin-left: 38upx;
	align-self: flex-end;
	width: 110upx;
	height: 110upx;
	background: #fff;
	border-radius: 50%;
	z-index: 1;
	transition: 0.5s;
}
.indicator::before{
	content : "";
	position: absolute;
	top: 5px;
	left: -26px;
	width: 30px;
	height: 30px;
	background: transparent;
	border-radius: 50%;
	box-shadow: 15px 18px #fff;
}
.indicator::after{
	content: "";
	position: absolute;
	top: 5px;
	right: -26px;
	width: 30px;
	height: 30px;
	background: transparent;
	border-radius: 50%;
	box-shadow: -15px 18px rgba;

}
.li:nth-child(1).active~.indicator {
	transform: translateX(calc( 90rpx*0));
}
.li:nth-child(2).active~.indicator {
	transform: translateX( 187.5rpx);
}
.li:nth-child(3).active~.indicator {
	transform: translateX( 187.5rpx*2);
}
.li:nth-child(4).active~.indicator {
	transform: translateX( 187.5rpx*3);
}
// .li:nth-child(5).active~.indicator {
// 	transform: translateX(calc(70px*4));
// }
</style>