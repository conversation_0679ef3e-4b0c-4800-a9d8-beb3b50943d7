<template>
	<view>
		<z-paging  :show-scrollbar="false"   refresher-background='#fffff00'  ref="paging"    :use-page-scroll='true'  >
			<view slot="top">
				<!-- 头部nav -->
			</view>
			<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
			<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->

			<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->
			<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
			<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
			<template #refresher="{refresherStatus}">
				<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
				<custom-refresher :status="refresherStatus" color="#000" />
			</template>
			<!-- 自定义没有更多数据view -->
			<template #loadingMoreNoMore>
				<!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
				<custom-nomore />
			</template>
			<view v-if="loading">
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 1,
						headHeight: '200rpx',
						textRows:2,
						gridRows:8,
						textShow:false
					}">
					<view></view>
				</x-skeleton>
			</view>
			<view v-else >
				<view style="width: 100%;padding: 30upx;">
				
					<view class=" flex-column borderB" style="font-size: 26upx;margin-bottom: 30upx;width: 100%;"
						v-for="item in addressList">
							<view class="" style="background-color: #fff;width: 100%;border-radius: 10upx;padding-bottom: 30upx;">
								
								<view class=" flex-column">
									<view class="dis-ali">
										<view class="com-fontsize-32 font-bold mr10">{{item.name}}</view>
										<view class="com-fontsize-32 font-bold">{{item.mobile}}</view>
									</view>
									<view class="com-fontsize-28 mt10 mb10">{{item.address}} {{item.detailAddress}}</view>
								</view>
								<view class="dis-ali jc_bet">
									<view class="dis-ali">
										<view v-if="item.defaultStatus" class="com-fontsize-26 font-bold com-back-red com-fff pr5 pl5">默认地址</view>
									</view>
									<view class="dis-ali com-color-163">
										<view class="dis-ali mr10" @click="editAddress(item)">
											<u-icon name="edit-pen"></u-icon>
											<view>编辑</view>
										</view>
										<view class="dis-ali" @click="deleteAddress(item.id)">
											<u-icon name="trash"></u-icon>
											<view>删除</view>
										</view>
									</view>
								</view>
							</view>
							
						
					</view>
				
				</view>
				<u-empty
						v-if="!loading && (!addressList || addressList.length === 0)"
						text="暂无收货地址"
				        mode="list"
				        icon="../../static/empty/list.png"
						
				>
				</u-empty>
				<view class="fixed-bottom">
				
					<u-button shape='circle' size="small"  style="width:685upx;background-color:#00C1CC;border: 0;height: 90upx;"
						type="primary" @click='goAdd'>新增收货地址</u-button>
				</view>
			
				<!-- <u-loadmore :status="status" /> -->
			</view>
					<!-- 学习列表 -->
					<u-toast ref="uToast"></u-toast>
					<!-- toast -->
					<!-- 加载页 -->
					<!-- <my-bottom></my-bottom> -->
					<!-- </bottom> -->
			
		</z-paging>
	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				loading: true,
				status: 'nomore',
				page: 1,
				displayedText: '',
				typingInterval: null,
				typingIndex: 0,
				pageScrollTop: 0, // 页面滚动距离
				bgColor: 'rgba(255,255,255,0.01)',
				navtitle: '',
				addressList: [], // 地址列表
			}
		},
		onShow() {
			that = this;
			this.getAddressList();
		},
		onPageScroll(e) {
			this.pageScrollTop = Math.floor(e.scrollTop);
			// this.$refs.paging.updatePageScrollTop(e.scrollTop);
		},
		onReachBottom(e) {
			console.log(e)
			this.$refs.paging.complete();
		},
		// 	},
		beforeDestroy() {
			
		},
		onHide() {
			
		},
		methods: {
			onRefresh() {
				this.getAddressList();
				setTimeout(() => {
					this.$refs.paging.complete();
				}, 1500)
			},
			goAdd(type) {
				uni.navigateTo({
					url:'/pages/personal/addAdress'
				})
				
			},
			// 获取地址列表
			async getAddressList() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.addresslist,
						method: 'GET'
					})
					if(res.code === 0) {
						this.addressList = res.data || []
						this.loading = false
					}
				} catch(err) {
					console.error('获取地址列表失败:', err)
				}
			},
			
			// 删除地址
			async deleteAddress(id) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该地址吗？',
					success: async (res) => {
						if(res.confirm) {
							try {
								const res = await this.http.ajax({
									url: this.http.api.addressdelete,
									method: 'POST',
									data: { id }
								})
								if(res.code === 0) {
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									})
									this.getAddressList()
								}
							} catch(err) {
								console.error('删除地址失败:', err)
							}
						}
					}
				})
			},
			
			// 编辑地址
			editAddress(item) {
				uni.navigateTo({
					url: `/pages/personal/addAdress?id=${item.id}`
				})
			},
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #fff;
		/* background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 60%; */
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>
	.fixed-bottom {
					height: 145upx;
					background-color: rgba(0, 0, 0, 0);
					position: fixed;
					right: 0;
					bottom: 0upx;
					left: 0;
					z-index: 1;
					display: flex;
					flex-flow: row;
					justify-content: space-around;
					align-items: center;
					// box-shadow: 10upx 0 15upx rgba(0, 0, 0, 0.16);
				
				}
</style>