<template>
	<view>
		<view class="title" :class="topNavStyle.class" :style="topNavStyle.style">
			
			<view class="dis-ali jc_cen" style="width: 100%;" v-if="!detail">
				<!-- <view class="box1"></view> -->
				<view class="dis-ali jc_cen">
					<view class="com-fontsize-30 mt10"  v-if="show">{{title}}</view>
				</view>
				<view class="box1 align_r" v-if="detail">
					
				</view>
			</view>
			<view>
				
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"my-titlenav",
		data() {
			return {
				// pageScrollTop:0,	// 页面滚动距离
				show:false
			};
		},
		props:{
			title:{
				type:String,
				default:''
			},
			pageScrollTop:{
				type:Number,
				default:0
			},
			detail:{
				type:Boolean,
				default:false
			}
		},
		computed:{
			topNavStyle(){
				let r = this.pageScrollTop  / 100;
				if(r>=1){
					this.show=true
					
				}else{
					this.show=false
				}
				return {
					"class":r>=0.85?'style2':'',
					"style":`background-color: rgba(255,255,255,${r>=1?1:r});`
					
				}
			}
		},
		onPageScroll(e){
			console.log(e)
		},
	}
</script>

<style lang="scss" scoped>
/* 标题栏 */
	.title{
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: auto;
		padding-top: var(--status-bar-height);
		z-index: 10;
		background-color: rgba(66,185,131,0);
		color: rgba(255,255,255,0.8);
		
		&>view{
			height: 44rpx;
		
		}
		
		.box1{
			
			width: 60rpx;
			margin: 0 40rpx;
			font-size: 32rpx;
		}
		
		
		.tab{
			&>view{
				margin: 0 30rpx;
				line-height: 64rpx;
				font-size: 32rpx;
				position: relative;
				letter-spacing: 0;
				transition: transform 0.3s ease-in-out 0s;
				transform: scale(1,1);
				
				&.active{
					color: rgba(255,255,255,1);
					transform: scale(1.15,1.15);
				}
			}
		}
		
		&.style2{
			color: #000;
			font-size: 32upx;
			background-color: rgba(66, 185, 131,1);
			
			.tab{
				&>view{
					&.active{
						color: #FFF;
					}
				}
			}
		}
	}
</style>