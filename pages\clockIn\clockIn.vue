<template>
	<view>
		<z-paging :show-scrollbar="false" refresher-background='#fffff00' ref="paging"  :use-page-scroll='true' >
			<view slot="top">
				<u-navbar height="0" :bgColor="bgColor" :placeholder='true' :auto-back="true">
					<view style="padding-top: 100upx;" slot="left">
						<u-icon name="arrow-left" size="24" color="#fff"></u-icon>

					</view>
					<!-- <view style="padding-top: 100upx;" slot="center">
						<view class="com-fff">签到中心</view>
					</view> -->
				</u-navbar>
				<my-nav :title='navtitle' :pageScrollTop='pageScrollTop'></my-nav>
				<!-- 头部nav -->
			</view>
			<template #refresher="{refresherStatus}">
				<custom-refresher :status="refresherStatus" />
			</template>
			<template #loadingMoreNoMore>
				<custom-nomore />
			</template>
			<view class="dis-ali flex-column  " style="position: relative;margin-top: 300upx;height:auto;">
				<image src="../../static/image/<EMAIL>" mode="heightFix"
					style="height: 402upx;position: absolute;right: 30upx;top: -170upx;z-index: 0;" class="">
				</image>
				<view class="qdview" style="z-index: 0;">
					<view style="z-index: 100;">
						<view style="font-weight: 700;font-size: 42rpx;color: #FFFFFF;">
							签到领学能币
						</view>
						<view style="color: #FFFFFF;" class="dis-ali mt10">
							<image src="../../static/image/<EMAIL>" mode=""
								style="width: 33upx;height: 33upx;margin-right: 10upx;font-size: 26upx;"></image>
							我的学能币：{{signInData.totalPoint || 0}}
						</view>
					</view>
					<view class="list" style="z-index: 100;">
						<view class="dis-ali jc_bet">
							<view class="com-color-163 ">签到越多奖励越多哦~</view>
							<view class="dis-ali" style="color: #0BC196;z-index: 999;" @click='goUrl'>签到规则
								<u-icon name='arrow-right' color="#0BC196" size="18"></u-icon>
							</view>
						</view>
						<view class="dis-ali jc_bet" style="flex-wrap: wrap;">
							<view v-for="index in 6" :key="index" 
								class="dis-ali flex-column jc_cen"
								:class="[isSignedDay(index) ? 'li' : 'li_1']"
							>
								<view class="com-fontsize-24 mb5">
									{{isSignedDay(index) ? '已签到' : `第${index}天`}}
								</view>
								<view>
									<image 
										:src="isSignedDay(index) ? '../../static/image/<EMAIL>' : '../../static/image/<EMAIL>'"
										mode=""
										style="width: 64upx;height: 64upx;">
									</image>
								</view>
								<view class="com-fontsize-24">
									{{getPointReward(index)}}积分
								</view>
							</view>
							<view class="dis-ali flex-column jc_cen li_7" 
								:style="{
									backgroundImage: `url('../../static/image/${isSignedDay(7) ? '<EMAIL>' : '<EMAIL>'}')`
								}"
							>
								<view class="com-fontsize-24 mb5">
									{{isSignedDay(7) ? '已签到' : '第七天'}}
								</view>
								<view style="height: 64upx;"></view>
								<view class="com-fontsize-24" 
									:class="{'signed': isSignedDay(7)}"
									style="align-self: start;margin-left: 20upx;margin-top: 10upx;"
								>
									{{getPointReward(7)}}积分
								</view>
							</view>
						</view>
						<view class="dis-ali jc_cen mt10 sign_btn"
							@click="doSignIn"
							:style="signInData.todaySignIn ? 'opacity: 0.5;' : ''"
						>
							{{signInData.todaySignIn ? '今日已签到' : '立即签到'}}
						</view>
						<view class="dis-ali jc_bet mt20">
							<view class="com-color-163 com-fontsize-24">已连续签到{{signInData.continuousDay}}天</view>
							<view class="dis-ali com-fontsize-24" style="color: #0BC196;">签到提醒
								<u-switch
									style="margin-left: 10upx;"
									space="3" v-model="value" activeColor="#0BC196" size='20'
									inactiveColor="#BDDED6">
								</u-switch>
							</view>
						</view>
					</view>
				</view>
				<my-bottom></my-bottom>
				<!-- 头部 -->
				<!-- <view class="" style="width: 100%;padding: 50upx 40upx;">
					<view class="" style="background-color: #fff;border-radius: 20upx;">
						<view class="borderB"  style="padding: 30upx;font-size: 34upx;color: #000;">
							做任务得积分
						</view>
						<view class="dis-ali" style="font-size: 26upx;width: 120upx;margin-bottom: 30upx;width: 100%;padding: 30upx;"
							v-for="item in 2">
							<view>
								<image src="/static/image/signBack.png"
									style="width: 94upx;height: 94upx;border-radius: 50%;" mode="aspectFill">
								</image>
							</view>
							<view class="dis-ali jc_bet" style="width: 100%;">
								<view class=" flex-column "
									style="display: flex;margin-left: 20upx;">
									<view class="com-fontsize-32 font-bold">直播标题</view>
									<view class="com-fontsize-24 ">+10</view>
								</view>
								<view style="width: 140rpx;
												height: 70rpx;
										background: #D8D8D8;
								border-radius: 90rpx 90rpx 90rpx 90rpx;font-size: 24upx;" class="dis-ali jc_cen">
									去完成
								</view>
							</view>
						</view>
					</view>
					
				</view> -->
				
			</view>

			<!-- 学习列表 -->
			<u-toast ref="uToast"></u-toast>
			<!-- toast -->
			<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
				:loading="loading"></u-loading-page>
			<!-- 加载页 -->
			<!-- </bottom> -->

		</z-paging>
	</view>
</template>

<script>
	var that
	import dragButton from "@/components/drag-button/drag-button.vue";
	export default {
		components: {
			dragButton
		},
		data() {
			return {
				loading: true,
				status: 'loadmore',
				page: 1,
				displayedText: '',
				typingInterval: null,
				typingIndex: 0,
				pageScrollTop: 0, // 页面滚动距离
				bgColor: 'rgba(255,255,255,0.01)',
				navtitle: '签到中心',
				value:false,
				signInData: {
					continuousDay: 0, // 连续签到天数
					totalDay: 0, // 总签到天数
					todaySignIn: false, // 今日是否已签到
					totalPoint: 0, // 签到奖励积分
				},
				signInRecords: [], // 签到记录列表
				pageSize: 10,
				pageNum: 1,
				total: 0,
				signConfigList: [], // 签到配置列表
			}
		},
		onLoad() {
			that = this;
			setTimeout(() => this.loading = false, 1000)
			this.getSignInSummary()
			this.getSignInRecords()
			this.getSignConfigList() // 获取签到配置
		},
		onPageScroll(e) {
			this.pageScrollTop = Math.floor(e.scrollTop);
			// this.$refs.paging.updatePageScrollTop(e.scrollTop);
		},
		onReachBottom(e) {
			// this.$refs.paging.pageReachBottom()
		},
		// 	},
		onShow() {
			return;
			that.http.ajax({
				url: '/registerMember',
				method: 'POST',
				success(res) {
					if (res.code == 200) {
						that.http.ajax({
							url: that.http.api.info,
							method: 'POST',
							success(res) {

							}
						})
					} else {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})
					}
				}
			})
		},
		beforeDestroy() {

		},
		onHide() {

		},
		methods: {
			onRefresh() {
				// 刷新时重新获取数据
				this.getSignInSummary()
				this.getSignInRecords()
				setTimeout(() => {
					this.$refs.paging.complete()
				}, 1500)
			},
			goUrl(type) {
				uni.navigateTo({
					url:'/pages/yinsi/policy?type=4'
				})

			},
			// 判断某天是否已签到
			isSignedDay(day) {
				// 如果是过去的天数，且在连续签到天数内，则显示已签到
				return day <= this.signInData.continuousDay
			},

			// 获取某天的奖励积分
			getPointReward(day) {
				// 从配置列表中获取对应天数的奖励积分
				const config = this.signConfigList.find(item => item.day === day)
				return config ? config.point : 100 // 如果没有配置则返回默认值100
			},

			// 获取签到统计信息
			getSignInSummary() {
				this.http.ajax({
					url: this.http.api.recordsummary,
					method: 'GET'
				}).then(res => {
					if(res.code === 0 && res.data) {
						this.signInData = res.data
					}
				}).catch(err => {
					console.error('获取签到统计失败:', err)
				})
			},

			// 获取签到记录
			getSignInRecords() {
				this.http.ajax({
					url: this.http.api.recordpage,
					method: 'GET',
					data: {
						pageSize: this.pageSize,
						pageNum: this.pageNum
					}
				}).then(res => {
					if(res.code === 0 && res.data) {
						this.signInRecords = res.data.list
						this.total = res.data.total
					}
				}).catch(err => {
					console.error('获取签到记录失败:', err)
				})
			},

			// 执行签到
			doSignIn() {
				if(this.signInData.todaySignIn) {
					uni.showToast({
						title: '今日已签到',
						icon: 'none'
					})
					return
				}

				this.http.ajax({
					url: this.http.api.recordcreate,
					method: 'POST'
				}).then(res => {
					if(res.code === 0) {
						uni.showToast({
							title: '签到成功',
							icon: 'success'
						})
						// 刷新签到信息
						this.getSignInSummary()
					}
				}).catch(err => {
					console.error('签到失败:', err)
					uni.showToast({
						title: '签到失败',
						icon: 'none'
					})
				})
			},

			// 获取签到配置列表
			async getSignConfigList() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.signconfiglist,
						method: 'GET'
					})
					if(res.code === 0 && res.data) {
						this.signConfigList = res.data
					}
				} catch(err) {
					console.error('获取签到配置失败:', err)
				}
			},
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #f3f3f3;
		background-image: url(../../static/image/<EMAIL>);
		background-size: 100% 100%;
		height: 100vh;
	}
</style>

<style lang="scss" scoped>
	.qdview {
		width: 682upx;
		height: 904upx;
		background-image: url(../../static/image/<EMAIL>);
		background-size: 100% 100%;
		padding: 35upx 25upx;

		.list {
			width: 635upx;
			height: 723upx;
			background-color: #fff;
			border-radius: 12upx;
			margin-top: 20upx;
			padding: 20upx;

			.li {
				width: 139upx;
				height: 178upx;
				background-color: #E0FFF8;
				border-radius: 12upx;
				color: #0BC196;
				margin: 20upx 0 0;
			}
			.li_1{
				width: 139upx;
				height: 178upx;
				background-color: #FEF9ED;
				border-radius: 12upx;
				color: #FF8D1A;
				margin: 20upx 0 0;
				
			}
			
		}
		
	}
	.li_7{
		width: 278upx;
		height: 178upx;
		border-radius: 12upx;
		color: #FF8D1A;
		background-size: 100% 100%;
		margin: 20upx 0 0;
		
		&.signed, &:has(.signed) {
			color: #0BC196;
		}
	}
	.u-switch{
		border-radius: 10upx !important;
	}
	.sign_btn{
		width: 576rpx;height: 108rpx;background: linear-gradient( 270deg, #0BC196 0%, #1CE885 100%);border-radius: 80upx;color: #fff;
	}
</style>