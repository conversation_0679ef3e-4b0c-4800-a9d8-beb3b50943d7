<template>
	<view>

		<z-paging :show-scrollbar="false" refresher-background='#fffff00' ref="paging" refresher-only
			@onRefresh="onRefresh" :use-page-scroll='true'>
			<view slot="top">
				<u-navbar :title="topNavStyle.title" :height="topNavStyle.height" :left-icon-color="topNavStyle.Lstyle"
					:bgColor="topNavStyle.style" :auto-back="true" :placeholder='false' style="z-index: 1;">
					<view  slot="left">
						<u-icon name="arrow-left" size="24" color="#000"></u-icon>
					</view>
				</u-navbar>
			</view>

			<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
			<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->

			<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

			<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
			<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
			<template #refresher="{refresherStatus}">
				<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
				<custom-refresher :status="refresherStatus" color='#000' />
			</template>
			<!-- 自定义没有更多数据view -->
			<template #loadingMoreNoMore>
				<!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
				<custom-nomore />
			</template>
			<!-- 	<u-navbar :title="navtitle" height="44" :bgColor="bgColor" :placeholder='true' :auto-back="true">
			</u-navbar> -->
			<view v-if="loading">
				<x-skeleton type="banner" :loading="true">
					<view></view>
				</x-skeleton>
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 1,
						headHeight: '50rpx',
						textRows: 2,
						gridRows:1,
						textShow:false
					}">
					<view></view>
				</x-skeleton>
				<x-skeleton type="list" :configs="{gridRows:8,headWidth:'80rpx',headHeight:'80rpx',textRows: 2}"
					:loading="true">
					<view></view>
				</x-skeleton>
			</view>

			<view v-else>

				<view class="com-fontsize-32 growpTopview" :style="{'background-image': `url(${detail.bannerUrl})`}">
					<view>{{detail.courseClassifyName}}</view>
					<view class="com-fontsize-24 mt10">{{detail.participantCount}}人参与</view>
				</view>

				<view style="background-color: #fff;padding: 20upx 20upx;">
					<view
						style="padding: 10upx;font-size: 28upx;line-height: 36upx;color: #333;line-height: 1.8;" v-html="detail.description">
					</view>
					
				</view>
				<view class="groupList" style="">
					<view class="com-fontsize-32 title" style="">成长阅读 </view>
					<!-- <text style="font-size: 26upx;font-weight: 500;margin-left: 20upx;color: #474747;">已更新{{detail.growTask.updatesNumber}}/{{detail.growTask.updatesNumber}}</text> -->
					<view 
						v-for="(item, index) in detail.growTaskItems" 
						:key="item.id"
						@click="goDetail(item.id)" 
						class="dis-ali jc_bet list"
					>
						<view class="dis-ali left">
							<view>
								<image 
									:src="item.cover || '../../static/image/<EMAIL>'"
									style="width: 102upx;height: 102upx;border-radius: 50%;margin-right: 20upx;" 
									mode="aspectFill"
								></image>
							</view>
							<view>
								<view class="text-line-overflow" style="font-size: 30rpx;color: #3D3D3D;width: 450upx;line-height: 1.8;">
									{{item.name}}
								</view>
								<view class="com-fontsize-26 com-color-163">
									已完成{{item.completedPeriodCount}}/{{item.sumPeriodCount}}节
									
								</view>
								<view class='dis-ali'>
									<view v-if="item.isFree" class="mt5 mr5 tab">已兑换</view>
									<view v-if="item.isLast" class="mt5 tab_new">上新</view>
								</view>
								
							</view>
							
						</view>
						<view>
							<view class="dis-ali right_1"  v-if="!item.isFree">
								去试看
							</view>
							<view :class="['dis-ali', item.isComplete  ? 'right_2' : 'right']" v-else>
								{{ item.isComplete  ? '已完成' : '去学习' }}
							</view>
						</view>
						
					</view>
				</view>

			</view>



			<!-- 学习列表 -->
			<u-toast ref="uToast"></u-toast>
			<!-- toast -->
			<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
				:loading="false"></u-loading-page>
			<!-- 加载页 -->
			<my-bottom></my-bottom>
			<!-- </bottom> -->

		</z-paging>

	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				loading: true,
				pageScrollTop: 0,
				bgColor: 'rgba(255,255,255,0.01)',
				navtitle: '',
				detail: {
					courseClassifyName: '',
					participantCount: 0,
					brief: '',
					growTask: {
						updatesNumber: 0,
						sumNumber: 0
					},
					growTaskItems: [],
				},
				scrollTop:0
			}
		},
		computed: {
			topNavStyle() {
				let r = this.scrollTop / 100
				return {
					style: `rgba(255,255,255,${r>=1?1:r})`,
					Tstyle: `color:${r>1?'#000':'#fff'}`,
					Lstyle: r > 1 ? '#000' : '#fff',
					title: r > 1 ? this.detail.courseClassifyName : '',
					height: r > 1 ? '44' : '44',
				}
			}
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop
		},
		methods: {
			// 对数据进行排序处理
			sortGrowTaskItems(data) {
				if(!data || !data.growTaskItems) return data
				
				// 复制一个新的数据对象，避免直接修改原数据
				const sortedData = { ...data }
				
				// 对 growTaskItems 进行排序
				sortedData.growTaskItems = data.growTaskItems.sort((a, b) => {
					// 1. 首先按 isFree 排序 (1排在前面)
					if (a.isFree !== b.isFree) {
						return b.isFree - a.isFree
					}
					// 2. 其次按 isLast 排序 (1排在前面)
					if (a.isLast !== b.isLast) {
						return b.isLast - a.isLast
					}
					// isFree相同时保持原有顺序
					return 0
				})
				
				return sortedData
			},
			async getGrowthPlanDetail(id) {
				try {
					const res = await that.http.ajax({
						url: that.http.api.loadGrowPlanDetail,
						data: {
							courseClassifyId: id
						}
					})
					
					if(res.code === 0) {
						this.detail = this.sortGrowTaskItems(res.data)
						this.loading = false
					}
				} catch(err) {
					console.error('获取成长计划详情失败:', err)
					this.loading = false
				}
			},
			goDetail(id) {
				uni.navigateTo({
					url: '/pages/growthPlan/groupDetail?id='+id
				})
			},
			onRefresh() {
				if(this.courseId) {
					this.getGrowthPlanDetail(this.courseId)
				}
				setTimeout(() => {
					this.$refs.paging.complete();
				}, 1500)
			},
		},
		onLoad(option) {
			that = this
			if(option.id) {
				this.courseId = option.id
				this.getGrowthPlanDetail(option.id)
			}
		}
	}
</script>

<style scoped lang="scss">
	video {
		vertical-align: middle;
	}

	image {
		vertical-align: middle !important;
	}

	.growpTopview {
		font-weight: 700;
		color: #333;
		background-image: url(https://cdn.pixabay.com/photo/2014/08/05/10/30/iphone-410324_1280.jpg);
		width: 750upx;
		height: 420upx;
		background-size: cover;
		display: flex;
		align-items: start;
		justify-content: end;
		padding: 80upx 30upx;
		flex-direction: column;
	}

	.groupList {
		background-color: #fff;
		padding: 30upx 20upx 0;
		margin-top: 10upx;

		.title {
			font-weight: 700;
			color: #000;
			margin-bottom: 20upx;
		}
		.list{
			min-height: 75upx;
			border-bottom: 1upx solid rgba(0, 0, 0, 0.06);
			padding: 30upx 10upx 30upx 0;
		}
		.list:last-child{
			border: 0;
		}
		.tab {
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			/* width: 103rpx; */
			width: 120upx;
			height: 35rpx;
			background: linear-gradient(90deg, #FB6D5F 0%, #FB968B 100%);
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 22rpx;
			color: #FFFFFF;
			padding: 0 10upx;
		}
		.tab_new{
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			/* width: 103rpx; */
			width: 80upx;
			height: 35rpx;
			background: linear-gradient(90deg, #FB6D5F 0%, #FB968B 100%);
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 22rpx;
			color: #FFFFFF;
			padding: 0 10upx;
		}
		.right{
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			color: #00C1CC;
			width: 123rpx;
			height: 53rpx;
			border-radius: 60rpx 60rpx 60rpx 60rpx;
			border: 2rpx solid #00C1CC;
		}
		.right_2{
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			color: #FFF;
			background-color: #00C1CC;
			width: 123rpx;
			height: 53rpx;
			border-radius: 60rpx 60rpx 60rpx 60rpx;
			border: 2rpx solid #00C1CC;
		}
		.right_1{
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			color: #adadad;
			width: 123rpx;
			height: 53rpx;
			border-radius: 60rpx 60rpx 60rpx 60rpx;
			border: 2rpx solid #adadad;
		}
	}
</style>