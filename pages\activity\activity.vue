<template>
	<view>
		<u-navbar id='jieshao' bgColor="#fff" title="活动详情" height="52" :placeholder='true' :auto-back="true"
			@rightClick='uniShare' style="">

			<view class="u-nav-slot" slot="right">
				<u-icon name="share-square" size="24" color="#000" style="z-index: 9999999;"></u-icon>
				<text class="com-fontsize-32"></text>
			</view>
		</u-navbar>
		<view v-if="pageLoading">

			<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle" iconSize='80'
				image="/static/image/<EMAIL>" :loading="pageLoading">
			</u-loading-page>
		</view>

		<view v-else>

			<view class="title com-shadow borderB" :class="topNavStyleT.class" :style="topNavStyleT.style">

				<view class="dis-ali jc_cen" style="width: 100%;padding-bottom: 20upx;" v-if="show">

					<view class="dis-ali jc_bet " style="width: 100%;padding:0 50upx;margin-top: 20upx;color: #555555;">
						<view :class="{'click':clickIndex==index}" @click="changeTop(index)"
							v-for="(item,index) in detailTopList" :key="index">
							{{item}}
						</view>

					</view>
				</view>
				<view>
				</view>
			</view>
			<view class="content">

				<!-- goodsData：轮播图数据  @setShowVideo：视频按钮点击事件 -->
				<cc-videoSwiper :statusBar="true" height='422upx' :goodsData="goodsData"
					@setShowVideo="setShowVideo"></cc-videoSwiper>

				<!-- 预览视频弹窗  @touchmove.stop.prevent="ondefault"-->
				<!-- <view class="mask" v-if="showVideo == true" @click="hideShow"  @touchmove.stop.prevent>
					<view class="close">
						<image src="/static/images/goods/close.png"></image>
					</view>
				</view> -->
				<u-overlay :show="showVideo" @click="hideShow">
					<view class="close">
						<image src="/static/image/icons/<EMAIL>" style="width: 80upx;height: 80upx;"></image>
					</view>
				</u-overlay>
				<view class="previewvideo" v-if="showVideo == true">
					<view class="videos">
						<video controls class="nowvideos" id="nowVideo" v-if="showVideo == true" :src="goodsData.videos"
							:autoplay="showVideo" :show-center-play-btn="true" :show-mute-btn="true"
							:show-fullscreen-btn="true">
							<!-- <cover-view @click="hideShow" class="close_btn" style="">关闭</cover-view> -->
						</video>
					</view>
				</view>
				<!-- 用来承载H5预览视频的 -->
				<view style="position: absolute;top: -999upx;left: -999upx;">
					<video ref="newVideo" id="newVideo" :src="goodsData.videos" :autoplay="showVideo"
						:show-center-play-btn="false" :show-mute-btn="true" :show-fullscreen-btn="false"
						@fullscreenchange="hideShow"></video>
				</view>
			</view>
			<!-- 商品图片 -->
			<view style="padding-bottom: 165upx;">
				<view style="width: 100%;padding: 30upx 30upx 20upx 30upx;background-color: #fff;">
					<view class="dis-ali jc_bet">
						<view class="com-fontsize-32 text-line-overflow">{{activityDetail.title}}</view>
						<!-- <view class="dis-ali share_btn" style="">
							<view>分享</view>
							<u-icon name="share-square"></u-icon>
						</view> -->
					</view>
					<view class="com-fontsize-26 com-color-163 mt10 pr10" style="color: #FF8500;">
						适合年龄：{{activityDetail.brief}}</view>
					<view class="com-fontsize-26 com-color-163 mt5 pr10">
						{{http.timestampToDate(activityDetail.startTime)}}-{{http.timestampToDate(activityDetail.endTime)}}
					</view>
					<view class="com-fontsize-26 com-color-163 mt5 pr10" style="color: #FF8500;font-size: 40upx;">
						￥{{activityDetail.price}}
						<text style="font-size: 24upx;" class="com-color-163">/{{activityDetail.unit}}</text>
					</view>
					<u-divider text="" textColor="#ccc" id='pingjia' lineColor="#ccc"></u-divider>
					<view class="dis-ali jc_bet">
						<view class="com-fontsize-32 text-line-overflow">服务</view>
						<view class="dis-ali" style="" @click="showServicePopup = true">
							<view class="com-fontsize-24" v-for="(item,index) in activityDetail.serviceDetailContent">
								<text v-if="index!=0">·</text>{{item.serviceDetailTitle}}
							</view>
							<u-icon name="arrow-right" size="12"></u-icon>
						</view>
					</view>
					<u-divider text="" textColor="#ccc" lineColor="#ccc"></u-divider>
					<view class="dis-ali jc_bet">
						<view class="com-fontsize-32 text-line-overflow">评价</view>
						<view class="dis-ali" style="" @click="showAllReviewsHandler">
							<view class="com-fontsize-24 com-color-163">查看全部</view>
							<u-icon name="arrow-right" size="12"></u-icon>
						</view>
					</view>
					<view>
						<view class="com-fontsize-24 mt10">
							共{{activityDetail.review?activityDetail.review.reviewNum :'0'}}人参与评价
						</view>
						<!-- 	<view class="dis-ali" style="justify-content: space-between;margin-top: 20upx;flex-wrap: wrap;">
							<view 
								v-if="activityDetail.review?activityDetail.review.reviewLabelList:activityDetail.review.length"
								v-for="(item,index) in activityDetail.review.reviewLabelList" 
								:key="index"
								:class="['label-item', {'label-active': selectedLabel === item.reviewLabel}]"
								@click="handleLabelClick(item.reviewLabel)"
								class="com-fontsize-24 text-overflow mb5"
							>
								{{item.reviewLabel}}({{item.reviewLabelNum}})
							</view>
							<view style="width: 220upx;"></view>
						</view> -->

					</view>
					<view v-for="(item,index) in reviewList" v-if="index<2">
						<view style="display: flex;margin-top: 20upx;">
							<view>
								<image
									:src="item.avatar?item.avatar:'https://syg-photo.oss-cn-shanghai.aliyuncs.com/saas_yxb/photo/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20241204183315.jpg-1733308407000'"
									mode="" style="width: 76upx;height: 76upx;margin-right: 20upx;border-radius: 50%;">
								</image>
							</view>
							<view>
								<view class="com-fontsize-36 ">
									{{item.nickname}}
								</view>
								<view class="dis-ali" style="margin-top: 20upx;">
									<view
										style="background-color: #F5F6F8;padding:10upx 15upx;color: #8E8E8E;margin-right: 10upx;font-size: 22upx;"
										class="com-fontsize-24">
										{{item.reviewLabel}}
									</view>
								</view>
								<view class="com-fontsize-28 mt10 mb10">
									{{item.reviewContent}}
								</view>
								<view>
									<!-- 评价图片 -->
									<view class="review-album" v-if="item.reviewImageUrl && item.reviewImageUrl.length">
										<u-album :urls="item.reviewImageUrl" keyName="url" :singleSize="160"
											:multipleSize="60" :rowCount="3" :maxCount="9" :gap="10"></u-album>
									</view>
								</view>
								<view class="com-fontsize-26 com-color-163  ">{{http.timestampToDate(item.createTime)}}
								</view>
							</view>
						</view>
					</view>
					<u-divider text="" textColor="#ccc" lineColor="#ccc"></u-divider>
					<view class="dis-ali jc_bet" id='xiangqing'>
						<view class="com-fontsize-32 text-line-overflow">详情</view>
					</view>
				</view>
				<view class="u-content" style="background-color: #fff;z-index: 0;">
					<u-parse :content="activityDetail.detail" style="z-index: 1;padding: 0 30upx;"></u-parse>
				</view>
				<view class="fixed-bottom">
					<!-- <view class="dis-ali flex-column">
						<u-icon name='kefu-ermai' color="#000" size='26'></u-icon>
						<view class="">
							客服
						</view>
					</view> -->
					<u-button size="small" style="width:575upx;background-color:#FF8D1A;border: 0;height: 105upx;"
						type="primary" @click="makephone">联系我们</u-button>
				</view>

			</view>
			<!-- 服务详情弹窗 -->
			<u-popup :show="showServicePopup" @close="showServicePopup = false" mode="bottom" round="10">
				<view class="service-popup">
					<view class="service-popup__header">
						<text class="service-popup__title">服务说明</text>
						<u-icon name="arrow-down" size="18" @click="showServicePopup = false"></u-icon>
					</view>
					<view class="service-popup__content">
						<view class="service-item" v-for="(item, index) in activityDetail.serviceDetailContent"
							:key="index">
							<view class="service-item__title">
								<text class="service-item__dot"></text>
								<text>{{item.serviceDetailTitle}}</text>
							</view>
							<view class="service-item__desc" v-html='item.serviceDetailContent'></view>
						</view>
					</view>
				</view>
			</u-popup>
			<!-- 全部评价弹窗 -->
			<u-popup :show="showAllReviews" @close="showAllReviews = false" mode="bottom" round="10"
				:maskCloseAble="true" @touchmove.prevent>
				<view class="all-reviews" @touchmove.stop.prevent>
					<view class="all-reviews__header">
						<view class="header-left">
							<u-icon name="arrow-left" size="20" @click="showAllReviews = false"></u-icon>
							<text class="header-title">全部评价</text>
						</view>
						<text
							class="review-count">共{{activityDetail.review && activityDetail.review.reviewNum ? activityDetail.review.reviewNum : 0}}人参与评价</text>
					</view>

					<!-- 评价标签筛选 -->
					<scroll-view scroll-x class="review-tabs">
						<view class="tab-list">
							<view class="tab-item" :class="{'tab-active': !popupSelectedLabel}"
								@click="handleLabelClick('')">全部</view>
							<view
								v-for="(item, index) in (activityDetail.review && activityDetail.review.reviewLabelList ? activityDetail.review.reviewLabelList : [])"
								:key="index" class="tab-item"
								:class="{'tab-active': popupSelectedLabel === item.reviewLabel}"
								@click="handleLabelClick(item.reviewLabel)">
								{{item.reviewLabel}}({{item.reviewLabelNum}})
							</view>
						</view>
					</scroll-view>

					<!-- 评价列表 - 修改高度计算方式 -->
					<scroll-view scroll-y class="review-list" :style="{height: 'calc(90vh - 200rpx)'}" @touchmove.stop
						@scrolltolower="loadMore">
						<view class="review-item" v-for="(item, index) in popupReviewList" :key="index"
							style="padding: 30rpx 0;border-bottom: 1rpx solid #EEEEEE;">
							<view class="review-user">
								<image
									:src="item.avatar ? item.avatar : 'https://syg-photo.oss-cn-shanghai.aliyuncs.com/saas_yxb/photo/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20241204183315.jpg-1733308407000'"
									class="user-avatar"></image>
								<view class="user-info">
									<text class="user-name">{{item.nickname}}</text>
									<text class="review-time">{{http.timestampToDate(item.createTime)}}</text>
								</view>
							</view>
							<view class="review-tags">
								<text class="review-tag">{{item.reviewLabel}}</text>
							</view>
							<view class="review-content">{{item.reviewContent}}</view>
							<view class="review-media" v-if="item.reviewImageUrl && item.reviewImageUrl.length">
								<u-album :urls="item.reviewImageUrl" keyName="url" :singleSize="160" :multipleSize="80"
									:rowCount="3" :maxCount="9" :gap="10"></u-album>
							</view>
						</view>
						<!-- 加载更多提示 -->
						<view class="load-more" v-if="popupReviewList.length > 0">
							<text v-if="loading">加载中...</text>
							<text v-else-if="!hasMore">没有更多了</text>
						</view>
					</scroll-view>
				</view>
			</u-popup>
		</view>
		<!-- H5 分享弹窗 -->
		<!-- #ifdef H5 -->
		<u-popup @close="showSharePopup=false" :show="showSharePopup" mode="bottom" border-radius="20">
			<view class="share-popup">
				<view class="share-title">分享至</view>
				<view class="share-options">
					<view class="share-item" @click="copyLink">
						<image src="/static/image/copyurl.png" mode="aspectFit"></image>
						<text>复制链接</text>
					</view>
					<view class="share-item" @click="saveH5Poster">
						<image src="/static/image/hp.png" mode="aspectFit"></image>
						<text>保存海报</text>
					</view>
				</view>
				<view class="share-cancel" @click="showSharePopup = false">取消</view>
			</view>
		</u-popup>
		<!-- #endif -->
		<l-painter ref="painter" isCanvasToTempFilePath :board="posterConfig" @success="onPosterSuccess"
			@fail="onPosterFail" @done='done' @progress='number' custom-style="position: fixed; left: 200%" />
	</view>


</template>

<script>
	var that
	var a, b, c
	import UniShare from 'uni_modules/uni-share/js_sdk/uni-share.js';
	import {
		requestPermissions
	} from "@/app-permission.js"
	const uniShare = new UniShare();
	export default {
		data() {
			return {
				goodsData: {
					videos: '',
					imgList: []
				},
				activityId: '', // 活动ID
				activityDetail: {
					review: {
						reviewNum: 0,
						reviewLabelList: []
					}
				}, // 活动详情
				reviewList: [], // 评价列表
				reviewTotal: 0, // 评价总数
				showVideo: false,
				newVideo: null,
				title: '活动详情',
				pageScrollTop: 0, // 页面滚动距离
				show: false,
				clickIndex: 0,
				selectedLabel: '', // 主页面标签选中状态
				popupSelectedLabel: '', // 弹窗标签选中状态
				detailTopList: [
					'介绍',
					"评价",
					"详情"
				],
				content: '<p><img style="max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;" src="https://manage.nuomengweb.com.cn/uploads/attach/2023/11/20231117/1283c970a66efc1660bf499703bfc03d.jpg"/></p><p><b><font color="#c24f4a">购买方式</font></b>：可用微信支付买或选择积分抵扣，积分不足时须支付现金购买；</p><p><b><font color="#8baa4a">领取方式</font></b>：凭兑换码线下领取（不接受截图领取）；</p><p><b><font color="#7b5ba1">领取地点</font></b>：仅限拉萨北城新天地购中心客服台&nbsp; &nbsp; ；&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </p><p>&nbsp;兑换领取规则：</p><p>1、每个会员账号仅一次兑换资格；</p><p>2、产品随机，领取以实物为准，产品数量有限，线上兑完即止；</p><p>3、凭兑换码在有效期内进行兑换，逾期未领取商品将作废，过期后兑换码自动失效且予退还积分；</p><p>4、产品价格设定会根据产品市场价浮动调整；</p><p>5、完成此商品兑换表示已同意以上条款；</p>',
				showServicePopup: false, // 服务弹窗显示状态
				showAllReviews: false, // 全部评价弹窗显示状态
				currentPage: 1, // 当前页码
				pageSize: 10, // 每页数量
				loading: false, // 是否正在加载
				hasMore: true, // 是否还有更多数据
				popupReviewList: [], // 弹窗评价列表
				pageLoading: true,
				posterConfig: null,
				posterUrl: '', // 海报背景图URL
				currentUrl: '', // 当前页面完整URL用于生成二维码
				poserPath: '',
				showSharePopup: false, // H5 分享弹窗显示状态,
			}
		},

		computed: {
			topNavStyleT() {
				let r = this.pageScrollTop / 100;
				if (r >= 1) {
					this.show = true

				} else {
					this.show = false
				}
				return {
					"class": r >= 0.85 ? 'style2' : '',
					"style": `background-color: rgba(255,255,255,${r>=1?1:r});`

				}
			}
		},
		watch: {
			'showVideo': {
				handler(newVal, oldVal) {
					// 字段值变化时触发
					console.log('字段值变化了：', newVal);
				},
				immediate: true // 如果需要在组件创建时立即监听，设置immediate为true
			}

		},
		onPageScroll(e) {
			this.pageScrollTop = Math.floor(e.scrollTop);
			// console.log(e)
			// console.log(a,b,c)
			if (this.pageScrollTop >= b && this.pageScrollTop < c) {
				this.clickIndex = 1
			} else if (this.pageScrollTop >= c) {
				this.clickIndex = 2
			} else {
				this.clickIndex = 0
			}

		},
		onLoad(option) {
			that = this;
			console.log(option)
			this.activityId = option.id
			this.selectedLabel = '' // 初始化选中标签为空
			this.newVideo = uni.createVideoContext('newVideo');
			setTimeout(() => {
				this.getElementInfo('#jieshao')
				this.getElementInfo('#pingjia')
				this.getElementInfo('#xiangqing')
			}, 1000)
			if (option.id) {
				this.activityId = option.id
				this.getActivityDetail()
				this.getReviewList()
			}
			this.getCurrentPageUrl() // 获取当前页面URL
		},
		onBackPress({
			from
		}) {
			console.log(from)
			// 处理分享菜单的返回
			if (from === 'backbutton') {
				if (uniShare.isShow) {
					this.$nextTick(() => {
						uniShare.hide()
					})
					return true
				}

				// 处理其他弹窗的返回
				return this.handleBackPress()
			}
			return false
		},
		onHide() {


			this.$nextTick(function() {
				uniShare.hide()
			})
			return uniShare.isShow;

		},
		onUnload() {
			this.$nextTick(function() {
				uniShare.hide()
			})
			return uniShare.isShow;

		},
		methods: {
			// 获取当前页面完整URL
			getCurrentPageUrl() {
				// #ifdef H5
				this.currentUrl = window.location.href;
				// #endif

				// #ifdef APP-PLUS
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				const route = currentPage.route;
				const options = currentPage.options;

				// 构建查询字符串
				let queryString = '';
				if (Object.keys(options).length > 0) {
					queryString = '?' + Object.keys(options)
						.map(key => `${key}=${options[key]}`)
						.join('&');
				}

				// 获取当前域名
				plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
					console.log(widgetInfo, domain)
					const domain = widgetInfo.origin || this.http.poserUrl;
					this.currentUrl = `${domain}/${route}${queryString}`;
					// 获取到URL后初始化海报配置
					if (this.posterUrl) {
						this.initPosterConfig().catch(err => {
							console.error('初始化海报配置失败:', err);
						});
					}
				});
				// #endif
			},
			getImageUrlWithTimestamp(url) {
				const timestamp = Date.now();
				const separator = url.includes('?') ? '&' : '?';
				return `${url}${separator}t=${timestamp}`;
			},
			// 初始化海报配置
			async initPosterConfig() {
				// 先下载图片
				try {
					const imageUrl = this.posterUrl || '/static/image/poster.png';
					const imagePath = await this.downloadImage(imageUrl);
					// const imagePath = imageUrl

					this.posterConfig = {
						width: '750rpx',
						views: [
							// 背景图
							{
								type: 'image',
								src: imagePath,
								css: {
									width: '750rpx',
									objectFit: 'cover',
									top: '0rpx',
									left: '0rpx'
								}
							},
							// 二维码
							{
								type: 'qrcode',
								text: this.currentUrl,
								css: {
									border: '10rpx solid #fff',
									backgroundColor: '#fff',
									width: '150rpx',
									height: '150rpx',
									left: '40rpx',
									bottom: '40rpx',
									position: "absolute"
								}
							}
						]
					}
				} catch (err) {
					console.error('下载图片失败:', err);
					uni.showToast({
						title: '海报生成失败',
						icon: 'none'
					});
				}
			},

			// 海报生成成功回调
			onPosterSuccess(res) {
				// console.log('海报生成成功:', res)
				// 可以保存或预览海报
				this.poserPath = res
				// uni.previewImage({
				// 	urls: [res.path]
				// })
			},

			// 海报生成失败回调
			onPosterFail(err) {
				console.error('海报生成失败:', err)
				uni.showToast({
					title: '海报生成失败',
					icon: 'none'
				})
			},
			number(number) {
				// console.log(number)
			},
			done(e) {
				// console.log(e)
			},
			// 保存海报到相册
			savePoster(path) {
				uni.saveImageToPhotosAlbum({
					filePath: path,
					success: () => {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						})
					},
					fail: () => {
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						})
					}
				})
			},
			downloadImage(url) {
				return new Promise((resolve, reject) => {
					uni.downloadFile({
						url: url,
						success: (res) => {
							if (res.statusCode === 200) {
								resolve(res.tempFilePath);
							} else {
								reject(new Error('Download failed'));
							}
						},
						fail: (err) => {
							reject(err);
						}
					});
				});
			},
			// 复制链接
			copyLink() {
				uni.setClipboardData({
					data: window.location.href,
					success: () => {
						uni.showToast({
							title: '链接已复制',
							icon: 'success'
						});
						this.showSharePopup = false;
					}
				});
			},
			// H5保存海报
			async saveH5Poster() {
				try {
					this.showSharePopup = false;
					// 显示预览图片，提示用户长按保存
					uni.previewImage({
						urls: [this.poserPath],
						longPressActions: {
							itemList: ['保存图片'],
							success: (data) => {
								console.log('选中了第' + (data.tapIndex + 1) + '个按钮');
								this.showSharePopup = false;
							},
							fail: (err) => {
								console.log(err.errMsg);
							}
						}
					});

					// 显示提示
					uni.showToast({
						title: '长按图片可保存到相册',
						icon: 'none',
						duration: 2000
					});

				} catch (err) {
					console.error('保存海报失败:', err);
					uni.showToast({
						title: '保存失败',
						icon: 'none'
					});
				}
			},
			uniShare() {
				console.log('123123')
				// #ifdef H5
				this.showSharePopup = true;
				// #endif

				// #ifdef APP-PLUS
				uniShare.show({
					content: { //公共的分享参数配置  类型（type）、链接（herf）、标题（title）、summary（描述）、imageUrl（缩略图）
						type: 0,
						href: this.currentUrl,
						title: this.activityDetail.title,
						summary: this.activityDetail.brief,
						imageUrl: '/static/app-plus/sharemenu/logo.png'
					},
					menus: [{
							"img": "/static/app-plus/sharemenu/wechatfriend.png",
							"text": "微信好友",
							"share": { //当前项的分享参数配置。可覆盖公共的配置如下：分享到微信小程序，配置了type=5
								"provider": "weixin",
								"scene": "WXSceneSession"
							}
						},
						{
							"img": "/static/app-plus/sharemenu/hp.png",
							"text": "海报",
							"share": { //当前项的分享参数配置。可覆盖公共的配置如下：分享到微信小程序，配置了type=5
								"provider": "weixin",
								"scene": "WXSceneSession",
								"type": 2,
								"imageUrl": this.poserPath || '/static/image/poster.png'

							}
						},
						{
							"img": "/static/app-plus/sharemenu/wechatmoments.png",
							"text": "微信朋友圈",
							"share": {
								"provider": "weixin",
								"scene": "WXSceneTimeline"
							}
						},
						// {
						// 	"img": "/static/app-plus/sharemenu/mp_weixin.png",
						// 	"text": "微信小程序",
						// 	"share": {
						// 		provider: "weixin",
						// 		scene: "WXSceneSession",
						// 		type: 5,
						// 		miniProgram: {
						// 			id: '123',
						// 			path: '/pages/list/detail',
						// 			webUrl: '/#/pages/list/detail',
						// 			type: 0
						// 		},
						// 	}
						// },
						// {
						// 	"img": "/static/app-plus/sharemenu/weibo.png",
						// 	"text": "微博",
						// 	"share": {
						// 		"provider": "sinaweibo"
						// 	}
						// },
						{
							"img": "/static/app-plus/sharemenu/copyurl.png",
							"text": "复制",
							"share": "copyurl"
						},
						{
							"img": "/static/app-plus/sharemenu/more.png",
							"text": "更多",
							"share": "shareSystem"
						}
					],
					cancelText: "取消分享",
				}, e => { //callback
					console.log(e)
					if (e.event == 3) {

					}
					// console.log(uniShare.isShow);
					// console.log(e);
				})
				// #endif
			},
			makephone() {
				// #ifdef APP-PLUS
				requestPermissions({
					title: "拨打电话权限申请说明",
					content: "便于您使用该功能直接拨打电话等场景中使用",
					permissionID: "CALL_PHONE"
				}).then(cameraResult => {
					console.log(cameraResult)
					if (!cameraResult.isSuc) {
						reject(new Error('未获得相机权限'));
						return;
					}
					uni.makePhoneCall({
						phoneNumber: this.activityDetail.phone
					})
				}).catch(error => {
					reject(error);
				});
				// #endif
				// #ifdef H5
				uni.makePhoneCall({
					phoneNumber: this.activityDetail.phone
				})
				// #endif
				// 先申请电话权限


			},
			getElementInfo(id) {
				// 创建选择器查询对象
				const query = uni.createSelectorQuery().in(this);

				// 选择我们想要的元素
				query.select(id).boundingClientRect(data => {
					if (data) {
						if (id == '#jieshao') {
							a = 0
						}
						if (id == '#pingjia') {
							b = data.top - 30
						}
						if (id == '#xiangqing') {
							c = data.top - 100
						}
					}
				}).exec(); // 执行查询

			},
			changeTop(index) {
				this.clickIndex = index
				let top = index == 0 ? a : index == 1 ? b : c
				uni.pageScrollTo({
					scrollTop: top,
					duration: 300
				});
			},
			//操作视频
			setShowVideo(showVideo, isH5) {
				this.showVideo = showVideo
				if (isH5 == true) {
					this.newVideo.play()
				}
				console.log('视频点击播放');
			},
			// 关闭视频
			hideShow() {
				this.showVideo = false
			},
			formatRichText(html) {
				let newContent = html.replace(/<img[^>]*>/gi, function(match, capture) {
					match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
					match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
					match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
					return match;
				});
				newContent = newContent.replace(/style="[^"]+"/gi, function(match, capture) {
					match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi,
						'max-width:100%;');
					return match;
				});
				newContent = newContent.replace(/<br[^>]*\/>/gi, '');
				newContent = newContent.replace(/\<img/gi, '<img style="max-width:99%;"');
				return newContent;
			},
			// 获取活动详情
			async getActivityDetail() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.recentactivityget,
						method: 'GET',
						data: {
							id: this.activityId
						}
					})

					if (res.code === 0 && res.data) {
						this.activityDetail = {
							...res.data,
							review: res.data.review || {
								reviewNum: 0,
								reviewLabelList: []
							}
						}
						// 处理视频和图片数据
						this.processMediaData(res.data)
						console.log('活动详情:', this.activityDetail)
						// 使用方法
						// #ifdef H5

						// this.posterUrl = res.data[0].posterUrl

						this.posterUrl = this.getImageUrlWithTimestamp(res.data.posterUrl);
						// #endif
						// #ifdef APP-PLUS
						this.posterUrl = this.getImageUrlWithTimestamp(res.data.posterUrl);
						// #endif
						// 如果已经有了currentUrl，初始化海报配置
						if (this.currentUrl) {
							this.initPosterConfig().catch(err => {
								console.error('初始化海报配置失败:', err);
							});
						}
					}
				} catch (err) {
					console.error('获取活动详情失败:', err)
				}
			},

			// 处理媒体数据
			processMediaData(data) {
				const imgList = []
				let videoUrl = ''

				// 处理所有图片和视频URL
				if (data.imageUrl && Array.isArray(data.imageUrl)) {
					data.imageUrl.forEach(url => {
						console.log(url.toLowerCase().endsWith('.mp4'))
						// 检查是否是视频文件
						if (url.toLowerCase().endsWith('.mp4')) {
							videoUrl = url
						} else {
							imgList.push(url)
						}
					})
				}

				// 如果有视频封面图，将其作为第一张图片
				if (data.videoCoverUrl) {
					imgList.unshift(data.videoCoverUrl)
				}

				// 更新 goodsData
				this.goodsData = {
					videos: videoUrl,
					imgList: imgList
				}
				this.pageLoading = false
			},

			// 获取评价列表
			getReviewList() {
				this.http.ajax({
					url: this.http.api.reviewpage,
					method: 'GET',
					data: {
						dataId: this.activityId,
						reviewType: 1,
						pageSize: 2, // 主页面只显示2条
						pageNum: 1,
					}
				}).then(res => {
					if (res.code === 0 && res.data) {
						this.reviewList = res.data.list
						this.reviewTotal = res.data.total
					}
				}).catch(err => {
					console.error('获取评价列表失败:', err)
				})
			},

			// 获取弹窗评价列表
			getPopupReviewList() {
				// 重置列表时重置分页参数
				if (this.currentPage === 1) {
					this.popupReviewList = []
					this.hasMore = true
				}

				if (!this.hasMore || this.loading) return

				this.loading = true
				this.http.ajax({
					url: this.http.api.reviewpage,
					method: 'GET',
					data: {
						dataId: this.activityId,
						reviewType: 1,
						pageSize: this.pageSize,
						pageNum: this.currentPage,
						reviewLabel: this.popupSelectedLabel
					}
				}).then(res => {
					if (res.code === 0 && res.data) {
						this.popupReviewList = this.currentPage === 1 ?
							res.data.list :
							[...this.popupReviewList, ...res.data.list]
						this.reviewTotal = res.data.total
						this.hasMore = this.popupReviewList.length < res.data.total
						this.loading = false
					}
				}).catch(err => {
					console.error('获取评价列表失败:', err)
					this.loading = false
				})
			},

			// 处理标签点击
			handleLabelClick(label) {
				// 如果点击已选中的标签，则取消选中
				if (this.popupSelectedLabel === label) {
					this.popupSelectedLabel = ''
				} else {
					this.popupSelectedLabel = label
				}
				this.currentPage = 1
				this.getPopupReviewList()
			},
			// 预览视频
			previewVideo(videoUrl) {
				this.goodsData.videos = videoUrl
				this.showVideo = true
			},

			// 预览图片
			previewImage(urls, current) {
				uni.previewImage({
					urls: urls,
					current: current,
					loop: true,
					indicator: 'number'
				})
			},
			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loading) {
					this.currentPage++
					this.getPopupReviewList()
				}
			},
			// 打开评价弹窗
			showAllReviewsHandler() {
				this.showAllReviews = true
				this.currentPage = 1
				this.popupSelectedLabel = '' // 重置弹窗标签选中状态
				this.getPopupReviewList()
			},
			// 处理返回事件
			handleBackPress() {
				// 检查是否有打开的弹窗
				if (this.showAllReviews) {
					this.showAllReviews = false
					return true
				}
				if (this.showServicePopup) {
					this.showServicePopup = false
					return true
				}
				if (this.showSharePopup) {
					this.showSharePopup = false
					return true
				}
				// 如果没有打开的弹窗，则执行默认的返回行为
				return false
			}
		},

	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #fff;
		height: 100%;
	}
</style>
<style lang="scss" scoped>
	// .u-content {
	// 	padding: 24rpx;
	// }

	.share_btn {
		background-color: #ccc;
		padding: 10upx 15upx;
		border-top-left-radius: 30upx;
		border-bottom-left-radius: 30upx;
		font-size: 24upx;
	}

	.close_btn {
		color: #fff;
		background-color: #ccc;
		width: 100upx;
		height: 50upx;
		text-align: center;
		border-radius: 25upx;
		float: right;
		margin-right: 20upx;
	}

	.fixed-bottom {
		height: 145upx;
		background-color: #fff;
		position: fixed;
		right: 0;
		bottom: 0upx;
		left: 0;
		z-index: 1;
		display: flex;
		flex-flow: row;
		justify-content: space-around;
		align-items: center;
		box-shadow: 10upx 0 15upx rgba(0, 0, 0, 0.16);

	}

	.content {
		display: flex;
		flex-direction: column;

	}

	/* 预览视频弹窗 */
	.mask {
		width: 100%;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		background-color: rgba(0, 0, 0, .8);
		z-ivhndex: 1200;
	}

	.previewvideo {
		width: 100vw;
		height: 100vw;
		position: fixed;
		top: 50%;
		left: 0;
		transform: translateY(-50%);
		background-color: #000;
		z-index: 999999999999999999;
		opacity: 1;
	}

	.close {
		display: flex;
		align-content: center;
		align-items: flex-end;
		position: absolute;
		top: 140upx;
		right: 20upx;
		z-index: 900;

		image {
			width: 50upx;
			height: 50upx;
			display: block;
			justify-content: center;
			margin-left: 30upx;
			margin-bottom: 20upx;
			border-radius: 50%;
			padding: 10upx;
		}
	}

	.videos {
		height: 100vw;
		width: 100vw;
		z-index: 10;
		position: relative;

		video {
			width: 100%;
			height: 100%;
		}
	}

	.click {
		color: #00C1CC;
	}

	.nowvideos {
		width: 100%;
		height: 100%;
		margin: 0 auto;
	}

	.title {
		position: fixed;
		top: 88upx;
		left: 0;
		width: 100%;
		height: auto;
		padding-top: var(--status-bar-height);
		z-index: 10;
		background-color: rgba(66, 185, 131, 0);
		color: rgba(255, 255, 255, 0.8);

		&>view {
			// height: 44upx;
		}

		.box1 {
			width: 60rpx;
			margin: 0 40rpx;
			font-size: 32rpx;
		}


		.tab {
			&>view {
				margin: 0 30rpx;
				line-height: 64rpx;
				font-size: 32rpx;
				position: relative;
				letter-spacing: 0;
				transition: transform 0.3s ease-in-out 0s;
				transform: scale(1, 1);

				&.active {
					color: rgba(255, 255, 255, 1);
					transform: scale(1.15, 1.15);
				}
			}
		}

		&.style2 {
			color: #000;
			font-size: 32upx;
			background-color: rgba(66, 185, 131, 1);

			.tab {
				&>view {
					&.active {
						color: #FFF;
					}
				}
			}
		}
	}

	.label-item {
		background-color: #F5F6F8;
		padding: 10upx 35upx;
		color: #555555;
		width: 220upx;
		text-align: center;
		transition: all 0.3s;

		&.label-active {
			background-color: #00C1CC;
			color: #FFFFFF;
		}
	}

	.review-video-wrap {
		position: relative;
		width: 200upx;
		height: 200upx;
		margin: 20upx 0;
		border-radius: 12upx;
		overflow: hidden;

		.video-cover {
			width: 100%;
			height: 100%;
		}

		.play-icon {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			background-color: rgba(0, 0, 0, 0.5);
			border-radius: 50%;
			width: 80upx;
			height: 80upx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.review-album {
		margin: 20upx 0;

		/deep/ .u-album {
			&__row {
				margin-bottom: 10upx;

				&:last-child {
					margin-bottom: 0;
				}
			}

			&__item {
				border-radius: 12upx;
				overflow: hidden;
			}
		}
	}

	.service-popup {
		width: 100%;
		padding: 30rpx;
		max-height: 80vh;

		&__header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-bottom: 30rpx;
			border-bottom: 1rpx solid #EEEEEE;
		}

		&__title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
		}

		&__content {
			padding: 20rpx 0;
			max-height: calc(80vh - 120rpx);
			overflow-y: auto;
		}
	}

	.service-item {
		margin-bottom: 30rpx;

		&:last-child {
			margin-bottom: 0;
		}

		&__title {
			display: flex;
			align-items: center;
			font-size: 28rpx;
			font-weight: bold;
			color: #333333;
			margin-bottom: 10rpx;
		}

		&__dot {
			width: 12rpx;
			height: 12rpx;
			border-radius: 50%;
			background-color: #00C1CC;
			margin-right: 16rpx;
		}

		&__desc {
			font-size: 26rpx;
			color: #666666;
			line-height: 1.6;
			padding-left: 28rpx;
		}
	}

	.all-reviews {
		height: 90vh;
		background-color: #fff;
		display: flex;
		flex-direction: column;
		position: relative;
		z-index: 999;

		&__header {
			padding: 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			// border-bottom: 1rpx solid #EEEEEE;

			.header-left {
				display: flex;
				align-items: center;
				gap: 20rpx;
			}

			.header-title {
				font-size: 32rpx;
				font-weight: bold;
			}

			.review-count {
				font-size: 24rpx;
				color: #999;
			}
		}
	}

	.review-tabs {
		padding: 20rpx 30rpx;
		white-space: nowrap;
		// border-bottom: 1rpx solid #EEEEEE;

		.tab-list {
			display: inline-flex;
			flex-wrap: wrap;
			gap: 20rpx;
		}

		.tab-item {
			padding: 10rpx 30rpx;
			font-size: 26rpx;
			color: #666;
			background-color: #F5F6F8;
			border-radius: 26rpx;
			transition: all 0.3s; // 添加过渡效果

			&.tab-active {
				color: #fff;
				background-color: #00C1CC;
			}
		}
	}

	.review-list {
		box-sizing: border-box;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
		padding: 0 30rpx;
		position: relative;
		z-index: 1;

		.review-item {
			padding: 30rpx 0;
			border-bottom: 1rpx solid #EEEEEE;

			&:last-child {
				color: #00C1CC;
				border-bottom: none;
			}
		}

		.review-user {
			display: flex;
			align-items: center;
			gap: 20rpx;
			margin-bottom: 20rpx;

			.user-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
			}

			.user-info {
				display: flex;
				flex-direction: column;
				gap: 6rpx;
			}

			.user-name {
				font-size: 28rpx;
				font-weight: bold;
			}

			.review-time {
				font-size: 24rpx;
				color: #999;
			}
		}

		.review-tags {
			margin-bottom: 16rpx;

			.review-tag {
				display: inline-block;
				padding: 6rpx 20rpx;
				font-size: 22rpx;
				color: #8E8E8E;
				background-color: #F5F6F8;
				border-radius: 4rpx;
			}
		}

		.review-content {
			font-size: 28rpx;
			line-height: 1.6;
			margin-bottom: 20rpx;
		}

		.review-media {
			margin-bottom: 20rpx;
		}
	}

	.load-more {
		text-align: center;
		padding: 20rpx 0;
		color: #999;
		font-size: 24rpx;
	}

	/* H5 分享弹窗样式 */
	.share-popup {
		padding: 30rpx;

		.share-title {
			text-align: center;
			font-size: 28rpx;
			color: #999;
			margin-bottom: 30rpx;
		}

		.share-options {
			display: flex;
			justify-content: space-around;
			padding: 20rpx 0 40rpx;

			.share-item {
				display: flex;
				flex-direction: column;
				align-items: center;

				image {
					width: 80rpx;
					height: 80rpx;
					margin-bottom: 16rpx;
				}

				text {
					font-size: 24rpx;
					color: #333;
				}
			}
		}

		.share-cancel {
			height: 90rpx;
			line-height: 90rpx;
			text-align: center;
			font-size: 28rpx;
			color: #333;
			border-top: 1rpx solid #eee;

			&:active {
				background-color: #f5f5f5;
			}
		}
	}

	.close {
		display: flex;
		align-content: center;
		align-items: flex-end;
		position: absolute;
		top: 88upx;
		right: 20upx;
		z-index: 900;

		image {
			width: 50upx;
			height: 50upx;
			display: block;
			justify-content: center;
			margin-left: 30upx;
			margin-bottom: 20upx;
			border-radius: 50%;
			padding: 10upx;
			// background-color: rgba(0, 0, 0, 0.2);
		}
	}
</style>