<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>本地websocket测试</title>
    <meta name="robots" content="all"/>
    <meta name="keywords" content="本地,websocket,测试工具"/>
    <meta name="description" content="本地,websocket,测试工具"/>
    <style>
        .btn-group {
            display: inline-block;
        }
    </style>
</head>
<body>
<p>ws://manage.boxuehao.cn:6808/websocket/agent/291/1 </p> 
<p>ws://127.0.0.1:48080/websocket/agent/291/1 </p> 
<input type='text' value='ws://127.0.0.1:48080/websocket/agent/291/1' class="form-control" style='width:390px;display:inline'
       id='wsaddr'/>
<div class="btn-group">
    <button type="button" class="btn btn-default" onclick='addsocket();'>连接</button>
    <button type="button" class="btn btn-default" onclick='closesocket();'>断开</button>
    <button type="button" class="btn btn-default" onclick='$("#wsaddr").val("")'>清空</button>
</div>
<div class="row">
    <div id="output" style="border:1px solid #ccc;height:365px;overflow: auto;margin: 20px 0;"></div>
    <!-- <input type="text" id='message' class="form-control" style='width:810px' placeholder="待发信息" onkeydown="en(event);">-->
    <input type="file" id="fileInput" accept=".mp3">

    <input type="text" id='message' class="form-control" style='width:810px' placeholder="待发信息" onkeydown="en(event);">
    <span class="input-group-btn">
        <button class="btn btn-default" type="button" onclick="doSend();">发送</button>
    </span>
</div>
</div>
<audio id="audioPlayer" controls></audio>

</body>

<script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
<script language="javascript" type="text/javascript">

    const MAX_CHUNK_SIZE = 1024;

    document.getElementById('fileInput').addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (!file) {
            return;
        }
        if (!file.type.match('audio/mpeg')) {
            alert('Please select an MP3 file.');
            return;
        }
        const reader = new FileReader();
        reader.onload = function(e) {
            const arrayBuffer = e.target.result;
            const byteArrayAA = new Uint8Array(arrayBuffer);
            console.log(byteArrayAA);
            const byteArray = new Uint8Array([255, 255]);
            console.log(byteArray);
            let offset = 0;
            while (offset < byteArrayAA.byteLength) {
                const end = Math.min(offset + MAX_CHUNK_SIZE, byteArrayAA.byteLength);
                const chunk = byteArrayAA.slice(offset, end);
                //ws.send(chunk.buffer);
                websocket.send(chunk.buffer);
                offset = end;
            }
            // 你现在可以使用byteArray进行后续操作
            websocket.send(byteArray.buffer);
        };

        reader.onerror = function(error) {
            console.error('Error reading file:', error);
        };

        // 开始读取文件
        reader.readAsArrayBuffer(file);
    });

    function formatDate(now) {
        var year = now.getFullYear();
        var month = now.getMonth() + 1;
        var date = now.getDate();
        var hour = now.getHours();
        var minute = now.getMinutes();
        var second = now.getSeconds();
        return year + "-" + (month = month < 10 ? ("0" + month) : month) + "-" + (date = date < 10 ? ("0" + date) : date) +
            " " + (hour = hour < 10 ? ("0" + hour) : hour) + ":" + (minute = minute < 10 ? ("0" + minute) : minute) + ":" + (
                second = second < 10 ? ("0" + second) : second);
    }

    var output;
    var websocket;
    var audioCtx = new (window.AudioContext || window.webkitAudioContext)();
    var source = null;

    function init() {
        output = document.getElementById("output");
        testWebSocket();
    }

    function addsocket() {
        var wsaddr = $("#wsaddr").val();
        if (wsaddr == '') {
            alert("请填写websocket的地址");
            return false;
        }
        StartWebSocket(wsaddr);
    }

    function closesocket() {
        websocket.close();
    }

    function StartWebSocket(wsUri) {
        websocket = new WebSocket(wsUri);
        websocket.onopen = function (evt) {
            onOpen(evt)
        };
        websocket.onclose = function (evt) {
            onClose(evt)
        };
        websocket.onmessage = function (evt) {
            onMessage(evt)
        };
        websocket.onerror = function (evt) {
            onError(evt)
        };
    }

    function onOpen(evt) {
        writeToScreen("<span style='color:red'>连接成功，现在你可以发送信息啦！！！</span>");
    }

    function onClose(evt) {
        writeToScreen("<span style='color:red'>websocket连接已断开!!!</span>");
        websocket.close();
    }


    var audioSources = [];
    let currentTrackIndex = 0;
    var isPlaying = false;

    function onMessage(event) {
        /* writeToScreen('<span style="color:blue">服务端回应&nbsp;' + formatDate(new Date()) + '</span><br/><span class="bubble">' +
             <evt className="dat"></evt> + '</span>');*/
        if (typeof event.data === 'string') {
            writeToScreen('<span style="color:blue">' + event.data + '&nbsp;' + '</span>')
            return;
        }


        //writeToScreen('<span style="color:blue">服务端有回应&nbsp;'+ '</span>')
        // 假设event.data是一个byte数组
        // 如果你的音频数据是PCM格式，并且已经是Uint8Array，则可以跳过这一步
        // 假设这里的bytes是从WebSocket接收到的原始音频数据
        const audioBlob = new Blob([event.data], {type: 'audio/wav'}); // 假设音频格式为wav
        const audioUrl = URL.createObjectURL(audioBlob);
        audioSources.push(audioUrl);
        if (!isPlaying) {
            audioPlayer.src = audioSources[currentTrackIndex];
            audioPlayer.play();
        }
        isPlaying = true;
        /*// 音频加载完成后自动播放
        audioPlayer.onloadedmetadata = function() {
            audioPlayer.play();
        };*/
    }


    // 获取audio元素并设置其src属性为音频URL
    const audioPlayer = document.getElementById('audioPlayer');
    audioPlayer.addEventListener('ended', playNextTrack);

    function playNextTrack() {
        currentTrackIndex++;
        if (currentTrackIndex < audioSources.length) {
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.src = audioSources[currentTrackIndex];
            audioPlayer.play();
        } else {
            // 如果是最后一首音频，可以选择重新开始或停止
            currentTrackIndex = 0;
            //playNextTrack();
        }
    }


    function onError(evt) {
		console.log('<span style="color: red;">发生错误:</span> ' , evt)
        writeToScreen('<span style="color: red;">发生错误:</span> ' );
    }

    function doSend() {
        // 获取当前时间的时间戳（毫秒）
        const currentTimeStamp = Date.now();

        // 打印时间戳
        console.log("发送消息时间戳" + currentTimeStamp);
        var message = $("#message").val();
        if (message == '') {
            alert("请先填写发送信息");
            $("#message").focus();
            return false;
        }
        if (typeof websocket === "undefined") {
            alert("websocket还没有连接，或者连接失败，请检测");
            return false;
        }
        if (websocket.readyState == 3) {
            alert("websocket已经关闭，请重新连接");
            return false;
        }
        console.log(websocket);
        $("#message").val('');
        writeToScreen('<span style="color:green">你发送的信息&nbsp;' + formatDate(new Date()) + '</span><br/>' + message);
        // 创建一个新的 TextEncoder 实例
        const encoder = new TextEncoder();
        const uint8Array = encoder.encode(message);
        const byteArray = new Uint8Array([255, 255]);

        websocket.send(uint8Array);
        // 你现在可以使用byteArray进行后续操作
        websocket.send(byteArray.buffer);
    }

    /*function doSend() {
        audioSources = [];
        currentTrackIndex = 0;
        isPlaying = false;
        if (typeof websocket === "undefined") {
            alert("websocket还没有连接，或者连接失败，请检测");
            return false;
        }
        if (websocket.readyState == 3) {
            alert("websocket已经关闭，请重新连接");
            return false;
        }
        console.log(websocket);
        $("#message").val('');
        writeToScreen('<span style="color:green">你已发送的信息&nbsp;' + formatDate(new Date()) + '</span><br/>');


        // 创建一个二进制消息
        const byteArray = new Uint8Array([72, 101, 108, 108, 111]);


        //websocket.send(message);
        websocket.send(byteArray.buffer);
    }*/

    function writeToScreen(message) {
        var div = "<div class='newmessage'>" + message + "</div>";
        var d = $("#output");
        var d = d[0];
        var doScroll = d.scrollTop == d.scrollHeight - d.clientHeight;
        $("#output").append(div);
        if (doScroll) {
            d.scrollTop = d.scrollHeight - d.clientHeight;
        }
    }


    function en(event) {
        var evt = evt ? evt : (window.event ? window.event : null);
        if (evt.keyCode == 13) {
            doSend()
        }
    }
</script>

</html>
