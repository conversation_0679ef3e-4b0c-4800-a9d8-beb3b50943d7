<template>
	<view style="position: relative;">


		<view class="content"></view>

		<u-steps current="2" active-color="#397afe" class="mt20 mb20">
			<u-steps-item title="身份认证">
			</u-steps-item>
			<u-steps-item title="实名认证"></u-steps-item>
			<u-steps-item title="完善信息"></u-steps-item>
		</u-steps>

		<view style="flex-direction: column;padding-bottom: 120upx;" class="dis-ali jc_cen  ">
			<view style="background-color: #fff;width: 100%;">
				<view
					style="font-size: 30upx;font-weight: 700;margin: 30upx 0 0;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);padding-bottom: 30upx;"
					class="dis-ali">
					<span style="border-left:10upx solid #397afe;padding-left: 20upx;">单位类型</span><!-- <u-icon
						name="info-circle" size="17" :bold="false" style="margin-left: 18upx ;"
						@click="show=true"></u-icon> -->
				</view>
				<view style="height: 90upx;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);justify-content: space-around;"
					class="dis-ali">
					<view class='onelist_li' :class="{'select_li':form.unit_type==index+1}" style="" @click="change(index,1)"
						v-for="(item,index) in oneList">{{item}}</view>

				</view>
				<view style="height: 90upx;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" class="dis-ali" v-if="form.unit_type!=3">
					<u-input v-model="form.unit_name" placeholder="请输入单位名称" prefixIcon="phone" style="width:710upx;border:0;"
						prefixIconStyle="font-size: 22px;color: #909399" fontSize='16px'>
						<template slot="prefix">
							<span style="width: 150upx;">单位名称</span>
						</template>
					</u-input>
				</view>
				<view style="font-size: 30upx;margin: 30upx 0 0;padding-bottom: 20upx;" class="dis-ali">
					<span style="padding-left: 15upx;">月收入</span><!-- <u-icon name="info-circle" size="17" :bold="false"
						style="margin-left: 18upx ;" @click="show=true"></u-icon> -->
				</view>
				<view
					style="border-bottom: 1upx solid rgba(0, 0, 0, 0.06);justify-content: space-between;flex-wrap: wrap; padding: 10upx 20upx ;"
					class="dis-ali">
					<view class='twolist_li' :class="{'select_li':form.monthly_income==index+1}" style="" @click="change(index,2)"
						v-for="(item,index) in twoList">{{item}}</view>
						

				</view>
			</view>

			<view style="background-color: #fff;width: 100%;margin:20upx 0;">
				<view
					style="font-size: 30upx;font-weight: 700;margin: 30upx 0 0;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);padding-bottom: 30upx;"
					class="dis-ali">
					<span style="border-left:10upx solid #397afe;padding-left: 20upx;">家庭地址</span><!-- <u-icon
						name="info-circle" size="17" :bold="false" style="margin-left: 18upx ;"
						@click="show=true"></u-icon> -->
				</view>
				<view style="height: 90upx;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" class="dis-ali"
					>
					<view @click="addressShow=true">
						<u-input v-model="address" @click="addressShow=true" placeholder="请选择地址" disabledColor='#fff'  prefixIcon="phone"
							style="width:710upx;border:0;" prefixIconStyle="font-size: 22px;color: #909399" fontSize='16px'>
							<template slot="prefix">
								<span style="width: 150upx;">所在地区</span>
							</template>
						</u-input>
					</view>
					
				</view>
				<view style="height: 90upx;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" class="dis-ali">
					<u-input v-model="form.address" placeholder="详细的街道,楼牌号等" prefixIcon="phone" style="width:710upx;border:0;"
						prefixIconStyle="font-size: 22px;color: #909399" fontSize='16px'>
						<template slot="prefix">
							<span style="width: 150upx;">详细地址</span>
						</template>
					</u-input>
				</view>

	
				<view>
					<address-picker :show="addressShow" closeOnClickOverlay @confirm='confirmAddress'
						@cancel='addressShow=false' @close='addressShow = false' :address-data="addressData"
						:indexs="indexs" :areaId="areaId" :type="type"></address-picker>
				</view>
				<view
					style="font-size: 30upx;font-weight: 700;margin: 30upx 0 0;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);padding-bottom: 30upx;"
					class="dis-ali">
					<span style="border-left:10upx solid #397afe;padding-left: 20upx;">家庭联系人</span><!-- <u-icon
						name="info-circle" size="17" :bold="false" style="margin-left: 18upx ;"
						@click="show=true"></u-icon> -->
				</view>
				<view class="dis-ali" style="border-bottom: 1upx solid rgba(0, 0, 0, 0.06);">
					<view>选择关系</view>
					<view
						style="justify-content: space-between; padding: 0 20upx ;"
						class="dis-ali">
						<view class='threelist_li' :class="{'select_li':form.contacts_type==index+1}" style="" @click="change(index,3)"
							v-for="(item,index) in threeList">{{item}}</view>
					
					</view>
				</view>
				
				<view style="height: 90upx;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" class="dis-ali">
					<u-input v-model="form.contacts_name" placeholder="请输入联系人姓名" prefixIcon="phone" style="width:710upx;border:0;"
						prefixIconStyle="font-size: 22px;color: #909399" fontSize='16px'>
						<template slot="prefix">
							<span style="width: 150upx;">姓名</span>
						</template>
					</u-input>
				</view>
				<view style="height: 90upx;border-bottom: 1upx solid rgba(0, 0, 0, 0.06);" class="dis-ali">
					<u-input v-model="form.contacts_phone"  placeholder="仅用于紧急情况联系" prefixIcon="phone" style="width:710upx;border:0;"
						prefixIconStyle="font-size: 22px;color: #909399" fontSize='16px'>
						<template slot="prefix">
							<span style="width: 150upx;">联系电话</span>
						</template>
					</u-input>
				</view>
			</view>
			
		
			

			<view @click="goUrl(1)"
				style="font-size: 34upx;upx;color: #fff;background-color: #397afe;width: 680upx;height: 100upx;border-radius: 10upx;display: flex;align-items: center;justify-content: center;margin-top: 30upx;">
				提交
			</view>
			
			<u-popup :show="show1" mode="center" round='5'>
				<view style="padding: 40upx;width: 600upx;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">关于个人信息处理规则说明</text>
					<view style="line-height: 1.7;text-indent: 2em;margin-top: 20upx;font-size: 28upx;">
						同意/允许融信生活通过语音/短信方式向我推荐我可能感兴趣的内容，包括但不限于:会员权益服务、促销、活动、产品等信息。 如不希望接收上述信息，可联系融信客服处理</view>

					<view @click="show = false" class='btn'>
						我知道了
					</view>

				</view>

			</u-popup>
			<u-popup :show="show" mode="center" round='5'>
				<view style="padding: 40upx;width: 600upx;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">关于个人信息处理规则说明</text>
					<view style="line-height: 1.7;text-indent: 2em;margin-top: 20upx;font-size: 28upx;">
						同意/允许融信生活通过语音/短信方式向我推荐我可能感兴趣的内容，包括但不限于:会员权益服务、促销、活动、产品等信息。 如不希望接收上述信息，可联系融信客服处理</view>
					<view @click="show = false" class='btn'>
						我知道了
					</view>
				</view>

			</u-popup>
			<u-popup :show="show2" mode="bottom" round='5' :closeable='true'>
				<view style="padding: 40upx;width: 100%;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">查看和下载身份证照片</text>
					<view>
						<image src="../../static/image/zfb.jpg" mode="widthFix" style="width: 710upx;"></image>
					</view>
					<view @click="show2 = false" class="btn">
						我知道了
					</view>
				</view>

			</u-popup>
			<view
				style="width: 710upx;border-radius:20upx;padding-bottom: 30upx;margin-top: 20upx;padding: 30upx 30upx ;position: absolute;bottom: 0upx;">
				<view @click='goUrl(10)' class="dis-ali " style="justify-content:center;align-items: center;">
			
					<view
						style="padding: 15upx 40upx;color: #939393;border-radius: 50upx;font-size: 24upx;border: 1upx solid rgba(186, 186, 186, 0.3);background-color: #fff;"
						class="dis-ali">
						<u-icon name="server-fill" color="#397afe" style="margin-right: 10upx;"></u-icon>
						我的客服
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				list1: [
					'../../static/image/20230208195156793b17.png',
					'../../static/image/202302081952019c0ab4.png',
				],
				show: false,
				btn: [],
				show1: false,
				tips: '',
				value: '',
				show2: false,
				address: '',
				addressShow: false,
				indexs: [0, 0, 0],
				areaId: [1, 110000, 110101],
				addressData: ['北京市', '北京市', '东城区'],
				type: 3, //1-省，2-省市，3-省市区,
				oneList: [
					'单位员工',
					'个体经营',
					'自由职业',
			
				],
				twoList: [
					'3000及以下',
					'3001-5000',
					'5001-8000',
					'80001-10000',
					'10001-20000',
					'20000以上',
				],
				threeList: [
					'父母',
					'配偶',
					'子女',
					'兄弟',
					'姐妹'
				],
				oneindex: 0,
				form:{
					name:uni.getStorageSync('info').realname,
					idcard:uni.getStorageSync('info').idcard,
					unit_type:'',
					unit_name:'',
					monthly_income:'',
					area:{
						province:'',
						city:'',
						area:'',
					},
					address:'',
					contacts_type:'',
					contacts_name:'',
					contacts_phone:'',
				}
			}
		},
		watch: {
			value(newValue, oldValue) {
				// console.log('v-model', newValue);
			}
		},
		onLoad() {
			that = this;
		},
		methods: {
			
			confirmAddress(val) {
				console.log(val);
				if (val.value[0] == val.value[1]) {
					this.address = val.value[0] + val.value[2]
				} else {
					this.address = val.value.join('')
				}
				this.form.area.province=val.value[0]
				this.form.area.city=val.value[1]
				this.form.area.area=val.value[2]
				this.addressShow = false
			},
			ChangeIntegral() {

			},
			codeChange(text) {
				this.tips = text;
			},
			change(e,type) {
				if(type==1){
					this.form.unit_type = e+1
				}
				if(type==2){
					this.form.monthly_income = e+1
				}
				if(type==3){
					this.form.contacts_type = e+1
				}
				console.log('change', e);
			},
			goUrl(type) {
				if(type==10){
					uni.navigateTo({
						url:'/pages/kefu/kefu'
					})
				} 
				if (type == 1) {
					
					that.http.ajax({
						url: that.http.api.limitapply,
						method: 'POST',
						data: that.form,
						success(res) {
							if (res.code == 200) {
								that.http.info()
								uni.showModal({
									title:'提示',
									content:'申请成功，请耐心等待额度批复',
									showCancel:false,
									success(res) {
										if(res.confirm){
											uni.switchTab({
												url: '/pages/index/index'
											})
										}
									}
								})
							
								
							} else {
								uni.showToast({
									title: res.message,
									icon: 'none'
								})
							}
						}
					})
					
				}
			},
		}
	}
</script>
<style>
	page,
	body {
		background-color: #f3f3f3;
		/* background-color: #fff; */
		/* height: 100%; */
	}
</style>

<style lang="scss">
	.content {
		// z-index: 1;
		// position: absolute;
		// top: 0;
		// width: 100%;
		// // height: 500upx;
		// background-image: linear-gradient(180deg,#397afe,#f3f3f3);
	}

	.btn {
		font-size: 34upx;
		color: #fff;
		background-color: #397afe;
		width: 500upx;
		height: 100upx;
		border-radius: 10upx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30upx;
	}

	.onelist_li {
		margin: 10upx 0;
		border: 1upx solid #939393;
		padding: 7upx 60upx;
		border-radius: 50upx;
		font-size: 26upx;
		color: #939393;
	}
	.twolist_li {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 10upx 0;
		width: 230upx;
		border: 1upx solid #939393;
		padding: 7upx 20upx;
		border-radius: 50upx;
		font-size: 26upx;
		color: #939393;
	}
	.threelist_li {
		margin: 20upx 10upx 20upx  0upx;
		border: 1upx solid #939393;
		padding: 12upx 25upx;
		border-radius: 50upx;
		font-size: 26upx;
		color: #939393;
	}
	.select_li {
		color: #fff;
		background-color: #397afe;
		border: 0;
	}
</style>