<template>
	<view>
		<z-paging  :show-scrollbar="false"   refresher-background='#fffff00'  ref="paging" refresher-only  @onRefresh="onRefresh" :use-page-scroll='true'  @query="queryList" >
			<view slot="top">
				<u-navbar :title="navtitle" height="44" :bgColor="bgColor" :placeholder='true' :auto-back="true">
				</u-navbar>
				<my-nav :title='navtitle' :pageScrollTop='pageScrollTop'></my-nav>
				<!-- 头部nav -->
			</view>
			<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
			<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->

			<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->
			<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
			<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
			<template #refresher="{refresherStatus}">
				<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
				<custom-refresher :status="refresherStatus" color="#000" />
			</template>
			<!-- 自定义没有更多数据view -->
			<template #loadingMoreNoMore>
				<!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
				<custom-nomore />
			</template>
			<view v-if="loading">
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 1,
						headHeight: '200rpx',
						textRows:2,
						gridRows:8,
						textShow:false
					}">
					<view></view>
				</x-skeleton>
			</view>
			<view v-else style="min-height: 100vh;">
				<view style="width: 100%;padding: 30upx 20upx;">
					<!-- 空数据显示 -->
					<view v-if="messageList.length === 0" class="empty-box">
						<image src="/static/empty/list.png" mode="aspectFit" class="empty-image"></image>
						<text class="empty-text">暂无消息</text>
					</view>
					
					<!-- 消息列表 -->
					<view v-else>
						<view class="dis-ali flex-column" style="font-size: 26upx;margin-bottom: 30upx;width: 100%;"
							v-for="item in messageList" @click="goUrl(item.templateContent,item.id)">
							<view class="mb10 com-fontsize-24" style="color: rgba(139, 146, 156, 1);">
								{{item.createTime}}
							</view>
						<view class=" flex-column jc_bet"
							style="display: flex;padding: 30upx;background-color: #fff;width: 100%;border-radius: 10upx;">
							<view class="com-fontsize-32 font-bold">{{item.templateName}}</view>
							<view class="com-fontsize-28 mt10 mb10 borderB pb10 com-color-163">{{item.templateBrief}}</view>
							<view class="dis-ali jc_bet">
								<view class="com-color-163">查看详情</view>
								<view>
									<u-icon name='arrow-right' color="#9B9B9B" size="14px"></u-icon>
								</view>
							</view>
				
						</view>
					</view>
					</view>
				</view>
				
			</view>
					<!-- 学习列表 -->
					<u-toast ref="uToast"></u-toast>
					<!-- toast -->
					<!-- <u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
						:loading="false"></u-loading-page> -->
					<!-- 加载页 -->
					<!-- <my-bottom></my-bottom> -->
					<!-- </bottom> -->
			
		</z-paging>
	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				loading: true,
				status: 'nomore',
				page: 1,
				pageSize: 10,
				total: 0,
				displayedText: '',
				typingInterval: null,
				typingIndex: 0,
				pageScrollTop: 0,
				bgColor: '#fff',
				navtitle: '',
				messageList: [],
				type: ''
			}
		},
		onLoad(option) {
			that = this;
			this.navtitle = option.content
			this.type = option.type
			// 初始化加载
			this.$nextTick(() => {
				this.$refs.paging.reload()
			})
			setTimeout(() => this.loading = false, 1000)
		},
		onPageScroll(e) {
			this.pageScrollTop = Math.floor(e.scrollTop);
			// this.$refs.paging.updatePageScrollTop(e.scrollTop);
		},
		onReachBottom(e) {
			console.log(e)
			this.$refs.paging.complete();
		},
		// 	},
		onShow() {
			
		},
		beforeDestroy() {
			
		},
		onHide() {
			
		},
		methods: {
			// z-paging 请求列表数据
			async queryList(pageNo, pageSize) {
				const { list, total } = await this.getMessages(pageNo)
				this.$refs.paging.complete(list)
			},
			// 获取消息列表
			async getMessages(pageNo) {
				try {
					const res = await this.http.ajax({
						url: this.http.api.notifymessagepage,
						data: {
							type: this.type,
							pageNo: pageNo,
							pageSize: this.pageSize
						},
						method: 'GET'
					})
					
					if(res.code === 0) {
						// 格式化时间
						const formattedList = res.data.list.map(item => ({
							...item,
							createTime: this.formatTime(item.createTime)
						}))
						
						// 更新分页数据
						this.total = res.data.total
						this.messageList = pageNo === 1 ? formattedList : [...this.messageList, ...formattedList]
						return {
							list: formattedList,
							total: res.data.total
						}
					}
					return {
						list: [],
							total: 0
					}
				} catch(err) {
					console.error('获取消息列表失败:', err)
					return {
						list: [],
						total: 0
					}
				}
			},
			// 下拉刷新
			async onRefresh() {
				try {
					const { list, total } = await this.getMessages(1)
					this.$refs.paging.complete(list)
				} catch(err) {
					this.$refs.paging.complete(false)
				}
			},
			// 上拉加载更多
			async loadMore(pageNo) {
				try {
					const { list, total } = await this.getMessages(pageNo)
					this.$refs.paging.complete(list)
				} catch(err) {
					this.$refs.paging.complete(false)
				}
			},
			// 格式化时间戳
			formatTime(timestamp) {
				if(!timestamp) return ''
				const date = new Date(timestamp)
				const year = date.getFullYear()
				const month = (date.getMonth() + 1).toString().padStart(2, '0')
				const day = date.getDate().toString().padStart(2, '0')
				const hour = date.getHours().toString().padStart(2, '0')
				const minute = date.getMinutes().toString().padStart(2, '0')
				return `${year}-${month}-${day} ${hour}:${minute}`
			},
			goUrl(content,id) {
				uni.navigateTo({
					url:'message?content='+content+'&id='+id
				})
			}
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #f3f3f3;
		/* background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 60%; */
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>
.empty-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;

	.empty-image {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 20rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
}
</style>