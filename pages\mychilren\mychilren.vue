<template>
	<view>
		<z-paging  :show-scrollbar="false"   refresher-background='#fffff00'  ref="paging"    :use-page-scroll='true'  >
			<view slot="top">
				<!-- 头部nav -->
			</view>
			<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
			<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->

			<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->
			<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
			<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
			<template #refresher="{refresherStatus}">
				<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
				<custom-refresher :status="refresherStatus" color="#000" />
			</template>
			<!-- 自定义没有更多数据view -->
			<template #loadingMoreNoMore>
				<!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
				<custom-nomore />
			</template>
			<view v-if="loading">
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 1,
						headHeight: '200rpx',
						textRows:2,
						gridRows:8,
						textShow:false
					}">
					<view></view>
				</x-skeleton>
			</view>
			<view v-else >
				<view style="width: 100%;padding: 30upx;">
				
					<view class="child-card" v-for="item in childrenList" :key="item.id" style="position: relative;">
						<view class="dis-ali jc_cen item_select" v-if="item.isCurrent">
							默认
						</view>
						<view class="card-content">
							<!-- 顶部区域：头像和基本信息 -->
							<view class="child-header">
								<view class="avatar-container">
									<image :src="item.headPortrait" class="avatar" mode="aspectFill"></image>
								</view>
								<view class="basic-info">
									<view class="child-name">{{item.childName}}</view>
									<view class="child-birthday">{{item.birthday}}</view>
								</view>
							</view>
							
							<!-- 标签区域：性别、年级、特长等 -->
							<view class="tags-container" >
								
								<!-- 性别标签，只有当存在性别数据时才显示 -->
								<view class="tag" :class="item.sex == 1 ? 'tag-boy' : 'tag-girl'" v-if="item.sex !== undefined">
									<u-icon :name="item.sex == 1 ? 'man' : 'woman'" size="18"></u-icon>
									<text>{{item.sex == 1 ? '男孩' : '女孩'}}</text>
								</view>
								
								<!-- 年级标签 -->
								<view class="tag tag-grade" v-if="item.gradeTitle">
									<u-icon name="bookmark" size="18"></u-icon>
									<text>{{item.gradeTitle}}</text>
								</view>
								
								<!-- 特长标签，如果有特长数据的话 -->
								<view class="tag tag-talent" v-if="item.attns">
									<u-icon name="star" size="18"></u-icon>
									<text>{{item.attns.length > 8 ? item.attns.substring(0,8) + '...' : item.attns}}</text>
								</view>
							</view>
							
							<!-- 操作按钮区域 -->
							<view class="actions-container">
								<view class="action-btn change-btn" @click="change(item.id)" v-if="item.isCurrent!=1">
									<u-icon name="edit-pen" color="#ffb300"></u-icon>
									<text>切换</text>
								</view>
								<view class="action-btn edit-btn" @click="goAdd(item.childName,item.id)">
									<u-icon name="edit-pen" color="#00C1CC"></u-icon>
									<text>编辑</text>
								</view>
								<view class="action-btn delete-btn" @click="deleteChild(item.id)">
									<u-icon name="trash" color="#ff5a5f"></u-icon>
									<text>删除</text>
								</view>
							</view>
						</view>
					</view>
				
				</view>
				<u-empty
						v-if="!loading && (!childrenList || childrenList.length === 0)"
						text="暂无孩子信息"
				        mode="list"
				        icon="../../static/empty/list.png"
				>
				</u-empty>
				<view class="fixed-bottom">
					<u-button shape='circle' size="small" class="add-btn"
						type="primary" @click='goAdd'>新增孩子</u-button>
				</view>
			
				<!-- <u-loadmore :status="status" /> -->
			</view>
					<!-- 列表 -->
					<u-toast ref="uToast"></u-toast>
					<!-- toast -->
					<!-- 加载页 -->
					<!-- <my-bottom></my-bottom> -->
					<!-- </bottom> -->
			
		</z-paging>
	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				loading: true,
				status: 'nomore',
				page: 1,
				displayedText: '',
				typingInterval: null,
				typingIndex: 0,
				pageScrollTop: 0, // 页面滚动距离
				bgColor: 'rgba(255,255,255,0.01)',
				navtitle: '我的孩子',
				childrenList: [], // 孩子列表
			}
		},
		onShow() {
			that = this;
			this.getChildrenList();
		},
		onPageScroll(e) {
			this.pageScrollTop = Math.floor(e.scrollTop);
			// this.$refs.paging.updatePageScrollTop(e.scrollTop);
		},
		onReachBottom(e) {
			console.log(e)
			this.$refs.paging.complete();
		},
		// 	},
		beforeDestroy() {
			
		},
		onHide() {
			
		},
		methods: {
			change(id){
				uni.showModal({
					title: '提示',
					content: '请确认是否更换选中孩子？',
					success: (res) => {
						if(res.confirm) {
							that.http.ajax({
								url: that.http.api.switchChild,
								method: 'POST',
								data: {
									id:id
									},
								success: (res) => {
									console.log(res)
									if (res.code == 0) {
										this.$u.toast('切换成功')
										this.getChildrenList();
									} else {
										uni.showToast({
											title: res.msg,
											icon: 'none'
										})
									}
								}
							})
						}
					}
				})
			},
			deleteChild(id) {
				uni.showModal({
					title: '提示',
					content: '确定要解绑该孩子吗？',
					success: (res) => {
						if(res.confirm) {
							console.log(that.http.api.removeChild)
							// return;
							this.doDeleteChild(id)
						}
					}
				})
			},
			// 执行解绑操作
			doDeleteChild(id) {
				that.http.ajax({
					url: that.http.api.removeChild,
					method: 'POST',
					data: {id:id},
					success: (res) => {
						console.log(res)
						if (res.code == 0) {
							this.$u.toast('删除成功')
							this.getChildrenList();
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			onRefresh() {
				this.getChildrenList();
				setTimeout(() => {
					this.$refs.paging.complete();
				}, 1500)
			},
			goAdd(value,id) {
				uni.navigateTo({
					url:'/pages/children/children'+'?name='+value+'&id='+id
				})
				
			},
			// 获取孩子列表
			async getChildrenList() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.getChild,
						method: 'GET'
					})
					if(res.code === 0) {
						this.childrenList = res.data || []
						this.loading = false
					}
				} catch(err) {
					console.error('获取孩子列表失败:', err)
				}
			},
			
			
			// 编辑孩子信息
			editChild(item) {
				uni.navigateTo({
					url: `/pages/personal/addChild?id=${item.id}`
				})
			},
		}
	}
</script>
<style>
	page,
	body {
		background-color: #f3f3f3;
		/* background-color: #fff; */
		/* background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 60%; */
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>
	.fixed-bottom {
					height: 145upx;
					background-color: rgba(0, 0, 0, 0);
					position: fixed;
					right: 0;
					bottom: 0upx;
					left: 0;
					z-index: 1;
					display: flex;
					flex-flow: row;
					justify-content: space-around;
					align-items: center;
					/* box-shadow: 10upx 0 15upx rgba(0, 0, 0, 0.16); */
				
				}
				
	/* 新增样式 */
	.child-card {
		margin-bottom: 30upx;
		border-radius: 16upx;
		background-color: #fff;
		overflow: hidden;
		box-shadow: 0 4upx 20upx rgba(0, 0, 0, 0.05);
	}
	.item_select{
		width: 115rpx;
		height: 45rpx;
		background: #00C1CC;
		border-radius: 0rpx 20rpx 0rpx 20rpx;
		color: #fff;font-size: 22upx;position: absolute;right: 0;top: 0;
	}
	.card-content {
		padding: 30upx;
	}
	
	.child-header {
		display: flex;
		align-items: center;
		margin-bottom: 20upx;
	}
	
	.avatar-container {
		margin-right: 20upx;
	}
	
	.avatar {
		width: 100upx;
		height: 100upx;
		border-radius: 50%;
		border: 3upx solid #00C1CC;
	}
	
	.basic-info {
		flex: 1;
	}
	
	.child-name {
		font-size: 36upx;
		font-weight: bold;
		color: #333;
		margin-bottom: 6upx;
	}
	
	.child-birthday {
		font-size: 28upx;
		color: #666;
	}
	
	.tags-container {
		display: flex;
		flex-wrap: wrap;
		margin: 20upx 0;
	}
	
	.tag {
		display: flex;
		align-items: center;
		padding: 8upx 16upx;
		border-radius: 30upx;
		margin-right: 16upx;
		margin-bottom: 16upx;
		font-size: 24upx;
	}
	
	.tag-boy {
		background-color: #e8f7ff;
		color: #1e88e5;
	}
	
	.tag-girl {
		background-color: #ffedf1;
		color: #ff5a5f;
	}
	
	.tag-grade {
		background-color: #f0f7ee;
		color: #4caf50;
	}
	
	.tag-talent {
		background-color: #fff8e1;
		color: #ffb300;
	}
	
	.tag u-icon {
		margin-right: 6upx;
	}
	
	.actions-container {
		display: flex;
		justify-content: flex-end;
		margin-top: 20upx;
		padding-top: 20upx;
		border-top: 1px solid #f5f5f5;
	}
	
	.action-btn {
		display: flex;
		align-items: center;
		padding: 10upx 20upx;
		border-radius: 30upx;
		margin-left: 20upx;
	}
	
	.edit-btn {
		background-color: #f0f9fa;
		color: #00C1CC;
	}
	.change-btn {
		background-color: #fff8e1;
		color: #ffb300;
	}
	.delete-btn {
		background-color: #fff0f0;
		color: #ff5a5f;
	}
	
	.action-btn text {
		margin-left: 6upx;
		font-size: 26upx;
	}
	
	.add-btn {
		width: 685upx;
		background-color: #00C1CC;
		border: 0;
		height: 90upx;
	}
</style>