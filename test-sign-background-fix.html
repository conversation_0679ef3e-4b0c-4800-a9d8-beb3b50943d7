<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>登录注册页面背景图片修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #FFA500;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-item {
            margin-bottom: 15px;
            padding: 10px;
            background: #f9f9f9;
            border-left: 4px solid #FFA500;
        }
        .success {
            border-left-color: #4CAF50;
            background: #f1f8e9;
        }
        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        .code-block {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .before-after {
            display: flex;
            gap: 20px;
            margin: 15px 0;
        }
        .before, .after {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
        }
        .before {
            background: #ffebee;
            border: 1px solid #f44336;
        }
        .after {
            background: #f1f8e9;
            border: 1px solid #4CAF50;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔧 登录注册页面背景图片修复</h1>
        
        <div class="test-item success">
            <h3>✅ 问题解决</h3>
            <p><strong>问题描述：</strong>用户点击输入框时，键盘弹起导致背景图片上推，影响用户体验。</p>
            <p><strong>解决方案：</strong>将CSS背景图片改为固定的image标签，使用widthFix模式保持适配。</p>
        </div>

        <div class="test-item">
            <h3>📋 修改详情</h3>
            
            <h4>1. pages/sign/sign.vue 修改：</h4>
            <div class="before-after">
                <div class="before">
                    <h5>❌ 修改前（CSS背景）：</h5>
                    <div class="code-block">
&lt;view class="page-box"&gt;
  &lt;u-navbar...&gt;

.page-box {
  background-image: url(../../static/image/signBack1.png);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
                    </div>
                </div>
                <div class="after">
                    <h5>✅ 修改后（固定图片）：</h5>
                    <div class="code-block">
&lt;view class="page-box"&gt;
  &lt;image 
    src="../../static/image/signBack1.png" 
    mode="widthFix" 
    class="background-image"
  &gt;&lt;/image&gt;
  &lt;u-navbar...&gt;

.page-box {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: -1;
  object-fit: cover;
}
                    </div>
                </div>
            </div>

            <h4>2. pages/sign/regiter.vue 修改：</h4>
            <div class="before-after">
                <div class="before">
                    <h5>❌ 修改前：</h5>
                    <div class="code-block">
.page-box {
  background-image: url(@/static/image/signBack.png);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
                    </div>
                </div>
                <div class="after">
                    <h5>✅ 修改后：</h5>
                    <div class="code-block">
&lt;image 
  src="../../static/image/signBack.png" 
  mode="widthFix" 
  class="background-image"
&gt;&lt;/image&gt;

.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: -1;
  object-fit: cover;
}
                    </div>
                </div>
            </div>
        </div>

        <div class="test-item success">
            <h3>🎯 修复效果</h3>
            <ul>
                <li><strong>固定位置</strong>：使用 position: fixed 确保背景图片不会随页面滚动</li>
                <li><strong>层级控制</strong>：z-index: -1 确保背景在所有内容下方</li>
                <li><strong>完整覆盖</strong>：width: 100%, height: 100vh 确保背景完全覆盖屏幕</li>
                <li><strong>适配保持</strong>：object-fit: cover 保持图片比例并填满容器</li>
                <li><strong>键盘兼容</strong>：键盘弹起时背景图片不会移动</li>
            </ul>
        </div>

        <div class="test-item">
            <h3>🔧 技术要点</h3>
            <ul>
                <li><strong>mode="widthFix"</strong>：保持图片宽高比，宽度自适应</li>
                <li><strong>position: fixed</strong>：相对于视口固定定位</li>
                <li><strong>object-fit: cover</strong>：保持比例填充，可能裁剪部分内容</li>
                <li><strong>overflow: hidden</strong>：防止内容溢出</li>
                <li><strong>min-height: 100vh</strong>：确保容器至少占满视口高度</li>
            </ul>
        </div>

        <div class="test-item success">
            <h3>✨ 用户体验改善</h3>
            <ul>
                <li>✅ 点击输入框时背景图片不再上推</li>
                <li>✅ 键盘弹起时视觉效果保持稳定</li>
                <li>✅ 背景图片始终保持完整显示</li>
                <li>✅ 适配不同屏幕尺寸</li>
                <li>✅ 提升整体视觉体验</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📝 修复摘要</h2>
        <p>已成功修复登录注册页面的背景图片问题：</p>
        <ol>
            <li>将CSS背景图片改为固定的image标签</li>
            <li>使用position: fixed确保背景不随键盘移动</li>
            <li>采用widthFix模式保持图片适配</li>
            <li>同时修复了sign.vue和regiter.vue两个页面</li>
        </ol>
        <p><strong>测试建议：</strong>在移动设备上测试输入框点击时的背景图片表现。</p>
    </div>
</body>
</html>
