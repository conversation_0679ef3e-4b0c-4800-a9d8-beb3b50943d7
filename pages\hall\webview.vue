<template>
	<view class="webview-container">
		<!-- 自定义导航栏 - 移除 -->
		<!-- <view class="custom-navbar">
			<view class="navbar-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" size="20" color="#fff"></u-icon>
				</view>
				<view class="nav-center">
					<text class="nav-title">{{ pageTitle }}</text>
				</view>
				<view class="nav-right" @click="showMoreOptions">
					<u-icon name="more-dot-fill" size="20" color="#fff"></u-icon>
				</view>
			</view>
		</view> -->

		<!-- WebView容器 -->
		<web-view 
			:src="webviewUrl" 
			class="webview-content-full"
			@message="onWebviewMessage"
			@load="onWebviewLoad"
			@error="onWebviewError"
		></web-view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-overlay-full">
			<view class="loading-content">
				<u-loading-icon mode="circle" size="40" color="#ff6a00"></u-loading-icon>
				<text class="loading-text">正在加载页面...</text>
			</view>
		</view>

		<!-- 错误状态 -->
		<view v-if="error" class="error-overlay-full">
			<view class="error-content">
				<view class="error-icon">
					<u-icon name="wifi-off" size="60" color="#ff4757"></u-icon>
				</view>
				<text class="error-title">页面加载失败</text>
				<text class="error-message">{{ errorMessage }}</text>
				<view class="error-actions">
					<view class="action-btn" @click="reloadWebview">
						<u-icon name="reload" size="16" color="#fff"></u-icon>
						<text class="btn-text">重新加载</text>
					</view>
					<view class="action-btn secondary" @click="goBack">
						<u-icon name="arrow-left" size="16" color="#666"></u-icon>
						<text class="btn-text secondary">返回</text>
					</view>
				</view>
			</view>
		</view>
			
		<!-- 关闭按钮 -->
		<cover-view   class="back-button">
			<cover-image @click="goBack" src="/static/<EMAIL>" class="nav-btn-bg">

			</cover-image>
		</cover-view>  >

	</view>
</template>

<script>
export default {
	data() {
		return {
			// 页面参数
			webviewUrl: '',
			pageTitle: '视频播放',
			
			// 状态管理
			loading: true,
			error: false,
			errorMessage: '',
			isFullScreen: false,
			
			// 原始参数（用于构建URL）
			chapterId: '',
			videoTitle: '',
			videoDuration: 0,
			videoHeat: 0,

		// 播放授权信息
		videoId: '',
		playAuth: '',
		videoPoster: '',
		videoStatus: '',
		playCount: 0
		}
	},
	
	// 监听页面加载
	onLoad(options) {
		console.log('WebView页面参数:', options);

		// 解锁屏幕方向，允许自由旋转
		// #ifdef APP-PLUS
		try {
			plus.screen.unlockOrientation();
			console.log('已解锁屏幕方向，允许自由旋转');
		} catch (e) {
			console.error('解锁屏幕方向失败:', e);
		}
		// #endif

		// 获取基本信息
		if (options.chapterId) {
			this.chapterId = options.chapterId;
		}

		if (options.title) {
			this.videoTitle = decodeURIComponent(options.title);
			this.pageTitle = this.videoTitle;
		}

		// 获取播放授权信息
		if (options.videoId) {
			this.videoId = options.videoId;
		}

		if (options.playAuth) {
			// 解码PlayAuth
			this.playAuth = decodeURIComponent(options.playAuth);
		}

		// 获取视频元数据
		if (options.duration) {
			this.videoDuration = parseInt(options.duration);
		}

		if (options.heat) {
			this.videoHeat = parseInt(options.heat);
		}

		if (options.poster) {
			this.videoPoster = decodeURIComponent(options.poster);
		}

		if (options.status) {
			this.videoStatus = options.status;
		}

		if (options.playCount) {
			this.playCount = parseInt(options.playCount);
		}

		// 验证必要参数
		if (!this.videoId || !this.playAuth) {
			this.loading = false;
			this.error = true;
			this.errorMessage = '播放参数不完整，请返回重新进入';
			return;
		}

		// 构建WebView URL
		this.buildWebviewUrl();
	},
	
	// 监听页面显示
	onShow() {
		console.log('WebView页面显示');
	},

	// 监听页面隐藏
	onHide() {
		console.log('WebView页面隐藏');
	},

	// 监听页面卸载
	onUnload() {
		console.log('WebView页面卸载');
		
		// 页面卸载时锁定竖屏
		// #ifdef APP-PLUS
		try {
			plus.screen.unlockOrientation();
			plus.screen.lockOrientation('portrait-primary');
			console.log('页面卸载时锁定竖屏模式');
		} catch (e) {
			console.error('锁定竖屏失败:', e);
		}
		// #endif
	},
	
	methods: {
		// 构建WebView URL
		buildWebviewUrl() {
			// TODO: 请配置您的H5视频播放页面地址
			// 方案1: 使用相对路径（如果H5页面在同一域名下）
			// const baseUrl = '/h5/video-player.html';

			// 方案2: 使用完整URL（推荐）
			// const baseUrl = 'https://boxue.com/h5/video-player.html';

			// 方案3: 临时使用当前H5页面（仅用于测试）
			const baseUrl = 'http://114.215.188.198:8080/sygh5/pages/hall/videoPlayer';

			// 构建查询参数 - 手动构建以兼容所有环境
			const params = {
				// 基本信息
				chapterId: this.chapterId,
				title: this.videoTitle, // 已经解码过了，不需要再次编码

				// 播放授权信息（核心参数）
				videoId: this.videoId,
				playAuth: this.playAuth,

				// 视频元数据
				duration: this.videoDuration,
				heat: this.videoHeat,
				poster: this.videoPoster,
				status: this.videoStatus,
				playCount: this.playCount,

				// 标识信息
				from: 'app',
				platform: 'uniapp',
				// 添加时间戳避免缓存
				_t: Date.now()
			};

			// 手动构建查询字符串
			const queryString = Object.keys(params)
				.filter(key => params[key] !== '' && params[key] !== null && params[key] !== undefined)
				.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
				.join('&');

			this.webviewUrl = `${baseUrl}?${queryString}`;

			console.log('WebView URL:', this.webviewUrl);
			console.log('传递的播放参数:', {
				videoId: this.videoId,
				playAuth: this.playAuth ? '已获取' : '未获取',
				title: this.videoTitle
			});

			// 如果使用默认配置，显示配置提示
			if (baseUrl.includes('your-domain.com')) {
				this.showConfigTip();
			}
		},
		
		// 显示配置提示
		showConfigTip() {
			this.loading = false;
			this.error = true;
			this.errorMessage = '请配置H5视频播放页面地址\n\n配置步骤：\n1. 将视频播放功能打包成H5页面\n2. 部署到服务器获得URL\n3. 在webview.vue的buildWebviewUrl方法中配置baseUrl';
		},
		
		// WebView加载完成
		onWebviewLoad(e) {
			console.log('WebView加载完成:', e);
			this.loading = false;
			this.error = false;
		},
		
		// WebView加载错误
		onWebviewError(e) {
			console.error('WebView加载错误:', e);
			this.loading = false;
			this.error = true;
			this.errorMessage = '网页加载失败，请检查网络连接';
		},
		
		// WebView消息处理
		onWebviewMessage(e) {
			console.log('收到WebView消息:', e.detail.data);
			
			// 处理来自H5页面的消息
			const message = e.detail.data[0];
			if (message) {
				switch (message.type) {
					case 'title':
						// 更新页面标题
						this.pageTitle = message.data;
						break;
					case 'error':
						// 处理H5页面的错误
						this.error = true;
						this.errorMessage = message.data;
						break;
					case 'loaded':
						// H5页面加载完成
						this.loading = false;
						break;
					case 'close':
						// 处理视频播放器的返回请求
						console.log('收到close消息，执行返回操作');
						this.goBack();
						break;
					case 'fullscreen':
						// 处理全屏请求
						if (message.data && message.data.isFullScreen) {
							this.requestFullScreen();
						} else {
							this.exitFullScreen();
						}
						break;
					default:
						console.log('未知消息类型:', message.type);
				}
			}
		},
		
		// 请求全屏并旋转屏幕
		requestFullScreen() {
			console.log('请求全屏并旋转屏幕');
			this.isFullScreen = true;
			
			// #ifdef APP-PLUS
			try {
				// 获取当前方向
				const currentOrientation = plus.navigator.getOrientation();
				console.log('当前屏幕方向:', currentOrientation);
				
				// 解除方向锁定，允许自由旋转
				plus.screen.unlockOrientation();
				
				// 设置全屏
				const currentWebview = plus.webview.currentWebview();
				currentWebview.setStyle({
					'popGesture': 'none',
					'bounce': 'none',
					'scrollIndicator': 'none',
					'scalable': false
				});
				
				// 隐藏系统状态栏
				plus.navigator.setFullscreen(true);
				
				// 向WebView发送消息，通知已进入全屏
				this.sendMessageToWebView({
					type: 'fullscreenChange',
					data: { isFullScreen: true }
				});
				
				console.log('设置全屏成功');
			} catch (e) {
				console.error('设置全屏失败:', e);
			}
			// #endif
			
			// #ifndef APP-PLUS
			// 在非APP环境中，让WebView内部处理全屏
			this.sendMessageToWebView({
				type: 'requestFullscreen',
				data: {}
			});
			// #endif
		},
		
		// 退出全屏
		exitFullScreen() {
			console.log('退出全屏');
			this.isFullScreen = false;
			
			// #ifdef APP-PLUS
			try {
				// 退出全屏
				plus.navigator.setFullscreen(false);
				
				// 解除方向锁定，允许自由旋转
				plus.screen.unlockOrientation();
				
				// 向WebView发送消息，通知已退出全屏
				this.sendMessageToWebView({
					type: 'fullscreenChange',
					data: { isFullScreen: false }
				});
				
				console.log('退出全屏成功');
			} catch (e) {
				console.error('退出全屏失败:', e);
			}
			// #endif
			
			// #ifndef APP-PLUS
			// 在非APP环境中，让WebView内部处理退出全屏
			this.sendMessageToWebView({
				type: 'exitFullscreen',
				data: {}
			});
			// #endif
		},
		
		// 向WebView发送消息
		sendMessageToWebView(message) {
			try {
				// #ifdef APP-PLUS
				// 获取当前页面
				const currentWebview = this.$scope.$getAppWebview();
				// 获取web-view组件的webview对象
				setTimeout(() => {
					const webviewObj = currentWebview.children()[0];
					if (webviewObj) {
						webviewObj.evalJS(`
							try {
								window.dispatchEvent(new CustomEvent('app-message', { 
									detail: ${JSON.stringify(message)} 
								}));
							} catch (e) {
								console.error('发送消息到WebView失败:', e);
							}
						`);
						console.log('消息已发送到WebView');
					} else {
						console.error('未找到WebView对象');
					}
				}, 500);
				// #endif
				
				// #ifndef APP-PLUS
				console.log('非APP环境，无法直接发送消息到WebView');
				// #endif
			} catch (e) {
				console.error('发送消息到WebView失败:', e);
			}
		},
		
		// 重新加载WebView
		reloadWebview() {
			this.loading = true;
			this.error = false;
			this.errorMessage = '';
			
			// 重新构建URL（添加时间戳避免缓存）
			const url = new URL(this.webviewUrl);
			url.searchParams.set('_t', Date.now().toString());
			this.webviewUrl = url.toString();
		},
		
		// 返回上一页
		goBack() {
			console.log('返回按钮点击, 全屏状态:', this.isFullScreen);
			
			// // 如果处于全屏状态，先退出全屏
			// if (this.isFullScreen) {
			// 	this.exitFullScreen();
			// 	return; // 先退出全屏，不直接返回
			// }
			
			// 锁定竖屏模式
			// #ifdef APP-PLUS
			try {
				plus.screen.unlockOrientation();
				plus.screen.lockOrientation('portrait-primary');
				console.log('返回前锁定竖屏模式');
			} catch (e) {
				console.error('锁定竖屏失败:', e);
			}
			// #endif
			
			// 执行返回操作
			console.log('执行返回操作');
			
			// 尝试多种方式关闭页面
			// #ifdef APP-PLUS
			// try {
			// 	// 方法1: 使用plus关闭当前页面
			// 	const currentWebview = plus.webview.currentWebview();
			// 	currentWebview.close('auto');
			// 	console.log('使用plus.webview.close关闭当前页面');
			// 	return;
			// } catch (e) {
			// 	console.error('使用plus关闭页面失败:', e);
			// }
			// #endif
			
			// 方法2: 使用uni.navigateBack
			try {
				uni.navigateBack({
					delta: 1,
					success: () => {
						console.log('使用uni.navigateBack成功返回上一页');
					},
					fail: (err) => {
						console.error('使用uni.navigateBack失败:', err);
						// 方法3: 如果无法返回，尝试跳转到首页
						uni.switchTab({
							url: '/pages/hall/hall',
							success: () => {
								console.log('跳转到首页成功');
							},
							fail: (err) => {
								console.error('跳转到首页失败:', err);
							}
						});
					}
				});
			} catch (e) {
				console.error('执行返回操作失败:', e);
				// 方法4: 最后尝试跳转到首页
				uni.switchTab({
					url: '/pages/hall/hall'
				});
			}
		},
		
		// 显示更多选项
		showMoreOptions() {
			uni.showActionSheet({
				itemList: ['在浏览器中打开', '复制链接', '刷新页面'],
				success: (res) => {
					switch (res.tapIndex) {
						case 0:
							// 在浏览器中打开
							this.openInBrowser();
							break;
						case 1:
							// 复制链接
							this.copyLink();
							break;
						case 2:
							// 刷新页面
							this.reloadWebview();
							break;
					}
				}
			});
		},
		
		// 在浏览器中打开
		openInBrowser() {
			// #ifdef APP-PLUS
			plus.runtime.openURL(this.webviewUrl);
			// #endif
			
			// #ifndef APP-PLUS
			uni.showToast({
				title: '当前环境不支持',
				icon: 'none'
			});
			// #endif
		},
		
		// 复制链接
		copyLink() {
			uni.setClipboardData({
				data: this.webviewUrl,
				success: () => {
					uni.showToast({
						title: '链接已复制',
						icon: 'success'
					});
				}
			});
		},

		// 关闭WebView页面
		closeWebview() {
			console.log('关闭按钮点击');
			
			// 锁定竖屏模式
			// #ifdef APP-PLUS
			try {
				plus.screen.unlockOrientation();
				plus.screen.lockOrientation('portrait-primary');
				console.log('关闭前锁定竖屏模式');
			} catch (e) {
				console.error('锁定竖屏失败:', e);
			}
			// #endif
			
			// 直接跳转到首页
			console.log('直接跳转到首页');
			uni.switchTab({
				url: '/pages/hall/hall',
				success: () => {
					console.log('跳转到首页成功');
				},
				fail: (err) => {
					console.error('跳转到首页失败:', err);
					// 如果跳转失败，尝试返回上一页
					uni.navigateBack({
						delta: 1
					});
				}
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.webview-container {
	height: 100vh;
	background-color: #f8f9fa;
}

/* 自定义导航栏 - 移除 */
/* .custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 100%);
	backdrop-filter: blur(10rpx);
	
	.navbar-content {
		display: flex;
		align-items: center;
		height: 88rpx;
		padding: 0 30rpx;
		padding-top: var(--status-bar-height);
		
		.nav-left, .nav-right {
			width: 80rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 30rpx;
			background: rgba(255, 255, 255, 0.15);
			backdrop-filter: blur(10rpx);
			transition: all 0.3s ease;
			
			&:active {
				transform: scale(0.95);
				background: rgba(255, 255, 255, 0.25);
			}
		}
		
		.nav-center {
			flex: 1;
			text-align: center;
			margin: 0 20rpx;
			
			.nav-title {
				color: #fff;
				font-size: 32rpx;
				font-weight: 600;
				text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
			}
		}
	}
*/

/* WebView内容区域 - 全屏 */
.webview-content-full {
	width: 100%;
	height: 100vh;
}

/* 加载状态 - 全屏 */
.loading-overlay-full {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	
	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.loading-text {
			color: #666;
			font-size: 28rpx;
			margin-top: 20rpx;
		}
	}
}

/* 错误状态 - 全屏 */
.error-overlay-full {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	
	.error-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		padding: 0 60rpx;
		
		.error-icon {
			margin-bottom: 30rpx;
		}
		
		.error-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 15rpx;
		}
		
		.error-message {
			font-size: 28rpx;
			color: #666;
			line-height: 1.5;
			margin-bottom: 40rpx;
		}
		
		.error-actions {
			display: flex;
			gap: 20rpx;
			
			.action-btn {
				display: flex;
				align-items: center;
				background: #ff6a00;
				padding: 15rpx 30rpx;
				border-radius: 25rpx;
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.95);
				}
				
				&.secondary {
					background: #f0f0f0;
				}
				
				.btn-text {
					color: #fff;
					font-size: 28rpx;
					margin-left: 8rpx;
					
					&.secondary {
						color: #666;
					}
				}
			}
		}
	}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	/* .custom-navbar .navbar-content {
		padding: 0 20rpx;
		
		.nav-left, .nav-right {
			width: 70rpx;
			height: 50rpx;
		}
	} */
}

/* 返回按钮 */
.back-button {
	position: fixed;
	top: 100rpx; /* 调整顶部距离，避开状态栏 */
	left: 30rpx;
	z-index: 999999999; /* 确保在最上层 */
	width: 80rpx;
	height: 80rpx;
}

.nav-btn-bg {
	/* width: 80rpx;
	height: 80rpx;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(0, 0, 0, 0.7);
	} */
}

/* 关闭按钮 */
.close-button {
	position: fixed;
	top: 40rpx; /* 调整顶部距离，避开状态栏 */
	right: 30rpx;
	z-index: 10000; /* 确保在最上层 */
	width: 80rpx;
	height: 80rpx;
}

.close-btn-bg {
	width: 80rpx;
	height: 80rpx;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.9);
		background: rgba(0, 0, 0, 0.7);
	}
}
</style>
