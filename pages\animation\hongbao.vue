<!-- <template>
	<view class="container">
		<view class="navbar">
			<u-navbar
				title="红包动画"
				:autoBack="true"
				:placeholder="true"
			>
			</u-navbar>
		</view>
		
		<view id="animation-container" ref="animationContainer" 
			style="width: 100%; height: calc(100vh - 88rpx); position: relative; background-color: #000;" 
			@click="handleContainerClick">
		</view>
		
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import { Player, spec } from '@galacean/effects';

export default {
	data() {
		return {
			loading: true,
			player: null,
			explosionPlayer: null, // 爆炸效果播放器
			hongbaoLoaded: false, // 红包动画是否已加载
			hongbaoComposition: null, // 红包动画合成
		}
	},
	onReady() {
		// 在 onReady 阶段初始化动画，确保 DOM 已渲染
		setTimeout(() => {
			this.initAnimation();
		}, 300); // 给页面渲染一点时间
	},
	methods: {
		// 处理容器点击事件 - 作为备用方案
		handleContainerClick(event) {
			console.log('容器被点击了，但通常点击由Galacean Effects内部处理');
			// 如果内部交互未触发，这里作为备用
			if (!this.hongbaoLoaded) return;
			this.playExplosionEffect(event.clientX, event.clientY);
		},
		
		// 播放爆炸效果
		playExplosionEffect(x, y) {
			// 如果已经有爆炸动画实例，先销毁
			if (this.explosionPlayer) {
				try {
					this.explosionPlayer.dispose();
				} catch (e) {
					console.error('销毁旧播放器时出错:', e);
				}
				this.explosionPlayer = null;
			}
			
			// 使用 uni 选择器获取元素
			const query = uni.createSelectorQuery().in(this);
			query.select('#animation-container').boundingClientRect(data => {
				if (!data) {
					console.error('未找到容器元素');
					return;
				}
				
				const container = this.$refs.animationContainer;
				if (!container) {
					console.error('未找到容器元素引用');
					return;
				}
				
				// 计算爆炸位置（相对于容器）
				const posX = x - data.left;
				const posY = y - data.top;
				
				try {
					// #ifdef H5
					// 创建临时DIV用于爆炸效果
					const explosionContainer = document.createElement('div');
					explosionContainer.id = 'explosion-container-' + Date.now(); // 唯一ID避免冲突
					explosionContainer.style.position = 'absolute';
					explosionContainer.style.left = `${posX - 100}px`;  // 中心点偏移
					explosionContainer.style.top = `${posY - 100}px`;  // 中心点偏移
					explosionContainer.style.width = '200px';
					explosionContainer.style.height = '200px';
					explosionContainer.style.zIndex = '100';
					explosionContainer.style.pointerEvents = 'none'; // 避免阻止点击事件
					
					// 将爆炸容器添加到主容器中
					let parentContainer = null;
					if (container.$el) {
						parentContainer = container.$el;
						parentContainer.appendChild(explosionContainer);
					} else if (container) {
						parentContainer = container;
						parentContainer.appendChild(explosionContainer);
					} else {
						console.error('找不到父容器来添加爆炸效果');
						return;
					}
					
					// 确保容器已添加到DOM中
					setTimeout(() => {
						try {
							// 初始化爆炸效果播放器
							const playerInstance = new Player({
								container: explosionContainer,
								width: 200,
								height: 200,
								transparent: true,
								renderOptions: {
									preserveDrawingBuffer: true,
									premultipliedAlpha: true, // 尝试修复alpha混合问题
									antialias: true // 提高渲染质量
								},
								onError: (err) => {
									console.error('爆炸动画加载错误:', err);
								},
							});
							
							// 保存播放器引用
							this.explosionPlayer = playerInstance;
							
							// 加载爆炸动画
							playerInstance.loadScene('../../static/hongbao/baozha.json')
								.then(() => {
									console.log('爆炸动画加载成功');
									
									// 确保播放器仍然有效
									if (playerInstance) {
										try {
											// 播放爆炸动画
											playerInstance.play();
											
											// 爆炸效果播放完成后移除容器
											setTimeout(() => {
												try {
													if (explosionContainer && explosionContainer.parentNode) {
														explosionContainer.parentNode.removeChild(explosionContainer);
													}
													
													if (playerInstance) {
														try {
															playerInstance.dispose();
														} catch (e) {
															console.error('销毁播放器时出错:', e);
														}
														
														// 如果这是当前的爆炸播放器，则清空引用
														if (this.explosionPlayer === playerInstance) {
															this.explosionPlayer = null;
														}
													}
												} catch (e) {
													console.error('清理资源时出错:', e);
												}
											}, 1500); // 延长爆炸效果时间确保完整播放
										} catch (playError) {
											console.error('播放爆炸动画时出错:', playError);
										}
									} else {
										console.error('播放器实例已不存在');
									}
								})
								.catch(err => {
									console.error('爆炸动画加载失败:', err);
									
									// 移除没用的容器
									if (explosionContainer && explosionContainer.parentNode) {
										explosionContainer.parentNode.removeChild(explosionContainer);
									}
									
									// 清理播放器
									if (playerInstance) {
										try {
											playerInstance.dispose();
										} catch (e) {
											console.error('销毁播放器时出错:', e);
										}
										
										if (this.explosionPlayer === playerInstance) {
											this.explosionPlayer = null;
										}
									}
								});
						} catch (err) {
							console.error('初始化爆炸播放器失败:', err);
							
							// 清理DOM
							if (explosionContainer && explosionContainer.parentNode) {
								explosionContainer.parentNode.removeChild(explosionContainer);
							}
						}
					}, 50); // 短暂延迟确保DOM已更新
					// #endif
					
					// #ifndef H5
					// 在非H5平台，可以尝试其他反馈形式
					uni.showToast({
						title: '爆炸效果',
						icon: 'none'
					});
					// #endif
				} catch (err) {
					console.error('创建爆炸效果容器时出错:', err);
				}
			}).exec();
		},
		
		initAnimation() {
			// 使用 uni 选择器获取元素
			const query = uni.createSelectorQuery().in(this);
			query.select('#animation-container').boundingClientRect(data => {
				if (!data) {
					console.error('未找到容器元素');
					return;
				}
				
				// 确保容器有足够尺寸
				if (data.width === 0 || data.height === 0) {
					console.error('容器尺寸为0');
					return;
				}
				
				// 获取元素引用
				const container = this.$refs.animationContainer;
				if (!container) {
					console.error('未找到容器元素引用');
					return;
				}
				
				// 初始化播放器，使用 $el 获取原生DOM元素（在H5平台）
				try {
					// #ifdef H5
					this.player = new Player({
						container: container.$el || container,
						width: data.width,
						height: data.height,
						transparent: true,
						interactive: true, // 启用内置交互功能
						renderOptions: {
							preserveDrawingBuffer: true,
						},
						onError: (err, ...args) => {
							console.error('动画加载错误:', err);
							// 降级处理
							if (container.$el) {
								container.$el.style.backgroundImage = 'url("../../static/image/signBack1.png")';
							} else if (container.style) {
								container.style.backgroundImage = 'url("../../static/image/signBack1.png")';
							}
						},
					});
					
					// 注册全局点击事件
					this.player.on('click', clickedItem => {
						console.log('元素被点击:', clickedItem);
						
						// 这里可以根据元素名称判断处理不同的点击事件
						if (clickedItem && clickedItem.compositionName) {
							console.log(`点击了合成: ${clickedItem.compositionName}`);
							
							// 获取点击坐标
							if (clickedItem.clickInfo) {
								const x = clickedItem.clickInfo.canvasX;
								const y = clickedItem.clickInfo.canvasY;
								// 在点击位置播放爆炸效果
								this.playExplosionEffect(x, y);
							} else {
								// 如果没有获取到点击坐标，使用容器中心位置
								this.playExplosionEffect(data.width / 2, data.height / 2);
							}
						}
					});
					
					// 监听动画元素生命周期事件
					this.player.on('message', item => {
						console.log('动画元素事件:', item);
						if (item.phrase === spec.MESSAGE_ITEM_PHRASE_BEGIN) {
							console.log(`元素 ${item.name} 已创建`);
						} else if (item.phrase === spec.MESSAGE_ITEM_PHRASE_END) {
							console.log(`元素 ${item.name} 已销毁`);
						}
					});
					
					// 加载动画
					this.player.loadScene('../../static/hongbao/hongbao.json')
						.then(composition => {
							console.log('动画加载成功');
							this.hongbaoComposition = composition;
							
							// 尝试获取具体元素绑定事件
							const items = composition.getAllItems();
							console.log('可用动画元素:', items.map(item => item.name));
							
							// 为每个元素添加点击监听
							items.forEach(item => {
								if (item && item.name) {
									item.on('click', e => {
										console.log(`元素 ${e.name} 被点击`);
										
										// 在点击位置播放爆炸效果
										if (e.clickInfo) {
											this.playExplosionEffect(e.clickInfo.canvasX, e.clickInfo.canvasY);
										}
									});
									console.log(`已为元素 ${item.name} 添加点击监听`);
								}
							});
							
							// 显式开始播放
							this.player.play();
							this.hongbaoLoaded = true;
							
							// 为容器添加点击事件 - 备用方案
							if (container.$el) {
								container.$el.addEventListener('click', e => {
									if (!this.hongbaoLoaded) return;
									// 获取相对于容器的点击位置
									const rect = container.$el.getBoundingClientRect();
									const x = e.clientX - rect.left;
									const y = e.clientY - rect.top;
									// 在点击位置播放爆炸效果
									this.playExplosionEffect(x, y);
								});
							}
						})
						.catch(err => {
							console.error('动画加载失败:', err);
							// 尝试使用绝对路径
							this.player.loadScene(uni.getStorageSync('baseUrl') + '/static/hongbao/hongbao.json')
								.then(composition => {
									console.log('使用绝对路径加载动画成功');
									this.hongbaoComposition = composition;
									this.player.play();
									this.hongbaoLoaded = true;
								})
								.catch(e => {
									console.error('所有尝试均失败:', e);
								});
						});
					// #endif
					
					// #ifndef H5
					// 在非H5平台，使用图片降级
					if (container && container.style) {
						container.style.backgroundImage = 'url("../../static/image/signBack1.png")';
					}
					// #endif
				} catch (err) {
					console.error('初始化播放器失败:', err);
				}
			}).exec();
		}
	},
	onUnload() {
		// 清理资源
		if (this.player) {
			this.player.dispose();
			this.player = null;
		}
		if (this.explosionPlayer) {
			this.explosionPlayer.dispose();
			this.explosionPlayer = null;
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100vh;
}

.navbar {
	background-color: #000;
}
</style> -->