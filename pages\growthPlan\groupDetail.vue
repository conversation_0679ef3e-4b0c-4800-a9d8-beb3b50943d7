<template>
	<view>

		<z-paging :show-scrollbar="false" refresher-background='#fffff00' ref="paging" style="height: 100vh;"
			refresher-only @onRefresh="onRefresh" :use-page-scroll='true'>
			<view slot="top">
				<u-navbar title="" height="0" bgColor="#ffffff00" :placeholder='false' :auto-back="true">
					<view style="padding-top: 100upx;" slot="left">
						<u-icon name="arrow-left" size="24" color="#000"></u-icon>
					
					</view>
					<view slot="right" style="padding-top: 100upx;">
						<view  @click="toggleCollect">
							<image 
								v-if="detail.isCollected" 
								src="../../static/image/shoucang.png" 
								mode="" 
								style="width: 50upx;height: 50upx;"
							></image>
							<image 
								v-else 
								src="../../static/image/noshoucang.png" 
								mode="" 
								style="width: 50upx;height: 50upx;" 
								class="zwyHover1"
							></image>
						</view>
					</view>
				</u-navbar>
				<!-- 头部nav -->
			</view>
			
			<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
			<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->

			<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

			<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
			<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
			<template #refresher="{refresherStatus}">
				<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
				<custom-refresher :status="refresherStatus" color='#000' />
			</template>
			<!-- 自定义没有更多数据view -->
			<template #loadingMoreNoMore>
				<!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
				<custom-nomore />
			</template>

			<view v-if="loading">
				<x-skeleton type="banner" :loading="true">
					<view></view>
				</x-skeleton>
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 1,
						headHeight: '50rpx',
						textRows: 2,
						gridRows:1,
						textShow:false
					}">
					<view></view>
				</x-skeleton>
				<x-skeleton type="list" :configs="{gridRows:8,headWidth:'80rpx',headHeight:'80rpx',textRows: 2}"
					:loading="true">
					<view></view>
				</x-skeleton>
			</view>

			<view v-else style="min-height: 100vh;height: auto;">
				<view class="growpTopview" :style="{'background-image': `url(${detail.courseImg})`}">
					<view class="dis-ali jc_bet" style="width: 100%;">
						<view class="com-fontsize-32">
							<view>{{detail.courseName}}</view>
							<view class="com-fontsize-24 mt10">所属计划：{{detail.classifyName}}</view>
							<view class="com-fontsize-24 mt20">{{detail.participantCount}}人参与</view>
						</view>
						<view>
							<image :src="detail.courseCover || '../../static/xs.jpg'" mode="aspectFill"
								style="width: 220rpx;height: 220rpx;border-radius: 50%;border: 5upx solid #fff;"></image>
						</view>
					</view>
				</view>

				<view style="position: absolute;top: 490upx;" class="dis-ali jc_cen flex-column">
					<view
						style="background-color: #fff;padding: 20upx 20upx;width: 680upx;border-top-left-radius: 20upx;border-top-right-radius: 20upx;">
						<view
							style="padding: 10upx;font-size: 28upx;line-height: 36upx;color: #333;text-indent: 2em;line-height: 1.8;" v-html='detail.courseBriefIntroduction'>
							
						</view>
					</view>
					<view class="groupList" style="position: relative;">
						<view class="course-progress">
							<view class="com-fontsize-30">已完成 <text class="progress-text">{{detail.completedPeriodCount}}/{{detail.sumPeriodCount}}</text> 节</view>
						</view>
						<view class="tabs-wrapper">
							<u-tabs 
								:list="list1" 
								@click="changeTab" 
								:current="currentIndex"
								:is-scroll="true"
								:scroll-into="currentIndex"
								lineHeight="4" 
								lineColor="#00C1CC" 
								itemStyle="padding-left: 15rpx; padding-right: 15rpx; height: 80rpx;"
								:activeStyle="{
									color: '#00C1CC',
									fontWeight: 'bold',
									transform: 'scale(1.05)'
								}"
								:inactiveStyle="{
									color: '#666',
									fontSize: '28rpx'
								}"
							></u-tabs>
						</view>
						<!-- <view class="border"></view> -->
						<view class="dis-ali jc_cen mt20">
							<view class="dis-ali flex-column view_list">
								<view class="dis-ali jc_cen list_title">
									{{detail.periodList[currentIndex].title}}
								</view>
								<view class="title2 com-fontsize-30 mt20">
									<!-- {{list1[currentIndex]?name || ''}} -->
								</view>

								<view v-if="detail.periodList && detail.periodList[currentIndex]">
									<view 
										class="dis-ali jc_bet list"
										@tap="goDetail(detail.periodList[currentIndex])">
										<view class="dis-ali left">
											<view>
												<image 
													src="../../static/image/book.png"
													style="width: 48upx;height: 48upx;margin-right: 20upx;"
													mode="aspectFill"
												></image>
											</view>
											<view>
												<view class="text-line-overflow"
													style="font-size: 30rpx;color: #3D3D3D;line-height: 1.8;width: 380upx;">
													{{detail.periodList[currentIndex].title}}
												</view>
											</view>
										</view>
										<view>
											<view v-if="!detail.periodList[currentIndex].isFree&&detail.periodList[currentIndex].freeWatchDuration" class='right_1'>
												试看
											</view>
											<view v-else-if="!detail.periodList[currentIndex].isFree&&!detail.periodList[currentIndex].freeWatchDuration">
												<u-icon name="lock" size="30"></u-icon>
											</view>
											<view v-else :class="['dis-ali', detail.periodList[currentIndex].isComplete  ? 'right_2' : 'right']">
												{{ detail.periodList[currentIndex].isComplete  ? '已完成' : '去完成' }}
											</view>
										</view>
										
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 学习列表 -->
			<u-toast ref="uToast"></u-toast>
			<!-- toast -->
			<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
				:loading="false"></u-loading-page>
			<!-- 加载页 -->
			<!-- <my-bottom></my-bottom> -->
			<!-- </bottom> -->

		</z-paging>

	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				loading: true,
				pageScrollTop: 0,
				bgColor: '#ffffff00',
				navtitle: '',
				detail: {
					courseName: '',
					courseBriefIntroduction: '',
					classifyName: '',
					participantCount: 0,
					courseCover: '',
					completedPeriodCount: 0,
					sumPeriodCount: 0,
					periodList: []
				},
				list1: [],
				currentIndex: 0,
				videoContext: null,
				isPlaying: false,
				currentTime: 0,
				selectedTitle: ''
			}
		},
		methods: {
			async getGrowthPlanDetail(id) {
				try {
					const res = await that.http.ajax({
						url: that.http.api.goFinish,
						method: 'POST',
						data: {
							courseId: id
						}
					})
					
					if(res.code === 0) {
						this.detail = res.data
						this.list1 = this.detail.periodList.map(item => ({
							name: item.chapterTitle || `第${item.sort}章`
						}))
						
						if (this.selectedTitle) {
							const index = this.detail.periodList.findIndex(item => 
								item.chapterTitle === this.selectedTitle
							)
							if (index !== -1) {
								this.currentIndex = index
							}
						}
						
						this.loading = false
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000,
							success() {
								setTimeout(() => {
									uni.navigateBack()
								}, 2000)
							}
						})
					}
				} catch(err) {
					console.error('获取课程详情失败:', err)
					this.loading = false
				}
			},
			changeTab(e) {
				this.currentIndex = e.index
			},
			goDetail(item) {
				if (item.isFree) {
					this.playVideo(item)
				} else {
					if (item.isComplete) {
						this.playVideo(item)
					} else {
						const freeTime = item.freeWatchDuration || 0
						if(freeTime==0){
							return;
						}
						uni.showModal({
							title: '提示',
							content: `此为付费课程，可试看${freeTime}秒`,
							success: (res) => {
								if (res.confirm) {
									this.playVideo(item, freeTime)
								}
							}
						})
					}
				}
			},
			playVideo(item, freeTime = null) {
				uni.navigateTo({
					url: `/pages/video/video?videoUrl=${encodeURIComponent(item.videoUrl)}&freeTime=${freeTime}&from=groupDetail&periodId=${item.id}&title=${encodeURIComponent(item.title)}&isComplete=${item.isComplete}`
				})
			},
			onRefresh() {
				if(this.courseId) {
					this.getGrowthPlanDetail(this.courseId)
				}
				setTimeout(() => {
					this.$refs.paging.complete();
				}, 1500)
			},
			async toggleCollect() {
				try {
					if (this.detail.isCollected) {
						// 取消收藏
						const res = await this.http.ajax({
							url: this.http.api.collectconsole,
							method: 'POST',
							data: {
								ids: [this.detail.courseId],
								type:0
							}
						})
						
						if (res.code === 0) {
							// uni.showToast({
							// 	title: '取消收藏成功',
							// 	icon: 'success'
							// })
							this.detail.isCollected = false
						}
					} else {
						// 添加收藏
						const res = await this.http.ajax({
							url: this.http.api.collectcreate,
							method: 'POST',
							data: {
								dataId: this.detail.courseId,
								type:0
							}
						})
						
						if (res.code === 0) {
							uni.showToast({
								title: '收藏成功',
								icon: 'success'
							})
							this.detail.isCollected = true
						}
					}
				} catch (err) {
					console.error('收藏操作失败:', err)
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					})
				}
			},
		},
		onLoad(option) {
			that = this
			if(option.id) {
				this.courseId = option.id
				this.selectedTitle = decodeURIComponent(option.title || '')
			}
		},
		onShow() {
			this.getGrowthPlanDetail(this.courseId)
			// // #ifdef APP-PLUS
			// 	plus.screen.unlockOrientation();//解除锁定屏幕方向
			// 	plus.screen.lockOrientation('portrait-primary');
			// 	// #endif	

		}
	}
</script>

<style>
	page,
	body {
		background-color: #fefefe;
	}
</style>
<style scoped lang="scss">
	

	video {
		vertical-align: middle;
	}

	image {
		vertical-align: middle !important;
	}

	.growpTopview {
		font-weight: 700;
		background-image: url(https://cdn.pixabay.com/photo/2014/08/05/10/30/iphone-410324_1280.jpg);
		width: 750upx;
		height: 613upx;
		background-size: cover;
		display: flex;
		align-items: center;
		/* justify-content: center; */
		padding: 190upx 30upx;
		flex-direction: column;
	}
	
	.tabs-wrapper {
		position: relative;
		margin: 20rpx 0;
		padding: 5rpx 0;
		overflow: hidden;

		/* &::before {
			content: '';
			position: absolute;
			left: 0;
			top: 0;
			width: 30rpx;
			height: 100%;
			background: linear-gradient(to right, #fefefe, rgba(254, 254, 254, 0));
			z-index: 2;
			pointer-events: none;
		}

		&::after {
			content: '';
			position: absolute;
			right: 0;
			top: 0;
			width: 30rpx;
			height: 100%;
			background: linear-gradient(to left, #fefefe, rgba(254, 254, 254, 0));
			z-index: 2;
			pointer-events: none;
		} */
	}
	
	.course-progress {
		display: flex;
		align-items: center;
		padding: 10rpx 0;
		
		.progress-text {
			color: #00C1CC;
			font-weight: bold;
		}
	}
	
	.right_2{
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		color: #FFF;
		background-color: #00C1CC;
		width: 123rpx;
		height: 53rpx;
		border-radius: 60rpx 60rpx 60rpx 60rpx;
		border: 2rpx solid #00C1CC;
		box-shadow: 0 2rpx 8rpx rgba(0, 193, 204, 0.2);
	}
	.groupList {
		width: 100vw;
		border-radius: 20upx;
		background-color: #fefefe;
		height: 100%;
		padding: 25upx 20upx 0;

		.title {
			font-weight: 700;
			color: #000;
			margin-bottom: 20upx;
		}

		.border {
			width: 100%;
			height: 2upx;
			background-color: rgba(0, 0, 0, 0.06);
			position: absolute;
			left: 0;
			top: 145upx;
		}

		.view_list {
			padding: 0upx 0upx 20upx;
			background-color: #fff;
			width: 625upx;
			height: auto;
			box-shadow: 0rpx 15rpx 50rpx 0rpx rgba(0,0,0,0.08);
			border-radius: 10rpx 10rpx 10rpx 10rpx;
		}

		.list_title {
			background-image: url(../../static/image/<EMAIL>);
			background-size: 100% 100%;
			width: 373upx;
			height: 62upx;
			font-size: 28rpx;
			color: #E3AF4A;
			margin-top: 15rpx;
		}

		.list {
			min-height: 75upx;
			border-bottom: 1upx solid rgba(0, 0, 0, 0.06);
			padding: 30upx 30upx 30upx 30upx;
		}

		.list:last-child {
			border: 0;
		}

		.tab {
			vertical-align: text-top;
			width: 103rpx;
			height: 25rpx;
			background: linear-gradient(90deg, #FB6D5F 0%, #FB968B 100%);
			border-radius: 4rpx 4rpx 4rpx 4rpx;
			font-size: 20rpx;
			color: #FFFFFF;
		}

		.right {
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			color: #00C1CC;
			width: 123rpx;
			height: 53rpx;
			border-radius: 60rpx 60rpx 60rpx 60rpx;
			border: 2rpx solid #00C1CC;
			box-shadow: 0 2rpx 8rpx rgba(0, 193, 204, 0.1);
		}
	}
	.right_1{
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		color: #adadad;
		width: 123rpx;
		height: 53rpx;
		border-radius: 60rpx 60rpx 60rpx 60rpx;
		border: 2rpx solid #adadad;
	}
</style>