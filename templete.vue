<template>
	<view>
		<z-paging  :show-scrollbar="false" refresher-background='#fffff00'  ref="paging" refresher-only @onRefresh="onRefresh" :use-page-scroll='true'>
			<view slot="top">
				<u-navbar :title="navtitle" height="0" :bgColor="bgColor" :placeholder='true' left-icon="">
				</u-navbar>
				<my-nav :title='navtitle' :pageScrollTop='pageScrollTop'></my-nav>
				<!-- 头部nav -->
			</view>
			<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
			<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->

			<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

			<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
			<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
			<template #refresher="{refresherStatus}">
				<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
				<custom-refresher :status="refresherStatus" />
			</template>
			<!-- 自定义没有更多数据view -->
			<template #loadingMoreNoMore>
				<!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
				<custom-nomore />
			</template>
			<view v-if="loading">
				<x-skeleton type="banner" :loading="true">
					<view></view>
				</x-skeleton>
				<x-skeleton type="menu" :loading="true">
					<view></view>
				</x-skeleton>
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 4,
						headHeight: '200rpx',
						textRows: 1,
						gridRows:1
					}">
					<view></view>
				</x-skeleton>
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 1,
						headHeight: '200rpx',
						textRows: 1,
						gridRows:3,
						textShow:false
					}">
					<view></view>
				</x-skeleton>
				<x-skeleton type="list" :loading="true">
					<view></view>
				</x-skeleton>
			</view>
					<!-- 学习列表 -->
					<u-toast ref="uToast"></u-toast>
					<!-- toast -->
					<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
						:loading="false"></u-loading-page>
					<!-- 加载页 -->
					<my-bottom></my-bottom>
					<!-- </bottom> -->
			
		</z-paging>
	
		<view>
			<drag-button :isDock="true" :existTabBar="true" @btnClick="btnClick" @btnTouchstart="btnTouchstart"
				@btnTouchend="btnTouchend" />
		</view>
		<!-- 机器人 -->
	</view>
</template>

<script>
	var that
	import dragButton from "@/components/drag-button/drag-button.vue";
	export default {
		components: {
			dragButton
		},
		data() {
			return {
				loading: true,
				status: 'loadmore',
				page: 1,
				displayedText: '',
				typingInterval: null,
				typingIndex: 0,
				pageScrollTop: 0, // 页面滚动距离
				bgColor: 'rgba(255,255,255,0.01)',
				navtitle: '',
			}
		},
		onLoad() {
			that = this;
			setTimeout(() => this.loading = false, 1000)
		},
		onPageScroll(e) {
			this.pageScrollTop = Math.floor(e.scrollTop);
			// this.$refs.paging.updatePageScrollTop(e.scrollTop);
		},
		onReachBottom(e) {
			// this.$refs.paging.pageReachBottom()
		},
		// 	},
		onShow() {
			that.http.ajax({
				url: '/registerMember',
				method: 'POST',
				success(res) {
					if (res.code == 200) {
						that.http.ajax({
							url: that.http.api.info,
							method: 'POST',
							success(res) {
							
							}
						})
					} else {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})
					}
				}
			})
		},
		beforeDestroy() {
			
		},
		onHide() {
			
		},
		methods: {
			onRefresh() {
				// 告知z-paging下拉刷新结束，这样才可以开始下一次的下拉刷新
				setTimeout(() => {
					// 1.5秒之后停止刷新动画
					this.$refs.paging.complete();
				}, 1500)
			},
			goUrl(type) {
				if (type == 1) {
					
				}
				
			}
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #f3f3f3;
		background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 60%;
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>

</style>