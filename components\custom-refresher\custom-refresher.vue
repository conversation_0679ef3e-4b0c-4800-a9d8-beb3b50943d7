<!-- z-paging自定义的下拉刷新view -->
<template>
	<view class="refresher-container">
		<!-- 这里的图片请换成自己项目的图片 -->
		<image class="refresher-image" mode="aspectFit" src="/static/syg/index/04.gif"></image>
		<text class="refresher-text" :style="{'color':color}">{{statusText}}</text>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		},
		computed: {
			statusText() {
				// 这里可以做i18n国际化相关操作，可以通过uni.getLocale()获取当前语言(具体操作见i18n-demo.vue);
				// 获取到当前语言之后，就可以自定义不同语言下的展示内容了
				const statusTextMap = {
					'default': '用点力继续下拉！',
					'release-to-refresh': '松手刷新~~',
					'refreshing': '正在努力刷新中...',
					'complete': '刷新成功啦~'
				};
				return statusTextMap[this.status];
			}
		},
		props: {
			status: {
				type: String,
				default: function() {
					return 0;
				},
			},
			color:{
				type:String,
				default:'#fff'
			}
		}
	}
</script>

<style scoped>
	.refresher-container {
		/* background: linear-gradient( 180deg, #00C1CC 0%,#3bcdd6 35%); */
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		height: 150rpx;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.refresher-image {
		margin-top: 10rpx;
		height: 45px;
		width: 45px;
	}

	.refresher-text {
		margin-top: 10rpx;
		font-size: 24rpx;
	}
</style>
