<template>
	<view>
		<view
			id="_drag_button"
			class="drag"
			v-show="isReady"
			:style="'left: ' + left + 'px; top:' + top + 'px;'"
			@touchstart="touchstart"
			@touchmove.stop.prevent="touchmove"
			@touchend="touchend"
			@click.stop.prevent="click"
			
			:class="{transition: isDock && !isMove }"
		>
			<view v-if="!show">
				<image :src="img_xy" mode="heightFix" style="height: 180upx;" class="zwyJittery1"></image>
			</view>
			<view v-if="show">
				<image src="../../static/kfindex.png" mode="heightFix" style="height: 180upx;" class=""></image>
			</view>
			<!-- <text>{{ text }}</text> -->
		</view>
	</view>
</template>

<script>
	var startTime
	export default {
		name: 'drag-button',
		props: {
			isDock:{
				type: Boolean,
				default: false
			},
			existTabBar:{
				type: <PERSON><PERSON><PERSON>,
				default: false
			}
		},
		data() {
			return {
				top:0,
				left:0,
				width: 113,
				height: 135,
				offsetWidth: 0,
				offsetHeight: 0,
				windowWidth: 0,
				windowHeight: 0,
				isMove: true,
				edge: 10,
				text: '按钮',
				show:false,
				isReady: false,
				img_xy:""
			}
		},
		mounted() {
			const sys = uni.getSystemInfoSync();
			this.windowWidth = sys.windowWidth;
			this.windowHeight = sys.windowHeight;
			
			// #ifdef APP-PLUS
			this.existTabBar && (this.windowHeight -= 50);
			// #endif
			if (sys.windowTop) {
				this.windowHeight += sys.windowTop;
			}
			
			// 设置默认偏移量
			this.offsetWidth = this.width / 2;
			this.offsetHeight = this.height / 2;
			
			// 设置初始位置并显示
			this.left = this.windowWidth - this.width - this.edge + 30;
			this.top = this.windowHeight - this.height - this.edge - 100;
			this.isReady = true;
			
			// 延迟获取实际尺寸并调整
			this.initPosition();
		},
		methods: {
			initPosition() {
				// 延迟执行以确保组件已渲染
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('#_drag_button').boundingClientRect(data => {
						if(data) {
							this.width = data.width;
							this.height = data.height;
							this.offsetWidth = data.width / 2;
							this.offsetHeight = data.height / 2;
							this.left = this.windowWidth - this.width - this.edge + 30;
							this.top = this.windowHeight - this.height - this.edge - 100;
							this.img_xy='../../static/yckf.png'
						} else {
							// 如果获取失败，重试一次
							setTimeout(() => {
								this.initPosition()
							}, 100)
						}
					}).exec();
				}, 100)
			},
			click() {
				console.log(this.show)
				if(this.show){
					this.$emit('btnClick');
					return;
				}else{
					this.show=true
					if (this.left < this.windowWidth / 2 - this.offsetWidth) {
						this.left = this.left+30;
					} else {
						this.left = this.left-30;
					}
					setTimeout(()=>{
						this.show=false
						let edgeRigth = this.windowWidth - this.width - this.edge;
						
						if (this.left < this.windowWidth / 2 - this.offsetWidth) {
							this.img_xy='../../static/yckf_left.png'
							this.left = this.edge-30;
						} else {
							this.img_xy='../../static/yckf.png'
							this.left = edgeRigth+30;
						}
					},3000)
				}
				
				
			},
			touchstart(e) {
				// console.log(e)
				this.$emit('btnTouchstart',e);
				startTime = Date.now();
			},
			touchmove(e) {
				this.show=true
				// 单指触摸
				if (e.touches.length !== 1) {
					return false;
				}
				
				this.isMove = true;
				
				this.left = e.touches[0].clientX - this.offsetWidth;
				
				let clientY = e.touches[0].clientY - this.offsetHeight;
				// #ifdef H5
					clientY += this.height;
				// #endif
				let edgeBottom = this.windowHeight - this.height - this.edge;

				// 上下触及边界
				if (clientY < this.edge) {
					this.top = this.edge;
				} else if (clientY > edgeBottom) {
					this.top = edgeBottom;
				} else {
					this.top = clientY
				}
				
			},
			touchend(e) {
					
				const endTime = Date.now();
				            const duration = endTime - startTime;
				            if (duration < 100) { // 假设300毫秒内为点击
				                return;
				            } else {
				                // 处理滑动等其他触摸操作
				            }
				this.show=false
				if (this.isDock) {
					let edgeRigth = this.windowWidth - this.width - this.edge;
					
					if (this.left < this.windowWidth / 2 - this.offsetWidth) {
						this.img_xy='../../static/yckf_left.png'
						this.left = this.edge-30;
					} else {
						this.img_xy='../../static/yckf.png'
						this.left = edgeRigth+30;
					}
					
				}
				
				this.isMove = false;
				
				this.$emit('btnTouchend');
			},
		}}
</script>

<style lang="scss">
	.drag {
		display: flex;
		justify-content: center;
		align-items: center;
		// background-color: rgba(0, 0, 0, 0.5);
		// box-shadow: 0 0 6upx rgba(0, 0, 0, 0.4);
		color: $uni-text-color-inverse;
		width: 113upx;
		height: 135upx;
		// border-radius: 50%;
		font-size: $uni-font-size-sm;
		position: fixed;
		z-index: 1;
		
		&.transition {
			transition: left .3s ease,top .3s ease;
		}
	}
	
</style>
