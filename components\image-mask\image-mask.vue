<template>
  <view class="mask" v-if="show" @click="closeMask">
    <view class="mask-content" @click.stop>
      <image 
        src="/static/<EMAIL>" 
        mode="widthFix"
        class="mask-image"
		@click="addChild"
      ></image>
      <view class="close-btn" @click="closeMask">
        <u-icon name="close-circle" color="#fff" size="30"></u-icon>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'image-mask',
  data() {
    return {
      show: false
    }
  },
  methods: {
	  addChild(){
		  uni.navigateTo({
		    url:'/pages/children/children'
		  })
		    this.show = false
	  },
    showMask() {
      this.show = true
	  // uni.hideTabBar()
    },
    closeMask() {
		// uni.showTabBar()
      this.show = false
    }
  }
}
</script>

<style lang="scss" scoped>
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  
  // 确保遮罩能覆盖原生tabbar
  height: 100vh !important;
  
  .mask-content {
    position: relative;
    width: 560rpx;
    animation: zoomIn 0.3s ease;
    
    .mask-image {
      width: 100%;
      border-radius: 20rpx;
    }
    
    .close-btn {
      position: absolute;
      bottom: -100rpx;
      left: 50%;
      transform: translateX(-50%);
      padding: 20rpx;
    }
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style> 