<template>
	<view class="ai-chat-container">
		<u-navbar
			title="智能客服"
			:autoBack="true"
			:placeholder="true"
		>
			<view slot="right" class="navbar-right">
				<view class="icon-wrapper" @click="toggleCall">
					<u-icon name="phone" color="#333" size="24"></u-icon>
				</view>
				<view class="icon-wrapper" @click="toggleMute">
					<u-icon :name="isMuted ? 'volume-off' : 'volume'" color="#333" size="24"></u-icon>
				</view>
			</view>
		</u-navbar>
		
		<!-- 使用z-paging组件替代scroll-view -->
		<z-paging 
			ref="paging" 
			class="chat-content"
			:show-scrollbar="false" 
			refresher-background='#f6f6f6' 
			:use-page-scroll="false"
			@query="loadMessages"
			style="padding-top: 100upx;"
		>
			<template #top>
				<!-- 顶部占位，如有需要 -->
			</template>
			
			<template #refresher="{refresherStatus}">
				<custom-refresher :status="refresherStatus" color="#000" />
			</template>
			
			<template #loadingMoreNoMore>
				<custom-nomore />
			</template>
			
			<view v-if="loading" class="loading-container">
				<!-- 简单加载提示，移除u-loading组件 -->
				<view class="simple-loading">
					<text>加载中...</text>
				</view>
			</view>
			
			<view v-else class="message-list">
				<view class="time-divider">{{currentDate}}</view>
				
				<!-- AI消息 -->
				<view class="message-item ai-message" v-for="(item, index) in aiMessages" :key="'ai-'+index">
					<view class="avatar">
						<image src="/static/image/ai-avatar.png" mode="aspectFill"></image>
					</view>
					<view class="message-content">
						<view class="message-bubble">
							<rich-text :nodes="item.content"></rich-text>
						</view>
						<view class="message-time">{{item.time}}</view>
					</view>
				</view>
				
				<!-- 用户消息 -->
				<view class="message-item user-message" v-for="(item, index) in userMessages" :key="'user-'+index">
					<view class="message-content">
						<view class="message-bubble" :class="{'voice-message': item.isVoice}">
							<view v-if="item.isVoice" class="voice-message-content" @click="playVoice(item)">
								<view class="voice-icon">
									<view class="voice-wave-icon">
										<view class="wave-line" v-for="(line, i) in 4" :key="i"></view>
									</view>s
								</view>
								<text>{{item.duration}}″</text>
							</view>
							<rich-text v-else :nodes="item.content"></rich-text>
						</view>
						<view class="message-time">{{item.time}}</view>
					</view>
					<view class="avatar">
						<image :src="userAvatar || '/static/image/default-avatar.png'" mode="aspectFill"></image>
					</view>
				</view>
			</view>
			
			<u-empty
				v-if="!loading && messages.length === 0"
				text="暂无聊天记录"
				mode="chat"
				icon="/static/empty/chat.png"
			>
			</u-empty>
		</z-paging>
		
		<!-- 底部输入区域 - 固定在底部 -->
		<view class="chat-footer">
			<view class="input-area">
				<!-- 语音/键盘切换按钮 -->
				<view class="voice-keyboard-toggle" @click="toggleInputMode">
					<image src="/static/image/keyboard-fill.png" mode="" style="width: 50upx;height: 50upx;" v-if="isVoiceMode"></image>
					<u-icon name="mic" color="#333" size="26" v-else></u-icon>
				</view>
				
				<!-- 文本输入框 - 在非语音模式下显示 -->
				<input 
					v-if="!isVoiceMode"
					class="message-input" 
					v-model="inputMessage" 
					placeholder="请输入消息..." 
					confirm-type="send"
					@confirm="sendMessage"
				/>
				
				<!-- 语音按钮 - 在语音模式下显示 -->
				<view 
					v-else
					class="voice-button-large" 
					@touchstart="startRecording" 
					@touchend="stopRecording"
					@touchcancel="cancelRecording"
				>
					<text>按住说话</text>
				</view>
				
				<view class="attachment-button" @click="showActionSheet = true">
					<u-icon name="plus-circle" color="#333" size="26"></u-icon>
				</view>
				
				<view class="send-button" @click="sendMessage" v-if="inputMessage.trim() && !isVoiceMode">
					<u-icon name="arrow-right" color="#1482ff" size="32"></u-icon>
				</view>
			</view>
		</view>
		
		<!-- 功能选择面板 -->
		<u-action-sheet 
			:actions="actionItems" 
			:show="showActionSheet"
			@select="handleActionSelect"
			@close="showActionSheet = false"
			cancel-text="取消">
		</u-action-sheet>
		
		<!-- 语音录制动画提示 -->
		<view class="voice-recording-tip" v-if="isRecording">
			<view class="voice-wave">
				<!-- 动态波形效果 -->
				<view class="voice-wave-container">
					<view 
						v-for="(item, index) in voiceWaveArray" 
						:key="index" 
						class="voice-wave-bar"
						:style="{height: item + 'rpx'}"
					></view>
				</view>
			</view>
			<text>{{recordingTip}}</text>
		</view>
		
		<!-- 通话状态显示 -->
		<view class="call-status" v-if="isInCall || callStatus === 'connecting'">
			<view class="call-status-container">
				<view class="call-animation">
					<view class="call-ripple"></view>
					<view class="call-ripple delay-1"></view>
					<view class="call-ripple delay-2"></view>
				</view>
				<view class="call-info">
					<view class="call-title">
						{{ isInCall ? (callId && callId.startsWith('mock_') ? '模拟通话中' : '通话中') : '连接中...' }}
					</view>
					<view class="call-timer" v-if="isInCall">{{ callDuration }}</view>
					<view class="call-status-text">{{ sdkLoadError ? '实时SDK加载失败，使用模拟模式' : '' }}</view>
				</view>
				<view class="call-actions">
					<view class="call-action-btn mute-btn" :class="{'active': isMuted}" @click="toggleMute">
						<u-icon :name="isMuted ? 'volume-off' : 'volume'" color="#fff" size="24"></u-icon>
					</view>
					<view class="call-action-btn end-btn" @click="toggleCall">
						<u-icon name="close" color="#fff" size="24"></u-icon>
					</view>
				</view>
			</view>
		</view>
		
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
	// import { RealtimeUtils } from "@coze/realtime-api";
	export default {
		data() {
			return {
				loading: false,
				inputMessage: '',
				messages: [],
				isRefreshing: false,
				showActionSheet: false,
				isMuted: false,
				isInCall: false,
				userAvatar: '',
				currentDate: this.formatDate(new Date()),
				isVoiceMode: false, // 是否是语音输入模式
				isRecording: false, // 是否正在录音
				recordingTip: '松开发送，上滑取消', // 录音提示文本
				recorderManager: null, // 录音管理器
				currentVoiceFile: '', // 当前录音文件路径
				recordingTimeout: null, // 录音超时计时器
				recordDuration: 0, // 录音时长(秒)
				recordStartTime: 0, // 录音开始时间
				cancelRecordY: 0, // 开始录音时的Y坐标，用于判断上滑取消
				voiceWaveArray: [10, 15, 20, 15, 10, 15, 20, 15, 10], // 语音波形高度数组
				animationTimer: null, // 波形动画定时器
				actionItems: [
					{
						name: '发送图片',
						icon: 'photo'
					},
					{
						name: '发送文件',
						icon: 'file-text'
					},
					{
						name: '我的材料',
						icon: 'folder'
					}
				],
				// 测试数据
				aiMessages: [
					{
						content: '很高兴见到你！我是你的专属智能客服，随时准备为你提供一切需要的支持。遇到任何问题记得找我哦！',
						time: '09:12'
					},
					{
						content: '很高兴见到你！我是你的专属AI助手，随时准备为你提供一切需要的支持，快来搭建你个有趣的小天地，遇到任何问题记得找我哦！',
						time: '09:15'
					}
				],
				userMessages: [
					{
						content: '帮我写份工作材料',
						time: '09:14'
					}
				],
				// Coze实时通话相关
				realtimeClient: null, // Coze实时客户端
				callId: null, // 当前通话ID
				callStatus: '', // 通话状态
				permissionChecked: false, // 是否已检查设备权限
				hasAudioPermission: false, // 是否有麦克风权限
				reconnecting: false, // 是否正在重连
				incomingCall: false, // 是否有来电
				callDuration: '00:00', // 通话持续时间
				callStartTime: 0, // 通话开始时间戳
				callTimerInterval: null, // 通话计时器
				sdkLoadError: null, // SDK加载错误信息
			}
		},
		computed: {
			// 所有消息按时间排序
			allMessages() {
				// 实际应用中需要合并aiMessages和userMessages并按时间排序
				return this.messages;
			},
			// 是否有更多消息可加载
			hasMoreMessages() {
				// 实际应用中根据API返回判断是否有更多历史消息
				return false;
			}
		},
		async onLoad() {
			// 获取用户信息
			this.getUserInfo();
			
			// 初始化录音管理器
			this.initRecorderManager();
			
			// 检查设备权限
			const result = await RealtimeUtils.checkDevicePermission();
			
			// 检查麦克风权限
			if (!result.audio) {
			    throw new Error("需要麦克风访问权限");
			}
			// 先不初始化Coze实时通话，等点击通话按钮时再初始化
			// this.initCozeRealtime();
		},
		beforeDestroy() {
			// 清理录音相关资源
			if (this.animationTimer) {
				clearInterval(this.animationTimer);
			}
			
			// 停止录音
			if (this.isRecording && this.recorderManager) {
				try {
					this.recorderManager.stop();
				} catch (e) {
					console.error('停止录音失败', e);
				}
			}
			
			// 断开Coze实时通话
			this.disconnectCall();
		},
		methods: {
			// 初始化Coze实时通话
			async initCozeRealtime() {
				try {
					// 通过脚本方式动态加载Coze SDK
					// 而不是使用import动态导入
					await new Promise((resolve, reject) => {
						// 检查是否已经加载过SDK
						if (window.CozeRealtimeAPI) {
							resolve();
							return;
						}
						
						// 创建脚本元素
						const script = document.createElement('script');
						script.src = 'https://cdn.jsdelivr.net/npm/@coze/realtime-api/dist/index.browser.js';
						script.onload = resolve;
						script.onerror = () => reject(new Error('无法加载Coze SDK'));
						document.head.appendChild(script);
					});
					
					// 使用全局变量
					const { EventNames, RealtimeClient, RealtimeUtils } = window.CozeRealtimeAPI;
					
					// 检查设备权限
					const result = await RealtimeUtils.checkDevicePermission();
					this.permissionChecked = true;
					this.hasAudioPermission = result.audio;
					
					if (!result.audio) {
						uni.showToast({
							title: '需要麦克风访问权限',
							icon: 'none'
						});
						return;
					}
					
					// 初始化客户端 - 这里需要替换成实际的配置
					const client = new RealtimeClient({
						accessToken: 'pat_MiEOFjdvwpe4zPCBPmRyEEHO4qCYv5uNWoI7pummdZmvD79ZsBiriVRsFQTAratX',
						botId: '7477484716074663973',
						connectorId: '1024',
						allowPersonalAccessTokenInBrowser: true,
					});
					
					// 配置事件监听
					this.setupEventListeners(client, EventNames);
					
					// 存储客户端实例
					this.realtimeClient = client;
					
					console.log('Coze实时通话初始化成功');
					return client;
				} catch (error) {
					console.error('初始化Coze实时通话失败:', error);
					this.sdkLoadError = error.message || '未知错误';
					uni.showToast({
						title: '通话功能不可用，使用模拟模式',
						icon: 'none',
						duration: 2000
					});
					
					// 返回null表示初始化失败
					return null;
				}
			},
			
			// 设置事件监听
			setupEventListeners(client, EventNames) {
				// 监听所有事件
				client.on(EventNames.ALL, (eventName, data) => {
					console.log('Coze事件:', eventName, data);
				});
				
				// 监听连接成功
				client.on(EventNames.CONNECTED, () => {
					console.log('已连接Coze服务器');
					this.callStatus = 'connected';
				});
				
				// 监听连接错误
				client.on(EventNames.CONNECTION_ERROR, (error) => {
					console.error('连接错误:', error);
					this.callStatus = 'error';
					uni.showToast({
						title: '连接错误',
						icon: 'none'
					});
				});
				
				// 监听断开连接
				client.on(EventNames.DISCONNECTED, () => {
					console.log('已断开连接');
					this.callStatus = 'disconnected';
					this.isInCall = false;
				});
				
				// 监听重连中
				client.on(EventNames.RECONNECTING, () => {
					console.log('正在重连...');
					this.reconnecting = true;
					this.callStatus = 'reconnecting';
				});
				
				// 监听重连成功
				client.on(EventNames.RECONNECTED, () => {
					console.log('重连成功');
					this.reconnecting = false;
					this.callStatus = 'connected';
				});
				
				// 通话开始
				client.on(EventNames.CALL_STARTED, (data) => {
					console.log('通话已开始:', data);
					this.callId = data.callId;
					this.isInCall = true;
					this.callStatus = 'active';
					
					// 开始计时
					this.startCallTimer();
					
					uni.showToast({
						title: '通话已开始',
						icon: 'none'
					});
				});
				
				// 通话结束
				client.on(EventNames.CALL_ENDED, (data) => {
					console.log('通话已结束:', data);
					this.callId = null;
					this.isInCall = false;
					this.callStatus = 'ended';
					
					// 停止计时
					this.stopCallTimer();
					
					uni.showToast({
						title: '通话已结束',
						icon: 'none'
					});
				});
				
				// 接收音频数据
				client.on(EventNames.AUDIO_DATA, (data) => {
					// 这里可以处理接收到的音频数据
					console.log('收到音频数据');
				});
				
				// AI回复开始
				client.on(EventNames.AI_RESPONSE_STARTED, () => {
					console.log('AI开始回复');
					// 这里可以显示AI正在思考的状态
				});
				
				// AI回复结束
				client.on(EventNames.AI_RESPONSE_ENDED, () => {
					console.log('AI回复结束');
					// 这里可以更新UI状态
				});
				
				// 来电通知
				client.on(EventNames.INCOMING_CALL, (data) => {
					console.log('有来电:', data);
					this.incomingCall = true;
					this.callId = data.callId;
					
					// 显示来电提示
					uni.showModal({
						title: '来电',
						content: '有新的来电，是否接听？',
						confirmText: '接听',
						cancelText: '拒绝',
						success: (res) => {
							if (res.confirm) {
								this.answerCall();
							} else {
								this.rejectCall();
							}
						}
					});
				});
			},
			
			// 开始通话 - 带降级方案
			async startCall() {
				// 如果客户端不存在，先初始化
				if (!this.realtimeClient) {
					try {
						const client = await this.initCozeRealtime();
						if (!client) {
							// SDK加载失败，启用模拟通话模式
							this.startMockCall();
							return;
						}
					} catch (error) {
						console.error('初始化通话失败:', error);
						// 启用模拟通话模式
						this.startMockCall();
						return;
					}
				}
				
				if (this.isInCall) {
					console.log('已在通话中');
					return;
				}
				
				try {
					// 连接服务器
					if (this.callStatus !== 'connected') {
						await this.realtimeClient.connect();
					}
					
					// 开始通话
					const callResult = await this.realtimeClient.startCall();
					this.callId = callResult.callId;
					this.isInCall = true;
					this.callStatus = 'active';
					
					// 开始计时
					this.startCallTimer();
					
					console.log('通话已开始:', callResult);
					uni.showToast({
						title: '通话已开始',
						icon: 'none'
					});
				} catch (error) {
					console.error('开始通话失败:', error);
					// 实际通话失败，启用模拟通话模式
					this.startMockCall();
				}
			},
			
			// 模拟通话功能 - 在SDK加载失败时使用
			startMockCall() {
				// 生成模拟通话ID
				this.callId = 'mock_' + Date.now();
				this.isInCall = true;
				this.callStatus = 'active';
				
				// 开始计时
				this.startCallTimer();
				
				// 添加一条系统消息
				this.aiMessages.push({
					content: '通话已连接（模拟模式）。您可以正常发送消息，但语音功能可能受限。',
					time: this.formatTime(new Date())
				});
				
				// 滚动到底部
				this.scrollToBottom();
				
				uni.showToast({
					title: '已启用模拟通话',
					icon: 'none'
				});
			},
			
			// 结束模拟通话
			endMockCall() {
				this.isInCall = false;
				this.callId = null;
				this.callStatus = 'ended';
				
				// 停止计时
				this.stopCallTimer();
				
				// 添加一条系统消息
				this.aiMessages.push({
					content: '通话已结束（模拟模式）',
					time: this.formatTime(new Date())
				});
				
				// 滚动到底部
				this.scrollToBottom();
				
				uni.showToast({
					title: '通话已结束',
					icon: 'none'
				});
			},
			
			// 切换通话状态 - 带降级支持
			async toggleCall() {
				if (this.isInCall) {
					// 检查是否是模拟通话
					if (this.callId && this.callId.startsWith('mock_')) {
						this.endMockCall();
					} else {
						await this.endCall();
					}
				} else {
					await this.startCall();
				}
			},
			
			// 结束通话
			async endCall() {
				if (!this.realtimeClient || !this.isInCall) {
					return;
				}
				
				try {
					await this.realtimeClient.endCall(this.callId);
					this.isInCall = false;
					this.callId = null;
					this.callStatus = 'ended';
					
					uni.showToast({
						title: '通话已结束',
						icon: 'none'
					});
				} catch (error) {
					console.error('结束通话失败:', error);
					uni.showToast({
						title: '结束通话失败',
						icon: 'none'
					});
				}
			},
			
			// 接听来电
			async answerCall() {
				if (!this.realtimeClient || !this.incomingCall) {
					return;
				}
				
				try {
					await this.realtimeClient.answerCall(this.callId);
					this.incomingCall = false;
					this.isInCall = true;
					this.callStatus = 'active';
					
					uni.showToast({
						title: '已接听',
						icon: 'none'
					});
				} catch (error) {
					console.error('接听来电失败:', error);
					uni.showToast({
						title: '接听失败',
						icon: 'none'
					});
				}
			},
			
			// 拒绝来电
			async rejectCall() {
				if (!this.realtimeClient || !this.incomingCall) {
					return;
				}
				
				try {
					await this.realtimeClient.rejectCall(this.callId);
					this.incomingCall = false;
					this.callId = null;
					
					uni.showToast({
						title: '已拒绝',
						icon: 'none'
					});
				} catch (error) {
					console.error('拒绝来电失败:', error);
				}
			},
			
			// 静音/取消静音
			async toggleMute() {
				// 如果不在通话中或客户端不存在，只切换状态
				if (!this.realtimeClient || !this.isInCall) {
					this.isMuted = !this.isMuted;
					uni.showToast({
						title: this.isMuted ? '已静音' : '已取消静音',
						icon: 'none'
					});
					return;
				}
				
				try {
					if (this.isMuted) {
						// 取消静音
						await this.realtimeClient.unmuteMicrophone();
					} else {
						// 静音
						await this.realtimeClient.muteMicrophone();
					}
					
					this.isMuted = !this.isMuted;
					uni.showToast({
						title: this.isMuted ? '已静音' : '已取消静音',
						icon: 'none'
					});
				} catch (error) {
					console.error('切换静音状态失败:', error);
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					});
				}
			},
			
			// 断开连接
			async disconnectCall() {
				if (!this.realtimeClient) {
					return;
				}
				
				try {
					// 如果在通话中先结束通话
					if (this.isInCall) {
						await this.endCall();
					}
					
					// 断开连接
					await this.realtimeClient.disconnect();
					this.callStatus = 'disconnected';
					console.log('已断开Coze连接');
				} catch (error) {
					console.error('断开连接失败:', error);
				}
			},
			
			// 获取用户信息
			getUserInfo() {
				// 从缓存或API获取用户头像等信息
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.userAvatar = userInfo.avatarUrl;
				}
			},
			
			// 初始化录音管理器
			initRecorderManager() {
				try {
					// 检查平台是否支持录音功能
					if (!uni.getRecorderManager) {
						console.error('当前平台不支持录音功能');
						uni.showToast({
							title: '当前平台不支持录音',
							icon: 'none'
						});
						return;
					}
					
					// 获取全局唯一的录音管理器
					this.recorderManager = uni.getRecorderManager();
					
					// 检查录音管理器是否有效
					if (!this.recorderManager) {
						console.error('录音管理器初始化失败');
						uni.showToast({
							title: '录音功能不可用',
							icon: 'none'
						});
						return;
					}
					
					// 监听录音开始事件
					if (typeof this.recorderManager.onStart === 'function') {
						this.recorderManager.onStart(() => {
							console.log('录音开始');
							this.isRecording = true;
							this.recordStartTime = Date.now();
							
							// 开始波形动画
							this.startWaveAnimation();
							
							// 设置录音超时（最长60秒）
							this.recordingTimeout = setTimeout(() => {
								if (this.isRecording) {
									this.stopRecording();
								}
							}, 60000);
						});
					}
					
					// 监听录音结束事件
					if (typeof this.recorderManager.onStop === 'function') {
						this.recorderManager.onStop((res) => {
							console.log('录音结束', res);
							this.isRecording = false;
							clearTimeout(this.recordingTimeout);
							
							// 停止波形动画
							if (this.animationTimer) {
								clearInterval(this.animationTimer);
								this.animationTimer = null;
							}
							
							// 计算录音时长
							this.recordDuration = Math.round((Date.now() - this.recordStartTime) / 1000);
							
							// 处理录音文件
							if (res && res.tempFilePath) {
								this.currentVoiceFile = res.tempFilePath;
								// 发送语音消息
								this.sendVoiceMessage(res.tempFilePath, this.recordDuration);
							}
						});
					}
					
					// 监听录音错误事件
					if (typeof this.recorderManager.onError === 'function') {
						this.recorderManager.onError((res) => {
							console.error('录音失败', res);
							this.isRecording = false;
							clearTimeout(this.recordingTimeout);
							
							uni.showToast({
								title: '录音失败',
								icon: 'none'
							});
						});
					}
					
					// 监听声音大小变化事件 - 仅在支持的平台使用
					if (this.recorderManager.onFrameRecorded && typeof this.recorderManager.onFrameRecorded === 'function') {
						this.recorderManager.onFrameRecorded((res) => {
							// 处理音频数据
						});
					}
				} catch (e) {
					console.error('初始化录音管理器失败', e);
					this.recorderManager = null; // 确保失败时设为null
					uni.showToast({
						title: '录音功能初始化失败',
						icon: 'none'
					});
				}
			},
			
			// 开始波形动画 - 使用随机波形代替依赖于分贝值的波形
			startWaveAnimation() {
				// 清除之前的动画定时器
				if (this.animationTimer) {
					clearInterval(this.animationTimer);
				}
				
				// 创建新的动画定时器 - 模拟波形变化
				this.animationTimer = setInterval(() => {
					let baseHeight = 10;
					let maxHeight = 40;
					
					let newWave = [];
					for (let i = 0; i < 9; i++) {
						// 生成随机波形高度
						let randomFactor = Math.random(); // 0~1之间的随机数
						let height = baseHeight + (maxHeight - baseHeight) * randomFactor;
						newWave.push(Math.round(height));
					}
					
					this.voiceWaveArray = newWave;
				}, 150); // 每150ms更新一次波形
			},
			
			// z-paging的查询方法
			loadMessages(pageNo, pageSize) {
				if (pageNo === 1) {
					// 第一页，加载最新消息
					this.loadChatHistory();
				} else {
					// 加载更多历史消息
					this.loadMoreHistoryMessages(pageNo, pageSize);
				}
			},
			
			// 加载聊天历史
			loadChatHistory() {
				this.loading = true;
				// 模拟API请求
				setTimeout(() => {
					// 实际应用中这里应该调用API获取聊天记录
					this.loading = false;
					
					// 通知z-paging加载完成
					this.$refs.paging && this.$refs.paging.complete();
				}, 800);
			},
			
			// 加载更多历史消息
			loadMoreHistoryMessages(pageNo, pageSize) {
				// 实现分页加载更多历史消息逻辑
				this.loading = true;
				// 模拟API请求
				setTimeout(() => {
					// 加载更多历史消息
					this.loading = false;
					
					if (this.hasMoreMessages) {
						// 有更多数据
						this.$refs.paging && this.$refs.paging.complete();
					} else {
						// 没有更多数据了
						this.$refs.paging && this.$refs.paging.completeByTotal(this.aiMessages.length + this.userMessages.length);
					}
				}, 800);
			},
			
			// 发送消息
			async sendMessage() {
				if (!this.inputMessage.trim()) return;
				
				// 构造消息对象
				const message = {
					content: this.inputMessage,
					time: this.formatTime(new Date()),
					isUser: true
				};
				
				// 添加到消息列表
				this.userMessages.push({
					content: message.content,
					time: message.time
				});
				
				// 清空输入框
				const messageContent = this.inputMessage;
				this.inputMessage = '';
				
				// 滚动到底部
				this.scrollToBottom();
				
				// 如果在通话中，则发送消息到实时通话
				if (this.isInCall && this.realtimeClient) {
					try {
						await this.realtimeClient.sendMessage({
							type: 'text',
							content: messageContent
						});
					} catch (error) {
						console.error('发送消息到实时通话失败:', error);
					}
				} else {
					// 否则模拟AI回复
					this.simulateAIResponse();
				}
			},
			
			// 模拟AI回复
			simulateAIResponse() {
				// 显示AI正在输入状态
				setTimeout(() => {
					// AI回复消息
					this.aiMessages.push({
						content: '我已收到您的消息，正在为您处理...',
						time: this.formatTime(new Date())
					});
					
					// 滚动到底部
					this.scrollToBottom();
				}, 1000);
			},
			
			// 切换输入模式（语音/键盘）
			toggleInputMode() {
				this.isVoiceMode = !this.isVoiceMode;
			},
			
			// 开始录音
			startRecording(e) {
				// 防止多次调用
				if (this.isRecording) return;
				
				try {
					// 检查录音管理器是否可用
					if (!this.recorderManager) {
						console.error('录音管理器未初始化');
						uni.showToast({
							title: '录音功能不可用',
							icon: 'none'
						});
						return;
					}
					
					// 检查start方法是否存在
					if (typeof this.recorderManager.start !== 'function') {
						console.error('录音管理器不支持start方法');
						uni.showToast({
							title: '录音功能不支持',
							icon: 'none'
						});
						return;
					}
					
					// 记录触摸开始的Y坐标，用于判断上滑取消
					if (e && e.touches && e.touches[0]) {
						this.cancelRecordY = e.touches[0].clientY;
					}
					
					// 设置录音参数
					const options = {
						duration: 60000, // 最长录音时间，单位ms
						sampleRate: 16000, // 采样率
						numberOfChannels: 1, // 录音通道数
						encodeBitRate: 96000, // 编码码率
						format: 'mp3' // 音频格式
					};
					
					// 开始录音
					this.recorderManager.start(options);
				} catch (e) {
					console.error('开始录音失败', e);
					this.isRecording = false; // 确保状态正确
					uni.showToast({
						title: '录音失败',
						icon: 'none'
					});
				}
			},
			
			// 结束录音并发送
			stopRecording(e) {
				if (!this.isRecording) return;
				
				try {
					// 检查录音管理器是否可用
					if (!this.recorderManager || typeof this.recorderManager.stop !== 'function') {
						console.error('录音管理器不可用或不支持stop方法');
						this.isRecording = false;
						clearTimeout(this.recordingTimeout);
						
						if (this.animationTimer) {
							clearInterval(this.animationTimer);
							this.animationTimer = null;
						}
						
						uni.showToast({
							title: '录音功能不支持',
							icon: 'none'
						});
						return;
					}
					
					// 检查是否为上滑取消
					if (e && e.changedTouches && e.changedTouches[0]) {
						const curY = e.changedTouches[0].clientY;
						const diffY = this.cancelRecordY - curY;
						
						// 如果上滑距离超过50，则取消发送
						if (diffY > 50) {
							this.cancelRecording();
							return;
						}
					}
					
					// 停止录音
					this.recorderManager.stop();
				} catch (e) {
					console.error('停止录音失败', e);
					this.isRecording = false;
					clearTimeout(this.recordingTimeout);
					
					// 清除动画定时器
					if (this.animationTimer) {
						clearInterval(this.animationTimer);
						this.animationTimer = null;
					}
					
					uni.showToast({
						title: '录音失败',
						icon: 'none'
					});
				}
			},
			
			// 取消录音
			cancelRecording() {
				if (!this.isRecording) return;
				
				try {
					// 检查录音管理器是否可用
					if (!this.recorderManager || typeof this.recorderManager.stop !== 'function') {
						console.error('录音管理器不可用或不支持stop方法');
						this.isRecording = false;
						this.recordingTip = '录音已取消';
						
						setTimeout(() => {
							this.recordingTip = '松开发送，上滑取消';
						}, 500);
						
						// 清除录音文件
						this.currentVoiceFile = '';
						
						// 清除动画定时器
						if (this.animationTimer) {
							clearInterval(this.animationTimer);
							this.animationTimer = null;
						}
						
						uni.showToast({
							title: '录音功能不支持',
							icon: 'none'
						});
						return;
					}
					
					this.recordingTip = '已取消录音';
					setTimeout(() => {
						this.recordingTip = '松开发送，上滑取消';
						this.isRecording = false;
					}, 500);
					
					// 停止录音但不发送
					this.recorderManager.stop();
					
					// 清除录音文件
					this.currentVoiceFile = '';
					
					uni.showToast({
						title: '已取消录音',
						icon: 'none'
					});
				} catch (e) {
					console.error('取消录音失败', e);
					this.isRecording = false;
					
					// 清除动画定时器
					if (this.animationTimer) {
						clearInterval(this.animationTimer);
						this.animationTimer = null;
					}
					
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					});
				}
			},
			
			// 发送语音消息
			async sendVoiceMessage(filePath, duration) {
				// 构造语音消息对象
				const message = {
					content: '[语音消息]',
					time: this.formatTime(new Date()),
					isUser: true,
					isVoice: true,
					filePath: filePath, // 语音文件路径
					duration: duration || 1 // 语音时长(秒)
				};
				
				// 添加到消息列表
				this.userMessages.push(message);
				
				// 滚动到底部
				this.scrollToBottom();
				
				// 如果在通话中，则发送语音消息到实时通话
				if (this.isInCall && this.realtimeClient) {
					try {
						// 读取音频文件
						const fileManager = uni.getFileSystemManager();
						fileManager.readFile({
							filePath: filePath,
							success: async (res) => {
								// 发送到Coze实时通话
								await this.realtimeClient.sendAudio(res.data);
							},
							fail: (err) => {
								console.error('读取音频文件失败:', err);
							}
						});
					} catch (error) {
						console.error('发送语音消息到实时通话失败:', error);
					}
				} else {
					// 否则模拟AI回复
					this.simulateAIResponse();
				}
			},
			
			// 播放语音消息
			playVoice(message) {
				if (!message.filePath) return;
				
				try {
					const innerAudioContext = uni.createInnerAudioContext();
					innerAudioContext.src = message.filePath;
					innerAudioContext.autoplay = true;
					
					innerAudioContext.onPlay(() => {
						console.log('开始播放语音');
					});
					
					innerAudioContext.onError((res) => {
						console.error('播放语音失败', res);
						uni.showToast({
							title: '播放失败',
							icon: 'none'
						});
					});
				} catch (e) {
					console.error('播放语音失败', e);
					uni.showToast({
						title: '播放失败',
						icon: 'none'
					});
				}
			},
			
			// 滚动到底部
			scrollToBottom() {
				this.$nextTick(() => {
					// 使用z-paging的滚动到底部方法
					this.$refs.paging && this.$refs.paging.scrollToBottom(false);
				});
			},
			
			// 处理附件选择
			handleActionSelect(index) {
				switch(index) {
					case 0: // 发送图片
						this.chooseImage();
						break;
					case 1: // 发送文件
						this.chooseFile();
						break;
					case 2: // 我的材料
						uni.navigateTo({
							url: '/pages/materials/index'
						});
						break;
				}
			},
			
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						// 处理选择的图片
						const tempFilePath = res.tempFilePaths[0];
						this.uploadFile(tempFilePath, 'image');
					}
				});
			},
			
			// 选择文件
			chooseFile() {
				// 选择文件逻辑，具体API根据平台支持情况调整
				uni.showToast({
					title: '选择文件功能开发中',
					icon: 'none'
				});
			},
			
			// 上传文件
			uploadFile(filePath, type) {
				uni.showLoading({
					title: '正在上传...'
				});
				
				// 实际应用中这里应该调用上传API
				setTimeout(() => {
					uni.hideLoading();
					
					// 模拟发送成功
					const message = {
						content: type === 'image' ? `<img src="${filePath}" style="max-width: 200px;" />` : '[文件]',
						time: this.formatTime(new Date()),
						isUser: true
					};
					
					this.userMessages.push({
						content: message.content,
						time: message.time
					});
					
					// 滚动到底部
					this.scrollToBottom();
					
					// 模拟AI回复
					this.simulateAIResponse();
				}, 1000);
			},
			
			// 下拉刷新 - 会自动触发loadMessages
			onRefresh() {
				// z-paging已经内部处理了刷新逻辑
			},
			
			// 格式化日期
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 格式化时间
			formatTime(date) {
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${hours}:${minutes}`;
			},
			
			// 新增方法：通话计时器
			startCallTimer() {
				// 记录开始时间
				this.callStartTime = Date.now();
				
				// 清除之前的计时器
				if (this.callTimerInterval) {
					clearInterval(this.callTimerInterval);
				}
				
				// 创建新的计时器
				this.callTimerInterval = setInterval(() => {
					const duration = Math.floor((Date.now() - this.callStartTime) / 1000);
					const minutes = Math.floor(duration / 60).toString().padStart(2, '0');
					const seconds = (duration % 60).toString().padStart(2, '0');
					this.callDuration = `${minutes}:${seconds}`;
				}, 1000);
			},
			
			// 停止通话计时器
			stopCallTimer() {
				if (this.callTimerInterval) {
					clearInterval(this.callTimerInterval);
					this.callTimerInterval = null;
				}
				this.callDuration = '00:00';
			},
		}
	}
</script>

<style lang="scss" scoped>
	.ai-chat-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f6f6f6;
		position: relative;
	}
	
	.navbar-right {
		display: flex;
		align-items: center;
		padding-right: 10rpx;
		
		.icon-wrapper {
			width: 80rpx;
			height: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	
	.chat-content {
		flex: 1;
		box-sizing: border-box;
	}
	
	.loading-container {
		display: flex;
		justify-content: center;
		padding: 30rpx 0;
		
		.simple-loading {
			color: #999;
			font-size: 26rpx;
		}
	}
	
	.message-list {
		padding: 20rpx;
	}
	
	.time-divider {
		text-align: center;
		font-size: 24rpx;
		color: #999;
		margin: 20rpx 0;
		background: rgba(0,0,0,0.05);
		padding: 6rpx 20rpx;
		border-radius: 20rpx;
		width: fit-content;
		margin: 20rpx auto;
	}
	
	.message-item {
		display: flex;
		margin-bottom: 30rpx;
		
		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			overflow: hidden;
			flex-shrink: 0;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.message-content {
			max-width: 70%;
			
			.message-bubble {
				padding: 20rpx;
				border-radius: 12rpx;
				word-break: break-all;
				line-height: 1.4;
				
				&.voice-message {
					min-width: 160rpx;
					padding: 16rpx 20rpx;
				}
			}
			
			.message-time {
				font-size: 24rpx;
				color: #999;
				margin-top: 8rpx;
			}
			
			.voice-message-content {
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				.voice-icon {
					display: flex;
					align-items: center;
					
					.voice-wave-icon {
						display: flex;
						align-items: center;
						height: 30rpx;
						
						.wave-line {
							width: 4rpx;
							height: 16rpx;
							background-color: currentColor;
							margin: 0 2rpx;
							border-radius: 2rpx;
							
							&:nth-child(1) {
								height: 10rpx;
							}
							&:nth-child(2) {
								height: 16rpx;
							}
							&:nth-child(3) {
								height: 22rpx;
							}
							&:nth-child(4) {
								height: 16rpx;
							}
						}
					}
				}
				
				text {
					margin-left: 10rpx;
					font-size: 24rpx;
				}
			}
		}
	}
	
	.ai-message {
		.message-content {
			margin-left: 20rpx;
			
			.message-bubble {
				background-color: #fff;
				border: 1px solid #eee;
				border-top-left-radius: 4rpx;
			}
		}
	}
	
	.user-message {
		flex-direction: row-reverse;
		
		.message-content {
			margin-right: 20rpx;
			align-items: flex-end;
			
			.message-bubble {
				background-color: #1482ff;
				color: #fff;
				border-top-right-radius: 4rpx;
			}
			
			.message-time {
				text-align: right;
			}
			
			.voice-message-content {
				flex-direction: row-reverse;
				
				.voice-icon {
					transform: rotateY(180deg); // 翻转语音图标
				}
				
				text {
					margin-left: 0;
					margin-right: 10rpx;
				}
			}
		}
	}
	
	.chat-footer {
		background-color: #fff;
		padding: 20rpx;
		border-top: 1px solid #eee;
		box-sizing: border-box;
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 10;
	}
	
	.input-area {
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		border-radius: 36rpx;
		padding: 10rpx 20rpx;
	}
	
	.voice-keyboard-toggle, .attachment-button {
		flex-shrink: 0;
		padding: 10rpx;
	}
	
	.message-input {
		flex: 1;
		height: 72rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
	}
	
	.voice-button-large {
		flex: 1;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		border-radius: 36rpx;
		margin: 0 10rpx;
		font-size: 28rpx;
		color: #666;
	}
	
	.send-button {
		flex-shrink: 0;
		padding: 10rpx;
	}
	
	.voice-recording-tip {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 260rpx;
		height: 260rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		z-index: 999;
		
		.voice-wave {
			width: 160rpx;
			height: 120rpx;
			margin-bottom: 20rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			
			.voice-wave-container {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: flex-end;
				
				.voice-wave-bar {
					width: 8rpx;
					background-color: #fff;
					margin: 0 4rpx;
					border-radius: 4rpx;
					transition: height 0.1s ease-in-out;
				}
			}
		}
		
		text {
			color: #fff;
			font-size: 26rpx;
		}
	}
	
	// 通话状态显示
	.call-status {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
		
		.call-status-container {
			width: 80%;
			max-width: 600rpx;
			background-color: #fff;
			padding: 40rpx 30rpx;
			border-radius: 20rpx;
			text-align: center;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
			
			.call-animation {
				display: flex;
				justify-content: center;
				margin-bottom: 30rpx;
				
				.call-ripple {
					width: 20rpx;
					height: 20rpx;
					border-radius: 50%;
					background-color: #1482ff;
					margin: 0 5rpx;
					display: inline-block;
					animation: ripple 1.5s infinite ease-in-out;
					
					&.delay-1 {
						animation-delay: 0.2s;
					}
					
					&.delay-2 {
						animation-delay: 0.4s;
					}
				}
			}
			
			.call-info {
				margin-bottom: 40rpx;
				
				.call-title {
					font-size: 32rpx;
					font-weight: bold;
					margin-bottom: 10rpx;
					color: #333;
				}
				
				.call-timer {
					font-size: 28rpx;
					color: #666;
					margin-bottom: 10rpx;
				}
				
				.call-status-text {
					font-size: 24rpx;
					color: #ff6b6b;
					margin-top: 10rpx;
				}
			}
			
			.call-actions {
				display: flex;
				justify-content: center;
				
				.call-action-btn {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					margin: 0 20rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					
					&.mute-btn {
						background-color: #888;
						
						&.active {
							background-color: #ff9800;
						}
					}
					
					&.end-btn {
						background-color: #ff4757;
					}
				}
			}
		}
	}
	
	@keyframes ripple {
		0%, 100% {
			transform: scale(0.8);
			opacity: 0.5;
		}
		50% {
			transform: scale(1.2);
			opacity: 1;
		}
	}
</style>