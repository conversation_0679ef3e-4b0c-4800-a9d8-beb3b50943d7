<template>
	<view class="container">
		<view class="content">
			<!-- 视频列表 -->
			<view class="video-list">
				<view 
					class="video-item" 
					v-for="(item, index) in videoList" 
					:key="index"
					@click="playVideo(item)"
				>
					<view class="video-cover">
						<image :src="item.liveCover" mode="widthFix" class="cover-image"></image>
					
					</view>
		
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="videoList.length === 0 && !loading">
				<image src="/static/empty/2.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无数字人讲解视频</text>
			</view>
			
			<!-- 加载状态 -->
			<view class="loading-state" v-if="loading">
				<u-loading size="24" mode="circle"></u-loading>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loading: true,
			videoList: [],
			companyId: '', // 公司ID
			showVideoPopup: false,
			currentVideo: {},
			currentPage: 1,
			pageSize: 10,
			hasMore: true
		}
	},
	onLoad() {
		// 初始化公司ID
		// this.initCompanyId();
		
		// 获取视频列表
		this.getVideoList();
	},
	methods: {
		// 初始化公司ID
		initCompanyId() {
			// 从全局获取公司ID
			const app = getApp();
			if (app.globalData && app.globalData.companyId) {
				this.companyId = app.globalData.companyId;
			} else {
				// 默认公司ID
				// this.companyId = 'f78d05e497db433c8a96f44b548fce47';
				
				// 如果全局数据对象不存在，初始化它
				if (!app.globalData) {
					app.globalData = {};
				}
				
				// 设置全局公司ID
				app.globalData.companyId = this.companyId;
			}
			
			console.log('当前使用的公司ID:', this.companyId);
		},
		
		// 获取视频列表
		getVideoList(isLoadMore = false) {
			this.loading = true;
			
			this.syghttp.ajax({
				url: this.syghttp.api.liveVideopage,
				method: 'POST',
				data: {
					"page": {
						"maxResultCount": 0,
						"pageNo": this.currentPage,
						"pageSize": this.pageSize,
						"skipCount": 0
					},
					"companyId": this.companyId
				},
				success: (res) => {
					console.log('liveVideopage API返回数据:', res);
					this.loading = false;
					
					if (res.code === 1000 && res.data && res.data.items && res.data.items.items) {
						if (isLoadMore) {
							// 加载更多时，追加数据
							this.videoList = [...this.videoList, ...res.data.items.items];
						} else {
							// 首次加载，直接赋值
							this.videoList = res.data.items.items;
						}
						
						// 判断是否还有更多数据
						if (res.data.items.totalCount > this.videoList.length) {
							this.hasMore = true;
						} else {
							this.hasMore = false;
						}
					} else {
						if (!isLoadMore) {
							this.videoList = [];
						}
						this.hasMore = false;
					}
				},
				fail: (err) => {
					console.error('获取视频列表失败:', err);
					this.loading = false;
					
					// 显示错误提示
					this.$refs.uToast.show({
						title: '获取视频列表失败',
						type: 'error'
					});
					
					if (!isLoadMore) {
						this.videoList = [];
					}
				}
			});
		},
		
		// 播放视频
		playVideo(item) {
			// 准备视频数据
			const videoData = {
				id: item.id,
				url: item.liveUrl,
				name: item.tittle,
				videoThumbnailUrl: item.liveCover,
				author: '负责人',
				description: item.description
			};
			
			// 将视频数据转成字符串
			const videoDataStr = encodeURIComponent(JSON.stringify(videoData));
			
			// 将当前列表中的其他视频作为相关推荐传递
			const relatedVideos = this.videoList
				.filter(video => video.id !== item.id)
				.map(video => ({
					id: video.id,
					name: video.tittle,
					videoThumbnailUrl: video.liveCover,
					url: video.liveUrl,
					author: '负责人'
				}));
			
			// 将相关推荐数据转成字符串
			const relatedVideosStr = encodeURIComponent(JSON.stringify(relatedVideos));
			
			// 跳转到视频播放页面
			uni.navigateTo({
				url: `/pages/investment/ailive?videoData=${videoDataStr}&relatedVideos=${relatedVideosStr}`
			});
		},
		
		// 加载更多
		loadMore() {
			if (this.loading || !this.hasMore) return;
			
			this.currentPage++;
			this.getVideoList(true);
		}
	},
	
	// 下拉到底部加载更多
	onReachBottom() {
		this.loadMore();
	}
}
</script>

<style lang="scss" scoped>
.container {
}

.content {
	padding: 20rpx;
}

.video-list {
	display: flex;
	flex-direction: column;
}

.video-item {
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
}

.video-cover {
	position: relative;
	width: 100%;
	// height: 400rpx;
}

.cover-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.play-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
}

.video-info {
	padding: 20rpx;
}

.video-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.video-stats {
	display: flex;
	align-items: center;
}

.watch-count {
	font-size: 24rpx;
	color: #999;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.empty-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
}

.loading-text {
	font-size: 26rpx;
	color: #999;
	margin-top: 20rpx;
}

.video-popup {
	background-color: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	height: 100%;
	display: flex;
	flex-direction: column;
}

.video-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border-bottom: 1rpx solid #eee;
}

.video-popup-title {
	font-size: 30rpx;
	font-weight: bold;
	max-width: 80%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.video-player {
	flex: 1;
	width: 100%;
}
</style>