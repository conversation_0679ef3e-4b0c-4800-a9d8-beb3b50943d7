<template>
	<view>
		<u-popup :show="show" :round="10" mode="center" @close="handleClose">
			<view class="permission-popup">
				<view class="permission-title">权限申请</view>
				<view class="permission-content">
					<!-- <view class="permission-icon">
						<u-icon :name="permissionIcon" size="60" color="#00C1CC"></u-icon>
					</view> -->
					<!-- <view class="permission-text">
						<text>{{permissionTitle}}</text>
					</view> -->
					<view class="permission-desc">
						<text>{{permissionDesc}}</text>
					</view>
				</view>
				<view class="permission-buttons">
					<view class="btn cancel" @click="handleCancel">取消</view>
					<view class="btn confirm" @click="handleConfirm">确定</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	const permissionConfig = {
		'camera': {
			title: '相机权限',
			desc: '我们需要相机权限来帮助您拍摄和更新头像等图片',
			icon: 'camera-fill'
		},
		'album': {
			title: '相册权限',
			desc: '我们需要相册权限来帮助您选择和上传图片',
			icon: 'photo'
		},
		'location': {
			title: '位置权限',
			desc: '我们需要位置权限来为您提供基于位置的服务',
			icon: 'map'
		}
		// 可以继续添加其他权限配置
	}

	export default {
		name: 'permission-handler',
		data() {
			return {
				show: false,
				permissionType: '',
				permissionTitle: '',
				permissionDesc: '',
				permissionIcon: '',
				resolvePromise: null,
				rejectPromise: null
			}
		},
		methods: {
			// 请求权限的主方法
			async requestPermission(type) {
				if (!permissionConfig[type]) {
					console.error('未知的权限类型:', type);
					return Promise.reject('未知的权限类型');
				}

				// #ifdef APP-PLUS
				if(plus.os.name.toLowerCase() === 'android') {
					const main = plus.android.runtimeMainActivity();
					const PackageManager = plus.android.importClass('android.content.pm.PackageManager');
					let permission = '';
					
					switch(type) {
						case 'camera':
							permission = 'android.permission.CAMERA';
							break;
						case 'album':
							permission = 'android.permission.READ_EXTERNAL_STORAGE';
							break;
						case 'location':
							permission = 'android.permission.ACCESS_FINE_LOCATION';
							break;
					}
					
					// 先检查权限是否已经获取
					if(main.checkSelfPermission(permission) === PackageManager.PERMISSION_GRANTED) {
						return Promise.resolve(true);
					}
				}
				// #endif

				// 如果权限未获取，显示说明弹窗
				this.permissionType = type;
				this.permissionTitle = permissionConfig[type].title;
				this.permissionDesc = permissionConfig[type].desc;
				this.permissionIcon = permissionConfig[type].icon;
				this.show = true;

				// 返回Promise以便异步处理结果
				return new Promise((resolve, reject) => {
					this.resolvePromise = resolve;
					this.rejectPromise = reject;
				});
			},

			// 处理确认按钮点击
			async handleConfirm() {
				try {
					let authResult;
					switch (this.permissionType) {
						case 'camera':
							authResult = await this.requestCameraPermission();
							break;
						case 'album':
							authResult = await this.requestAlbumPermission();
							break;
						case 'location':
							authResult = await this.requestLocationPermission();
							break;
						default:
							throw new Error('未知的权限类型');
					}

					this.show = false;
					if (this.resolvePromise) {
						this.resolvePromise(authResult);
					}
				} catch (error) {
					if (this.rejectPromise) {
						this.rejectPromise(error);
					}
					uni.showToast({
						title: '权限获取失败',
						icon: 'none'
					});
				}
			},

			// 处理取消按钮点击
			handleCancel() {
				this.show = false;
				if (this.rejectPromise) {
					this.rejectPromise(new Error('用户拒绝授权'));
				}
			},

			// 处理弹窗关闭
			handleClose() {
				if (this.rejectPromise) {
					this.rejectPromise(new Error('用户取消操作'));
				}
			},

			// 请求相机权限
			async requestCameraPermission() {
				return new Promise((resolve, reject) => {
					// #ifdef APP-PLUS
					if(plus.os.name.toLowerCase() === 'android') {
						const main = plus.android.runtimeMainActivity();
						const Context = plus.android.importClass('android.content.Context');
						const PackageManager = plus.android.importClass('android.content.pm.PackageManager');
						const permission = 'android.permission.CAMERA';
						
						if(main.checkSelfPermission(permission) === PackageManager.PERMISSION_GRANTED) {
							resolve(true);
							return;
						}
						
						main.requestPermissions([permission], 1, new plus.android.implements('com.android.app.GrantedResult', {
							onGranted: function(permissions) {
								resolve(true);
							},
							onDenied: function(permissions) {
								if(main.shouldShowRequestPermissionRationale(permission)) {
									// 用户拒绝但没有勾选"不再询问"
									reject(new Error('用户拒绝相机权限'));
								} else {
									// 用户拒绝且勾选了"不再询问"
									uni.showModal({
										title: '提示',
										content: '相机权限被禁用，请前往系统设置开启',
										confirmText: '去设置',
										success: (res) => {
											if(res.confirm) {
												// 跳转到应用设置页面
												const Intent = plus.android.importClass('android.content.Intent');
												const Settings = plus.android.importClass('android.provider.Settings');
												const Uri = plus.android.importClass('android.net.Uri');
												const intent = new Intent();
												intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
												const uri = Uri.fromParts('package', main.getPackageName(), null);
												intent.setData(uri);
												main.startActivity(intent);
											}
											reject(new Error('用户拒绝相机权限'));
										}
									});
								}
							}
						}));
					} else {
						resolve(true); // iOS默认允许
					}
					// #endif

					// #ifdef MP
					uni.authorize({
						scope: 'scope.camera',
						success: () => resolve(true),
						fail: (err) => {
							console.error('相机权限请求失败:', err);
							if (err.errMsg.indexOf('auth deny') !== -1) {
								uni.showModal({
									title: '提示',
									content: '您已拒绝相机权限，是否去设置页面开启？',
									success: (res) => {
										if (res.confirm) {
											uni.openSetting({
												success: (settingRes) => {
													if (settingRes.authSetting['scope.camera']) {
														resolve(true);
													} else {
														reject(new Error('用户未授权相机权限'));
													}
												}
											});
										} else {
											reject(new Error('用户拒绝授权'));
										}
									}
								});
							} else {
								reject(err);
							}
						}
					});
					// #endif
				});
			},

			// 请求相册权限
			async requestAlbumPermission() {
				return new Promise((resolve, reject) => {
					// #ifdef APP-PLUS
					if(plus.os.name.toLowerCase() === 'android') {
						const main = plus.android.runtimeMainActivity();
						const Context = plus.android.importClass('android.content.Context');
						const PackageManager = plus.android.importClass('android.content.pm.PackageManager');
						const permission = 'android.permission.READ_EXTERNAL_STORAGE';
						
						if(main.checkSelfPermission(permission) === PackageManager.PERMISSION_GRANTED) {
							resolve(true);
							return;
						}
						
						main.requestPermissions([permission], 2, new plus.android.implements('com.android.app.GrantedResult', {
							onGranted: function(permissions) {
								resolve(true);
							},
							onDenied: function(permissions) {
								if(main.shouldShowRequestPermissionRationale(permission)) {
									// 用户拒绝但没有勾选"不再询问"
									reject(new Error('用户拒绝相册权限'));
								} else {
									// 用户拒绝且勾选了"不再询问"
									uni.showModal({
										title: '提示',
										content: '相册权限被禁用，请前往系统设置开启',
										confirmText: '去设置',
										success: (res) => {
											if(res.confirm) {
												// 跳转到应用设置页面
												const Intent = plus.android.importClass('android.content.Intent');
												const Settings = plus.android.importClass('android.provider.Settings');
												const Uri = plus.android.importClass('android.net.Uri');
												const intent = new Intent();
												intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
												const uri = Uri.fromParts('package', main.getPackageName(), null);
												intent.setData(uri);
												main.startActivity(intent);
											}
											reject(new Error('用户拒绝相册权限'));
										}
									});
								}
							}
						}));
					} else {
						resolve(true); // iOS默认允许
					}
					// #endif

					// #ifdef MP
					uni.authorize({
						scope: 'scope.album',
						success: () => resolve(true),
						fail: (err) => {
							console.error('相册权限请求失败:', err);
							if (err.errMsg.indexOf('auth deny') !== -1) {
								uni.showModal({
									title: '提示',
									content: '您已拒绝相册权限，是否去设置页面开启？',
									success: (res) => {
										if (res.confirm) {
											uni.openSetting({
												success: (settingRes) => {
													if (settingRes.authSetting['scope.album']) {
														resolve(true);
													} else {
														reject(new Error('用户未授权相册权限'));
													}
												}
											});
										} else {
											reject(new Error('用户拒绝授权'));
										}
									}
								});
							} else {
								reject(err);
							}
						}
					});
					// #endif
				});
			},

			// 请求位置权限
			async requestLocationPermission() {
				return new Promise((resolve, reject) => {
					// #ifdef APP-PLUS
					if(plus.os.name.toLowerCase() === 'android') {
						const main = plus.android.runtimeMainActivity();
						const Context = plus.android.importClass('android.content.Context');
						const PackageManager = plus.android.importClass('android.content.pm.PackageManager');
						const permission = 'android.permission.ACCESS_FINE_LOCATION';
						
						if(main.checkSelfPermission(permission) === PackageManager.PERMISSION_GRANTED) {
							resolve(true);
							return;
						}
						
						main.requestPermissions([permission], 3, new plus.android.implements('com.android.app.GrantedResult', {
							onGranted: function(permissions) {
								resolve(true);
							},
							onDenied: function(permissions) {
								if(main.shouldShowRequestPermissionRationale(permission)) {
									// 用户拒绝但没有勾选"不再询问"
									reject(new Error('用户拒绝位置权限'));
								} else {
									// 用户拒绝且勾选了"不再询问"
									uni.showModal({
										title: '提示',
										content: '位置权限被禁用，请前往系统设置开启',
										confirmText: '去设置',
										success: (res) => {
											if(res.confirm) {
												// 跳转到应用设置页面
												const Intent = plus.android.importClass('android.content.Intent');
												const Settings = plus.android.importClass('android.provider.Settings');
												const Uri = plus.android.importClass('android.net.Uri');
												const intent = new Intent();
												intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
												const uri = Uri.fromParts('package', main.getPackageName(), null);
												intent.setData(uri);
												main.startActivity(intent);
											}
											reject(new Error('用户拒绝位置权限'));
										}
									});
								}
							}
						}));
					} else {
						resolve(true); // iOS默认允许
					}
					// #endif

					// #ifdef MP
					uni.authorize({
						scope: 'scope.userLocation',
						success: () => resolve(true),
						fail: (err) => {
							console.error('位置权限请求失败:', err);
							if (err.errMsg.indexOf('auth deny') !== -1) {
								uni.showModal({
									title: '提示',
									content: '您已拒绝位置权限，是否去设置页面开启？',
									success: (res) => {
										if (res.confirm) {
											uni.openSetting({
												success: (settingRes) => {
													if (settingRes.authSetting['scope.userLocation']) {
														resolve(true);
													} else {
														reject(new Error('用户未授权位置权限'));
													}
												}
											});
										} else {
											reject(new Error('用户拒绝授权'));
										}
									}
								});
							} else {
								reject(err);
							}
						}
					});
					// #endif
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.permission-popup {
		width: 600rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 40rpx;

		.permission-title {
			font-size: 36rpx;
			font-weight: 500;
			color: #333333;
			text-align: center;
			margin-bottom: 30rpx;
		}

		.permission-content {
			.permission-icon {
				text-align: center;
				margin-bottom: 20rpx;
			}

			.permission-text {
				font-size: 32rpx;
				color: #333333;
				text-align: center;
				margin-bottom: 20rpx;
			}

			.permission-desc {
				font-size: 30rpx;
				line-height: 2.0;
				color: #666666;
				text-align: left;
				margin-bottom: 40rpx;
				padding: 0 20rpx;
			}
		}

		.permission-buttons {
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;

			.btn {
				width: 240rpx;
				height: 80rpx;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 32rpx;

				&.cancel {
					background: #F5F5F5;
					color: #666666;
				}

				&.confirm {
					background: #00C1CC;
					color: #FFFFFF;
				}
			}
		}
	}
</style> 