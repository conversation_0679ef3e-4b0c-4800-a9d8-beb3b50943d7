<template>
	<view style="position: relative;height: 100%;">
		<view v-if="loading">
			<x-skeleton type="banner" :loading="true">
				<view></view>
			</x-skeleton>
			<x-skeleton type="menu" :loading="true">
				<view></view>
			</x-skeleton>
			<x-skeleton type="waterfall" :loading="true" :configs="{
					gridColumns: 4,
					headHeight: '200rpx',
					textRows: 1,
					gridRows:1
				}">
				<view></view>
			</x-skeleton>
			<x-skeleton type="waterfall" :loading="true" :configs="{
					gridColumns: 1,
					headHeight: '200rpx',
					textRows: 1,
					gridRows:3,
					textShow:false
				}">
				<view></view>
			</x-skeleton>
			<x-skeleton type="list" :loading="true">
				<view></view>
			</x-skeleton>
		</view>
		<view style="padding:30upx 50upx;">
			<view style="font-size: 48upx;color: #fff;">
				常见问题
			</view>
			<view style="font-size:24upx ;color: #fff;margin: 30upx 0;">
				您想咨询的问题都在这里
			</view>
			<view style="padding-bottom: 120upx;">
				<view class="u-page__item">
					<u-collapse accordion :border='false'>
						<u-collapse-item v-for="(item, index) in protocolList" :key="index" :title="item.title">
							<text class="u-collapse-content" v-html="formatRichText(item.content)"></text>
						</u-collapse-item>
					</u-collapse>
				</view>
			</view>
		</view>
		<view @click="makePhone"
			style="background-color: #fff;height: 120upx;font-size: 28rpx;color: #3D3D3D;position:fixed;bottom: 0;width: 100%;"
			class="dis-ali jc_cen">
			<u-icon name="kefu-ermai" size="14px" class="mr5"></u-icon> 联系客服
		</view>
		<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
			:loading="loading"></u-loading-page>
		<!-- 加载页 -->
	</view>
</template>

<script>
	var that
	import {
		requestPermissions
	} from "@/app-permission.js"
	export default {
		data() {
			return {
				loading: true,
				protocolList: []
			}
		},
		onLoad() {
			that = this
			this.getProtocolList()
		},
		methods: {
			formatRichText(html) {
				let newContent = html.replace(/<img[^>]*>/gi, function(match, capture) {
					match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
					match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
					match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
					return match;
				});
				newContent = newContent.replace(/style="[^"]+"/gi, function(match, capture) {
					match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi,
						'max-width:100%;');
					return match;
				});
				newContent = newContent.replace(/<br[^>]*\/>/gi, '');
				newContent = newContent.replace(/\<img/gi, '<img style="max-width:99%;"');
				return newContent;
			},
			makePhone() {
				
				// #ifdef APP-PLUS
				// 先申请电话权限
				requestPermissions({
					title: "拨打电话权限申请说明",
					content: "便于您使用该功能直接拨打电话等场景中使用",
					permissionID: "CALL_PHONE"
				}).then(cameraResult => {
					console.log(cameraResult)
					if (!cameraResult.isSuc) {
						reject(new Error('未获得相机权限'));
						return;
					}
					// 拨打电话
					uni.makePhoneCall({
						phoneNumber: '400-6199-839',
						success: () => {
							console.log('拨打电话成功');
						},
						fail: (err) => {
							console.log('拨打电话失败:', err);
							// 拨打失败时也检查权限
							if (err.errMsg && err.errMsg.includes('denied')) {
								this.makePhone(); // 重新触发权限检查
							}
						}
					});
				}).catch(error => {
					reject(error);
				});
				// #endif
				// #ifdef H5
				uni.makePhoneCall({
					phoneNumber: '400-6199-839',
					success: () => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.log('拨打电话失败:', err);
						// 拨打失败时也检查权限
						if (err.errMsg && err.errMsg.includes('denied')) {
							this.makePhone(); // 重新触发权限检查
						}
					}
				});
				// #endif 
				// return;
				// if(uni.getSystemInfoSync().platform === 'android') {
				// 	// 检查是否有拨打电话权限
				// 	const main = plus.android.runtimeMainActivity();
				// 	const PackageManager = plus.android.importClass("android.content.pm.PackageManager");
				// 	const permission = 'android.permission.CALL_PHONE';

				// 	if(main.checkSelfPermission(permission) !== PackageManager.PERMISSION_GRANTED) {
				// 		// 没有权限,显示权限申请弹窗
				// 		uni.showModal({
				// 			title: '提示',
				// 			content: '需要获取拨打电话权限,是否去设置?',
				// 			success: (res) => {
				// 				if(res.confirm) {
				// 					// 跳转到应用设置页面
				// 					const Intent = plus.android.importClass('android.content.Intent');
				// 					const Settings = plus.android.importClass('android.provider.Settings');
				// 					const Uri = plus.android.importClass('android.net.Uri');
				// 					const intent = new Intent();
				// 					intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
				// 					const uri = Uri.fromParts('package', main.getPackageName(), null);
				// 					intent.setData(uri);
				// 					main.startActivity(intent);
				// 				}
				// 			}
				// 		});
				// 		return;
				// 	}
				// }



			},
			// 获取帮助列表
			async getProtocolList() {
				try {
					const res = await that.http.ajax({
						url: that.http.api.protocolgetByType,
						method: 'GET',
						data: {
							type: 2 // 传入type=2
						}
					})

					if (res.code === 0) {
						this.protocolList = res.data || []
					} else {
						this.showError(res.msg || '获取帮助列表失败')
					}
				} catch (err) {
					this.showError('获取帮助列表失败')
					console.error('获取帮助列表失败:', err)
				} finally {
					this.loading = false
				}
			},

			// 错误提示
			showError(message) {
				uni.showToast({
					title: message,
					icon: 'none'
				})
			}
		}
	}
</script>

<style>
	page,
	body {
		background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 30%;
		height: 100%;
	}
</style>

<style lang="scss" scoped>
	.u-page__item {
		background-color: #fff;
		border-radius: 20upx;
		overflow: hidden;

		.u-collapse-content {
			line-height: 2.0;
		}
	}

	image {
		vertical-align: middle !important;
	}
</style>