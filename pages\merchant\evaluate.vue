<template>
	<view class="container">
		<u-navbar :title="isViewMode ? '评价详情' : '评价'" height="44" :bgColor="bgColor" :placeholder='true' :auto-back="true" :border-bottom="false"></u-navbar>
		
		<view class="form-wrapper">
			<view class="rate-item">
				<view class="rate-label required">表现</view>
				<view class="rate-content">
					<!-- 查看模式下显示评价项 -->
					<view v-if="isViewMode">
						<view class="dis-ali" v-for="(item, index) in formData.offeringShows" :key="'v'+index">
							<text class="mr5 com-fontsize-26">{{ item.showLabel }}</text>
							<u-rate v-model="item.showScore" :count="5" active-color="#FFCC33" inactive-color="#E4E4E4" readonly></u-rate>
							<text class="rate-desc">{{ getRatingDesc(item.showScore) }}</text>
						</view>
					</view>
					
					<!-- 编辑模式下显示评价项 -->
					<view v-else>
						<view class="dis-ali" v-for="(item, index) in labelList" :key="'e'+index">
							<text class="mr5 com-fontsize-26">{{ item }}</text>
							<u-rate v-model="formData.offeringShows[index].showScore" :count="5" active-color="#FFCC33" inactive-color="#E4E4E4" @change="(value) => onRateChange(value, index, item)"></u-rate>
							<text class="rate-desc">{{ getRatingDesc(formData.offeringShows[index].showScore) }}</text>
						</view>
					</view>
				</view>
			</view>

			<view class="comment-area">
				<view class="comment-label">评价</view>
				<view class="comment-input">
					<u-textarea v-model="formData.reviewContent" placeholder="请输入评价" :maxlength="500" height="120" count :disabled="isViewMode"></u-textarea>
				</view>
				<view class="upload-area" v-if="!isViewMode">
					<view class="upload-info">
						<u-upload
							:file-list="fileList"
							@afterRead="afterRead"
							@delete="deletePic"
							name="pic"
							:max-count="1"
							:width="80"
							:height="80"
						></u-upload>
					</view>
					<view class="upload-tip">最多上传1张图片</view>
				</view>
				
				<!-- 查看模式下显示评价图片 -->
				<view class="review-image" v-if="isViewMode && formData.reviewImageUrl">
					<image :src="formData.reviewImageUrl" mode="aspectFill" @tap="previewImage" class="review-img"></image>
				</view>
			</view>

			
			
			<!-- 提交按钮 -->
			<view class="submit-btn-wrapper" v-if="!isViewMode">
				<view class="submit-btn" @tap="submitForm">提交评价</view>
			</view>
			
			<!-- 查看模式下的返回按钮 -->
			<view class="submit-btn-wrapper" v-else>
				<view class="back-btn" @tap="goBack">返回</view>
			</view>
		</view>
		
		<!-- 提示组件 -->
		<u-notify ref="uNotify"></u-notify>
	</view>
</template>

<script>
export default {
	data() {
		return {
			bgColor: '#fff',
			themeColor: '#2BCBD4',
			labelList: [], // 标签列表
			formData: {
				offeringRecordId: 0, // 从选项获取的ID
				reviewContent: '', // 评价内容
				reviewImageUrl: '', // 图片地址
				offeringShows: [] // 评分项
			},
			fileList: [],
			ratingOptions: [
				{ value: 1, desc: '1分' },
				{ value: 2, desc: '2分' },
				{ value: 3, desc: '3分' },
				{ value: 4, desc: '4分' },
				{ value: 5, desc: '5分' }
			],
			isViewMode: false, // 是否为查看模式
			reviewId: 0 // 评价ID，用于查看详情
		}
	},
	onLoad(option) {
		if (option) {
			// 检查是否是查看详情模式
			if (option.type === 'detail' && option.id) {
				this.isViewMode = true;
				this.reviewId = parseInt(option.id);
				// 获取评价详情
				this.getReviewDetail();
			} else if (option.id) {
				// 评价模式
				this.isViewMode = false;
				this.formData.offeringRecordId = parseInt(option.id);
				// 获取标签列表
				this.getLabelList();
			}
		}
	},
	methods: {
		// 获取评价详情
		async getReviewDetail() {
			try {
				uni.showLoading({
					title: '加载中...'
				});
				
				// 调用接口获取评价详情
				this.merhttp.ajax({
					url: this.merhttp.api.reviewreget,
					method: 'GET',
					data: { id: this.reviewId },
					success: (res) => {
						uni.hideLoading();
						if (res.code === 0 && res.data) {
							// 设置表单数据
							this.formData = {
								offeringRecordId: res.data.offeringRecordId,
								reviewContent: res.data.reviewContent,
								reviewImageUrl: res.data.reviewImageUrl,
								offeringShows: res.data.offeringShows || []
							};
							
							// 如果有图片，更新fileList
							if (res.data.reviewImageUrl) {
								this.fileList = [{
									url: res.data.reviewImageUrl,
									status: 'success',
									message: '图片'
								}];
							}
						} else {
							this.showErrorToast('获取评价详情失败');
						}
					},
					fail: (err) => {
						uni.hideLoading();
						console.error('获取评价详情失败:', err);
						this.showErrorToast('获取评价详情失败');
					}
				});
			} catch (error) {
				uni.hideLoading();
				console.error('获取评价详情失败:', error);
				this.showErrorToast('获取评价详情失败');
			}
		},
		
		// 获取标签列表
		async getLabelList() {
			try {
				uni.showLoading({
					title: '加载中...'
				});
				
				// 调用接口获取标签列表
				this.merhttp.ajax({
					url: this.merhttp.api.labelsimplelist,
					method: 'GET',
					success: (res) => {
						if (res.code === 0 && res.data && res.data.length > 0) {
							this.labelList = res.data;
							
							// 初始化评分项
							this.formData.offeringShows = this.labelList.map(label => {
								return {
									showLabel: label,
									showScore: 0
								}
							});
						} else {
							this.showErrorToast('获取评价标签失败');
						}
						uni.hideLoading();
					},
					fail: (err) => {
						console.error('获取评价标签出错:', err);
						this.showErrorToast('获取评价标签出错');
						uni.hideLoading();
					}
				});
			} catch (error) {
				console.error('获取评价标签出错:', error);
				this.showErrorToast('获取评价标签出错');
				uni.hideLoading();
			}
		},
		
		// 评分改变时触发
		onRateChange(value, index, label) {
			this.formData.offeringShows[index].showScore = value;
			this.formData.offeringShows[index].showLabel = label;
		},
		
		// 获取评分描述
		getRatingDesc(score) {
			if (!score) return '';
			const option = this.ratingOptions.find(item => item.value === score);
			return option ? option.desc : '';
		},
		
		// 图片上传后回调
		async afterRead(event) {
			const { file } = event;
			
			try {
				uni.showLoading({
					title: '上传中...'
				});
				
				// 上传图片
				const uploadRes = await this.http.upload({
					filePath: file.url
				});
				console.log('上传结果:', uploadRes);
				if (uploadRes && uploadRes.data) {
					// 存储图片地址
					this.formData.reviewImageUrl = uploadRes.data;
					
					// 更新显示列表
					this.fileList = [{
						url: file.url,
						status: 'success',
						message: '上传成功'
					}];
				} else {
					this.showErrorToast('图片上传失败');
				}
			} catch (error) {
				console.error('图片上传出错:', error);
				this.showErrorToast('图片上传出错');
			} finally {
				uni.hideLoading();
			}
		},
		
		// 删除图片
		deletePic() {
			this.fileList = [];
			this.formData.reviewImageUrl = '';
		},
		
		// 显示错误通知
		showErrorNotify(message) {
			if (this.$refs.uNotify) {
				this.$refs.uNotify.show({
					type: 'error',
					message: message,
					duration: 2000,
					safeAreaInsetTop: true,
					fontSize: 14,
					color: '#fff',
					bgColor: '#f56c6c'
				});
			}
		},
		
		// 显示错误Toast（作为备选通知方式）
		showErrorToast(message) {
			uni.showToast({
				title: message,
				icon: 'none',
				duration: 2000
			});
		},
		
		// 验证表单
		validateForm() {
			// 检查是否有评分为0的项
			const hasZeroScore = this.formData.offeringShows.some(item => item.showScore === 0);
			
			if (hasZeroScore) {
				this.showErrorToast('请为所有项目评分');
				return false;
			}
			
			if (!this.formData.reviewContent.trim()) {
				this.showErrorToast('请输入评价内容');
				return false;
			}
			
			return true;
		},
		
		// 提交表单
		async submitForm() {
			if (this.validateForm()) {
				try {
					uni.showLoading({
						title: '提交中...'
					});
					
					// 调用接口提交评价
					this.merhttp.ajax({
						url: this.merhttp.api.reviewcreate,
						method: 'POST',
						data: this.formData,
						success: (res) => {
							uni.hideLoading();
							if (res.code === 0) {
								uni.showToast({
									title: '评价成功',
									icon: 'success',
									success: () => {
										// 延迟返回上一页
										setTimeout(() => {
											uni.navigateBack();
										}, 1500);
									}
								});
							} else {
								this.showErrorToast(res.msg || '提交评价失败');
							}
						},
						fail: (err) => {
							uni.hideLoading();
							console.error('提交评价出错:', err);
							this.showErrorToast('提交评价出错');
						}
					});
				} catch (error) {
					uni.hideLoading();
					console.error('提交评价出错:', error);
					this.showErrorToast('提交评价出错');
				}
			}
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 预览图片
		previewImage() {
			if (this.formData.reviewImageUrl) {
				uni.previewImage({
					urls: [this.formData.reviewImageUrl],
					current: this.formData.reviewImageUrl
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.form-wrapper {
	// background-color: #fff;
	padding: 30rpx 30rpx;
	margin: 0rpx ;
}

.rate-item {
	padding: 30rpx ;
	background-color: #fff;
	border-radius: 20upx;
	// border-bottom: 1px solid #F2F2F2;
}

.rate-label {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 20rpx;
	font-weight: 500;
	
	&.required::before {
		content: '*';
		color: #f00;
		margin-right: 4rpx;
	}
}

.rate-content {
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	align-items: flex-start;
	height: auto;
	min-height: 200upx;
}

.dis-ali {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.rate-desc {
	margin-left: 20rpx;
	font-size: 26rpx;
	color: #999;
}

.comment-area {
	margin:30upx 0;
	padding: 30rpx;
	background-color: #fff;
	border-radius:20upx;
}

.comment-label {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 20rpx;
}

.comment-input {
	background-color: #F8F8F8;
	border-radius: 8rpx;
}

.upload-area {
	padding: 30rpx 0;
}

.upload-tip {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

// 提交按钮
.submit-btn-wrapper {
	padding: 40rpx 30rpx 20rpx 30rpx;
	
	.submit-btn {
		height: 90rpx;
		background: #2BCBD4;
		color: #fff;
		border-radius: 45rpx;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 10rpx 20rpx rgba(43, 203, 212, 0.3);
		
		&:active {
			opacity: 0.8;
			transform: scale(0.98);
		}
	}
}

.review-image {
	margin-top: 20rpx;
	display: flex;
	// justify-content: center;
	// align-items: center;
}

.review-img {
	width: 300upx;
	height: 300upx;
	max-height: 300rpx;
	border-radius: 10rpx;
}

.back-btn {
	height: 90rpx;
	background: #2BCBD4;
	color: #fff;
	border-radius: 45rpx;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 10rpx 20rpx rgba(43, 203, 212, 0.3);
	
	&:active {
		opacity: 0.8;
		transform: scale(0.98);
	}
}
</style>