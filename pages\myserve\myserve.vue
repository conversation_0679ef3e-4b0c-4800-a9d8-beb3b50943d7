<template>
	<view>
		<z-paging :show-scrollbar="false" refresher-background='#fffff00' ref="paging"  refresher-only :use-page-scroll='true' @onRefresh="onRefresh">
			<view slot="top">
				<u-navbar
					title="我的服务"
					@rightClick="showChildrenList"
					:autoBack="true"
					:placeholder="true"
					rightIcon='list-dot'
					:rightText="currentChildName || '选择孩子'"
				>
				</u-navbar>
			</view>
			
			<template #refresher="{refresherStatus}">
				<custom-refresher :status="refresherStatus" color="#000" />
			</template>
			
			<template #loadingMoreNoMore>
				<custom-nomore />
			</template>
			
			<view v-if="loading">
				<x-skeleton type="waterfall" :loading="true" :configs="{
					gridColumns: 1,
					headHeight: '200rpx',
					textRows: 2,
					gridRows: 8,
					textShow: false
				}">
					<view></view>
				</x-skeleton>
			</view>
			
			<view v-else>
				<view class="service-container">
					<view class="service-card" 
						v-for="(item, index) in serviceList" 
						:key="item.id"
						:style="{'backgroundImage':item.backgroundMember}"
						@click="goToServiceDetail(item.id)"
						hover-class="service-card-hover"
					>
						<view class="service-content">
							<view class="service-title">{{item.name }}</view>
							<view class="service-period">{{item.startDate}} - {{item.endDate}}</view>
						</view>
						<view class="service-indicator">
							<u-icon name="arrow-right" color="rgba(0,0,0,0.2)" size="28"></u-icon>
						</view>
					</view>
				</view>
				
				<u-empty
					v-if="!loading && (!serviceList || serviceList.length === 0)"
					text="暂无服务信息"
					mode="list"
					icon="../../static/empty/list.png"
				>
				</u-empty>
			</view>
			
			<u-toast ref="uToast"></u-toast>
			
			<u-action-sheet 
				:actions="childrenActionList" 
				:show="showActionSheet"
				@select="handleChildSelect"
				@close="showActionSheet=false"
				cancel-text="取消">
			</u-action-sheet>
		</z-paging>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				childrenList: [], // 孩子列表
				showActionSheet: false, // 控制动作面板显示
				childrenActionList: [], // 动作面板中的孩子列表选项
				currentChildName: '', // 当前选中的孩子名称
				serviceList: [], // 服务列表
			}
		},
		onLoad() {
			// 页面加载时获取孩子列表
			this.getChildrenList();
			this.getmyserveList();
		},
		methods: {
			getmyserveList(){
				this.loading = true;
				this.http.ajax({
					url: this.http.api.offeringlist,
					data: {
						page: 1,
						pagesize: 99
					},
					success: (res) => {
						this.loading = false;
						if (res.code == 0) {
							this.serviceList = res.data.list || [];
							// 处理服务数据
							this.serviceList.forEach(item => {
								// 处理背景图片URL格式
								if(item.backgroundMember && !item.backgroundMember.startsWith('url(')) {
									item.backgroundMember = `url(${item.backgroundMember})`;
								} else if(!item.backgroundMember) {
									// 如果没有背景图片，设置默认背景图
									item.backgroundMember = `url('/static/image/serve1.png')`;
								}
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
						
						// 完成下拉刷新
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					},
					fail: () => {
						this.loading = false;
						uni.showToast({
							title: '获取服务列表失败',
							icon: 'none'
						});
						
						// 完成下拉刷新
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					},
					complete: () => {
						// 确保在任何情况下都会执行complete
						this.loading = false;
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					}
				});
			},
			// 跳转到服务详情页
			goToServiceDetail(item) {
				uni.navigateTo({
					url: `/pages/myserve/serveDetail?id=${item}`
				});
			},
			
			// 显示孩子列表选择器
			showChildrenList() {
				// 如果还没有获取孩子列表，先获取
				if (this.childrenList.length === 0) {
					this.getChildrenList();
				} else {
					this.showActionSheet = true;
				}
			},
			
			// 处理孩子选择
			handleChildSelect(index) {
				console.log(index)
				// 获取选中的孩子
				const selectedChild = index;
				if (selectedChild && selectedChild.id) {
					// 如果已经是当前选中的孩子，不做处理
					if (selectedChild.isCurrent) {
						this.$u.toast('该孩子已经是当前选中的孩子');
						return;
					}
					// 切换选中的孩子
					this.switchChild(selectedChild.id);
				}
			},
			
			// 切换选中的孩子
			switchChild(id) {
				this.http.ajax({
					url: this.http.api.switchChild,
					method: 'POST',
					data: {
						id: id
					},
					success: (res) => {
						if (res.code == 0) {
							this.$u.toast('切换成功');
							// 重新获取孩子列表以更新状态
							this.getChildrenList();
							// 重新获取服务列表
							this.getmyserveList();
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					}
				});
			},
			
			// 获取孩子列表
			async getChildrenList() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.getChild,
						method: 'GET'
					});
					
					if (res.code === 0) {
						this.childrenList = res.data || [];
						
						// 设置动作面板的选项
						this.childrenActionList = this.childrenList.map(item => {
							return {
								name: item.childName,
								id: item.id,
								isCurrent:item.isCurrent,
								// 如果是当前选中的孩子，加上标记
								subname: item.isCurrent ? '(当前选中)' : '',
								color: item.isCurrent ? '#00C1CC' : ''
							};
						});
						
						// 获取当前选中的孩子
						const currentChild = this.childrenList.find(item => item.isCurrent);
						if (currentChild) {
							this.currentChildName = currentChild.childName;
						}
					}
				} catch (err) {
					console.error('获取孩子列表失败:', err);
				}
			},
			
			onRefresh() {
				this.getChildrenList();
				this.getmyserveList();
			}
		}
	}
</script>

<style>
	page, body {
		background-color: #f3f3f3;
	}
</style>

<style lang="scss" scoped>
	.service-container {
		width: 100%;
		padding: 30rpx;
	}
	
	.service-card {
		margin-bottom: 30rpx;
		border-radius: 16rpx;
		/* background-image: url('/static/image/serve1.png'); */
		background-size: 100% 100%;
		width: 100%;
		height: 250rpx;
		padding: 30rpx;
		padding-left: 230rpx;
		box-sizing: border-box;
		color: #000;
		position: relative;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		display: flex;
		justify-content: space-between;
		align-items: center;
		transition: all 0.2s;
	}
	
	.service-card-hover {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.service-content {
		flex: 1;
	}
	
	.service-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.service-period {
		font-size: 26rpx;
		
		opacity: 0.9;
	}
	
	.service-indicator {
		margin-left: 10upx;
		width: 40rpx;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		opacity: 0.6;
	}
</style>