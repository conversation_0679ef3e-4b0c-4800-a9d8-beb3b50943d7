<template>
	<view class="webview-page">
		<!-- 全屏网页容器 -->
		<web-view :src="webUrl" @message="handleMessage" class="webview-container"></web-view>
		
		<!-- 加载指示器 -->
		<view class="loading-container" v-if="loading">
			<u-loading-icon mode="circle" size="36"></u-loading-icon>
			<text class="loading-text">全景加载中...</text>
		</view>
		
		<!-- 错误提示 -->
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 20, // 默认值，会在onLoad中获取实际高度
			webUrl: '',
			title: '',
			loading: true
		}
	},
	onLoad(options) {
		// 获取传入的参数
		this.webUrl = decodeURIComponent(options.url || '');
		this.title = decodeURIComponent(options.title || '');
		uni.setNavigationBarTitle({
			title: this.title || ''
		});
		// 获取状态栏高度
		uni.getSystemInfo({
			success: (res) => {
				this.statusBarHeight = res.statusBarHeight || 0;
			}
		});
		
		// 设置加载超时
		setTimeout(() => {
			this.loading = false;
		}, 5000);
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 处理web-view消息
		handleMessage(event) {
			// 网页加载完成
			if (event.detail && event.detail.data && event.detail.data.loaded) {
				this.loading = false;
			}
			
			// 网页加载出错
			if (event.detail && event.detail.data && event.detail.data.error) {
				this.loading = false;
				this.$refs.uToast.show({
					title: '全景加载失败，请稍后再试',
					type: 'error'
				});
			}
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #000;
	height: 100vh;
	overflow: hidden;
}

.webview-page {
	position: relative;
	width: 100%;
	height: 100vh;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 99;
	background: linear-gradient(to bottom, rgba(0,0,0,0.8), rgba(0,0,0,0));
}

.navbar-content {
	position: relative;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 15px;
}

.navbar-left {
	width: 60upx;
	height: 60upx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navbar-title {
	font-size: 18px;
	font-weight: 500;
	color: #fff;
	max-width: 60%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.navbar-right {
	width: 60upx;
}

/* 全屏网页容器 */
.webview-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

/* 加载指示器 */
.loading-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 10;
}

.loading-text {
	font-size: 28upx;
	color: #fff;
	margin-top: 20upx;
}
</style>