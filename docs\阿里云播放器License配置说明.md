# 阿里云播放器License配置说明

## 问题背景

从2024年12月1日起，阿里云播放器新版本需要配置License才可以使用。如果不配置License，会出现以下错误：

```
播放失败，因为您的 License 校验未通过，具体原因： 未配置License
error_code: 4035
display_msg: '播放出错啦 (LICENSE ERROR)'
error_msg: '未配置License'
```

## License信息

### 当前License密钥
```
IPWsGiG4S01ssucgn8207ff3e34794cddbea8ce111a94c7b8
```

### License特点
- **免费申请**：播放器基础功能License免费
- **有效期**：1年
- **功能**：支持播放器基础功能

## 配置方法

### 1. 在播放器初始化时添加License

在所有使用阿里云播放器的页面中，需要在 `new Aliplayer()` 时添加 `licenseKey` 参数：

```javascript
this.player = new Aliplayer({
    id: 'player',
    vid: newValue.vid,
    playauth: newValue.playauth,
    region: 'cn-shanghai',
    width: '100%',
    height: '300px',
    autoplay: false,
    // 其他配置...
    
    // 添加License配置
    licenseKey: 'IPWsGiG4S01ssucgn8207ff3e34794cddbea8ce111a94c7b8'
});
```

### 2. 已更新的文件

以下播放器页面已添加License配置：

- ✅ `pages/hall/videoPlayerFixed.vue` - 推荐使用
- ✅ `pages/hall/videoPlayerStable.vue` - 稳定版本
- ✅ `pages/hall/videoPlayer.vue` - 完整版本

### 3. 错误处理优化

同时优化了License相关的错误处理：

```javascript
onPlayerError(error) {
    console.error('播放器错误:', error);
    
    // 处理License错误
    if (error && error.paramData) {
        const errorData = error.paramData;
        if (errorData.error_code === 4035) {
            this.showError('播放器License验证失败，请联系管理员');
        } else if (errorData.error_msg) {
            this.showError('播放失败：' + errorData.error_msg);
        } else {
            this.showError('视频播放失败，请重试');
        }
    } else {
        this.showError('视频播放失败，请重试');
    }
}
```

## 测试验证

### 1. 成功标志
配置License后，播放器应该能够：
- ✅ 正常初始化，无License错误
- ✅ 正确播放阿里云视频
- ✅ 显示播放控制界面
- ✅ 响应播放、暂停等操作

### 2. 错误排查
如果仍然出现License错误：

#### 2.1 检查License密钥
确认License密钥是否正确：
```javascript
licenseKey: 'IPWsGiG4S01ssucgn8207ff3e34794cddbea8ce111a94c7b8'
```

#### 2.2 检查播放器版本
确认使用的是支持License配置的播放器版本。

#### 2.3 检查网络连接
License验证需要网络连接，确保设备能正常访问阿里云服务。

### 3. 控制台日志
正常情况下，控制台应该显示：
```
初始化阿里云播放器...
阿里云播放器准备就绪
播放器准备完成
```

而不应该出现License相关的错误信息。

## License管理

### 1. License有效期
- **当前License有效期**：1年（从申请日期开始）
- **到期提醒**：建议在到期前1个月重新申请
- **续期方式**：通过阿里云控制台重新申请

### 2. License申请
如需重新申请License：
1. 访问：https://help.aliyun.com/zh/vod/developer-reference/license-authorization-and-management
2. 按照文档指引申请新的License
3. 更新代码中的 `licenseKey` 配置

### 3. License使用限制
- **域名限制**：License可能绑定特定域名
- **功能限制**：免费License支持基础播放功能
- **并发限制**：可能有并发播放数量限制

## 注意事项

### 1. 安全性
- License密钥包含在前端代码中，属于公开信息
- 阿里云通过其他方式（如域名、应用签名等）进行安全验证
- 不要将License密钥用于其他敏感操作

### 2. 兼容性
- License配置仅在新版本播放器中需要
- 旧版本播放器可能不支持此配置
- 建议使用最新版本的阿里云播放器SDK

### 3. 错误处理
- 添加了专门的License错误处理逻辑
- 用户看到的错误信息更加友好
- 便于开发者排查License相关问题

## 总结

通过添加License配置，解决了阿里云播放器的授权问题：

1. **问题解决**：消除了"未配置License"错误
2. **功能正常**：播放器能够正常初始化和播放
3. **错误处理**：优化了License相关的错误提示
4. **用户体验**：提供了更好的错误信息和处理流程

现在播放器应该能够正常工作，用户可以正常观看阿里云视频点播的内容。
