# License配置测试指南

## 快速测试步骤

### 1. 基本功能测试
1. **进入播放页面**
   - 打开课程详情页面
   - 点击任意课时的"去观看"按钮
   - 验证跳转到 `videoPlayerFixed.vue` 页面

2. **检查License配置**
   - 打开浏览器开发者工具
   - 查看Console标签
   - 应该看到：`初始化阿里云播放器...`
   - 不应该看到License相关错误

3. **播放器初始化**
   - 等待播放授权获取完成
   - 观察播放器容器是否正常显示
   - 检查是否出现播放控制界面

### 2. 错误检查

#### 2.1 成功标志 ✅
控制台应该显示：
```
开始获取播放授权，课时ID: xxx
获取播放授权返回: {...}
设置播放器配置: {...}
创建阿里云播放器，配置: {...}
初始化阿里云播放器...
阿里云播放器准备就绪
播放器准备完成
```

#### 2.2 License错误 ❌
如果仍然出现以下错误：
```
播放失败，因为您的 License 校验未通过，具体原因： 未配置License
error_code: 4035
```

**排查步骤：**
1. 检查License密钥是否正确配置
2. 确认播放器版本是否支持License配置
3. 检查网络连接是否正常

### 3. 播放测试

#### 3.1 播放控制测试
1. 点击播放按钮
2. 测试暂停功能
3. 测试音量调节
4. 测试进度条拖拽

#### 3.2 全屏测试
1. 点击全屏按钮
2. 验证全屏模式
3. 测试退出全屏

### 4. 错误处理测试

#### 4.1 网络错误测试
1. 断开网络连接
2. 尝试进入播放页面
3. 验证错误提示是否友好

#### 4.2 License错误测试
1. 临时移除License配置
2. 观察错误处理是否正确
3. 恢复License配置

## 预期结果

### ✅ 成功场景
- 播放器正常初始化
- 无License相关错误
- 视频能够正常播放
- 播放控制功能正常
- 错误提示用户友好

### ❌ 失败场景处理
- License错误有明确提示
- 网络错误有重试选项
- 播放失败有友好提示
- 控制台有详细错误日志

## 常见问题

### Q1: 仍然提示"未配置License"
**A1:** 检查以下几点：
- License密钥是否正确：`IPWsGiG4S01ssucgn8207ff3e34794cddbea8ce111a94c7b8`
- 播放器配置中是否包含 `licenseKey` 参数
- 播放器版本是否支持License配置

### Q2: 播放器初始化失败
**A2:** 可能的原因：
- 播放授权获取失败
- License验证失败
- 网络连接问题
- 播放器SDK加载失败

### Q3: 视频无法播放
**A3:** 排查步骤：
- 检查播放授权是否正确获取
- 确认视频ID和播放授权匹配
- 验证阿里云服务是否正常
- 检查播放器配置是否正确

## 调试技巧

### 1. 控制台日志
关键日志信息：
```javascript
// License配置成功
"初始化阿里云播放器..."
"阿里云播放器准备就绪"

// License配置失败
"播放失败，因为您的 License 校验未通过"
"error_code: 4035"
```

### 2. 网络请求
检查以下请求：
- `getVideoPlayAuth` 接口调用
- 阿里云License验证请求
- 视频资源加载请求

### 3. 播放器状态
监控播放器事件：
- `ready` - 播放器准备就绪
- `play` - 开始播放
- `pause` - 暂停播放
- `error` - 播放错误

## 性能监控

### 1. 加载时间
- 页面跳转时间
- 播放授权获取时间
- 播放器初始化时间
- 视频开始播放时间

### 2. 错误率
- License验证成功率
- 播放授权获取成功率
- 视频播放成功率
- 用户操作响应率

通过以上测试步骤，可以全面验证License配置是否正确，播放器功能是否正常。
