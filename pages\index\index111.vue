<template>
	<view>
		<z-paging :show-scrollbar="false" refresher-background='#fffff00' ref="paging" refresher-only
			@onRefresh="onRefresh" :use-page-scroll='true'>
			<view slot="top">
				<view class="index">
					<wu-app-update></wu-app-update>
				</view>
				<u-navbar title="" height="0" :bgColor="bgColor" :placeholder='true' left-icon="">
				</u-navbar>
				<my-nav title='勃学超市' :pageScrollTop='pageScrollTop'></my-nav>
				<!-- 头部nav -->
			</view>
			<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
			<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->

			<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

			<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
			<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
			<template #refresher="{refresherStatus}">
				<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
				<custom-refresher :status="refresherStatus" />
			</template>
			<!-- 自定义没有更多数据view -->
			<template #loadingMoreNoMore>
				<!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
				<custom-nomore />
			</template>
			<view v-if="loading" @click="goUrl(99)">
				<x-skeleton type="banner" :loading="true">
					<view></view>
				</x-skeleton>
				<x-skeleton type="menu" :loading="true">
					<view></view>
				</x-skeleton>
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 4,
						headHeight: '200rpx',
						textRows: 1,
						gridRows:1
					}">
					<view></view>
				</x-skeleton>
				<x-skeleton type="waterfall" :loading="true" :configs="{
						gridColumns: 1,
						headHeight: '200rpx',
						textRows: 1,
						gridRows:3,
						textShow:false
					}">
					<view></view>
				</x-skeleton>
				<x-skeleton type="list" :loading="true">
					<view></view>
				</x-skeleton>
			</view>
			<view v-if='!loading'>
				<view class="content">
					<view class="user-info">
						<view class="user-info__left dis-ali" v-if="childList.childName">
							<view>
								<image class="user-info__avatar" :src="childList.headPortrait" mode="aspectFill">
								</image>
							</view>
							<view class="user-info__text ml5">
								<view class="user-info__text--name">{{childList.childName}}</view>
								<view class="user-info__text--grade">{{childList.gradeTitle}}</view>
							</view>
						</view>
						<view v-else></view>
						<view class="user-info__sign" @click="goUrl(4)">签到</view>
					</view>
					<view style="position: relative;width: 100%;">
						<view ref="typingText" class="text_box_view">{{ displayedText }}</view>
						<image class="robot-image" src="../../static/kfindex.png" mode="heightFix"></image>
					</view>


					<view class="menu-wrapper">
						<view class="box_tab">
							<view class="menu-item " v-for="(item,index) in dataList" @click="goUrl(1,item.menuCode)">
								<image class="menu-item__icon" :src="item.menuIcon" mode=""></image>
								<view class="text-overflow" style="width: 100upx;">
									{{item.menuName}}
								</view>
							</view>
							<view class="menu-item--placeholder" v-for="item in 4"></view>
						</view>
					</view>

					<!-- 近期活动 -->
					<view class="title_nav" v-if="activityList.length > 0">
						<span>近期活动</span>
					</view>

					<view class="activity_banner" v-if="activityList.length > 0">
						<u-swiper
							:list="activityList"
							keyName="bannerUrl"
							:height="280"
							:autoplay="true"
							:interval="3000"
							:duration="500"
							:circular="true"
							bgColor="transparent"
							radius="30"
							:indicator="true"
							indicatorMode="dot"
							@click="clickSwiper"
						></u-swiper>
					</view>
					<!-- 近期分享 -->
					<view class="title_nav">
						<span>近期分享</span>
					</view>
					<view class="scroll-list">
						<template v-if="shareList.length > 0">
							<scroll-view :scroll-left='0' :scroll-x="true" class="scroll-view">
								<view class="scroll-content">
									<view class="scroll-item dis-ali" v-for="item in shareList">
										<image :src="item.bannerUrl" class="scroll-image" mode="aspectFill"></image>
										<view class="scroll-title">{{item.title}}</view>
									</view>
								</view>
							</scroll-view>
						</template>
					</view>
					<!-- 近期直播 -->
					<!-- <view class="title_nav">
						<span>近期直播</span>
						<view class="title_nav__more">更多直播</view>
					</view> -->

					<!-- <view class="live-list">
						<view class="live-item" v-for="item in 2" @click="goUrl(99)">
							<view>
								<image class="live-item__image" src="../../static/image/signBack.png" mode="aspectFill">
								</image>
							</view>
							<view class="live-item__content">
								<view class="live-item__title">直播标题</view>
								<view class="live-item__time">直播时间：2024/11/20 12:00:00</view>
								<view class="live-item__icons">
									<view class="menu-item" v-for="item in 2">
										<image class="menu-item__icon"
											src="../../static/1cc249a937fde3dc41171d7e9b610ea.png"></image>
										AI学习机
									</view>
								</view>
							</view>
						</view>
					</view> -->

					<!-- 成长计划 -->
					<view class="title_nav">
						<span>适合你的成长计划</span>
						<view class="title_nav__more">查看全部</view>
					</view>
					<template v-if="growthPlanList.length > 0">
						<view class="plan-banner" v-for="item in growthPlanList">
							<image :src="item.bannerUrl||'../../static/image/20210126173020cb11b0.png'" mode="aspectFill"></image>
						</view>
					</template>
					<!-- 学习列表 -->
					<view class="study_box">
						<u-tabs 
							:list="list1" 
							@click="changeTab" 
							lineHeight='0' 
							:activeStyle="{
								color: '#303133',
								fontWeight: 'bold',
								transform: 'scale(1.05)'
							}"
						></u-tabs>
						<template v-if="courseList && courseList.length > 0">
							<view class="study-item" v-for="item in courseList" :key="item.id" @click="goUrl(6, item.id)">
								<view>
									<image class="study-item__image" :src="item.picUrl" mode="aspectFill"></image>
								</view>
								<view class="study-item__content">
									<view class="study-item__title text-overflow">{{item.name}}</view>
									<view class="study-item__subtitle text-line-overflow">{{item.introduction}}</view>
									<view class="study-item__tag">{{item.categoryName}}</view>
								</view>
								<view class="study-item__detail">查看详情</view>
							</view>
						</template>
						<template v-else>
							<view style="padding: 40rpx 0;">
								<u-empty
								text='课程正在路上~'
									mode="list"
									icon="../../static/empty/list.png"
								></u-empty>
							</view>
						</template>
					</view>

					<!-- 组件部分 -->
					<u-toast ref="uToast"></u-toast>
					<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
						:loading="false"></u-loading-page>
					<my-bottom></my-bottom>
				</view>
			</view>
		</z-paging>

		<view>
			<drag-button :isDock="true" :existTabBar="true" @btnClick="btnClick" @btnTouchstart="btnTouchstart"
				@btnTouchend="btnTouchend" />
		</view>
		<!-- 机器人 -->
	</view>
</template>

<script>
	var that
	import dragButton from "@/components/drag-button/drag-button.vue";
	export default {
		components: {
			dragButton
		},
		data() {
			return {
				currentTab: 0,
				loading: true,
				status: 'loadmore',
				list1: [{
					name: '大家都在学',
					id: 0
				}],
				page: 1,
				fullText: '欢迎来到勃学超市，我是你的学习助手小勃',
				displayedText: '',
				typingInterval: null,
				typingIndex: 0,
				pageScrollTop: 0, // 页面滚动距离
				bgColor: 'rgba(255,255,255,0.01)',
				navtitle: '',
				height: 0,
				dataList: [],
				login: 0,
				activityList: [],
				growthPlanList: [],
				childList: [],
				shareList: [],
				currentCategoryId: 0,
				courseList: []
			}
		},
		onLoad() {
			that = this;
			this.initPage()
			this.getHomeMenu()
			this.getRecentActivity()
			this.getGrowthPlan()
			this.getShare()
			this.getGoodClass()
		},
		onPageScroll(e) {
			// console.log(e)
			this.pageScrollTop = Math.floor(e.scrollTop);
			// this.$refs.paging.updatePageScrollTop(e.scrollTop);
		},
		onReachBottom(e) {
			// console.log(e)
			// this.$refs.paging.pageReachBottom()
		},
		// onPullDownRefresh() {
		// 		console.log('refresh');
		// 		setTimeout(function () {
		// 			uni.stopPullDownRefresh();
		// 		}, 1000);
		// 	},
		onShow() {
			// this.getHomeMenu()
			this.getchild()
			this.startTypingEffect()
		},
		onHide() {
			this.clearTypingEffect()
		},
		beforeDestroy() {
			this.clearTypingEffect()
		},
		methods: {
			// 获取近期分享
			async getShare() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.recentshare,
						method: 'GET',
						data: {
							pageSize: 10
						}
					})
					if (res.code === 0) {
						this.shareList = res.data.list || []
					}
				} catch (err) {
					console.error('获取近期活动分享:', err)
				}
			},
			// 获取孩子
			getchild() {
				that.http.ajax({
					url: that.http.api.getChild,
					success: (res) => {
						console.log(res)
						if (res.code == 0) {
							this.childList = res.data[0]
							console.log(this.childList)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			// 获取近期活动
			async getRecentActivity() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.recentactivity,
						method: 'GET',
						data: {
							pageSize: 3
						}
					})
					if (res.code === 0) {
						this.activityList = res.data.list || []
					}
				} catch (err) {
					console.error('获取近期活动失败:', err)
				}
			},

			// 获取成长计划
			async getGrowthPlan() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.loadGrowPlanList,
						method: 'GET',
						data: {
							pageSize: 1,
							isQueryGrowPlan: true
						}
					})
					if (res.code === 0) {
						this.growthPlanList = res.data.list || []
					}
				} catch (err) {
					console.error('获取成长计划失败:', err)
				}
			},
			initPage() {
				setTimeout(() => {
					this.loading = false
				}, 3000)
			},
			async getHomeMenu() {
				const res = await this.http.ajax({
					url: this.http.api.homemenu
				})
				console.log(res)
				if (res) {
					if (res == undefined || res.code !== 0) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						return
					}
					this.dataList = res.data
				}


				// this.handleMenuData(res.data.dtos)
				// this.getGoodClass()
				this.loading = false
			},
			handleMenuData(data) {
				const classifiedData = {
					type0: [],
					type2: [],
					type3: []
				}

				data.forEach(item => {
					const type = `type${item.indexMark}`
					if (classifiedData[type]) {
						classifiedData[type].push(item)
					}
				})

				this.dataList = classifiedData
			},
			startTypingEffect() {
				this.typingInterval = setInterval(() => {
					if (this.typingIndex < this.fullText.length) {
						this.displayedText += this.fullText.charAt(this.typingIndex++)
					} else {
						this.clearTypingEffect()
					}
				}, 100)
			},
			clearTypingEffect() {
				if (this.typingInterval) {
					clearInterval(this.typingInterval)
				}
			},
			handleNavigation(type, value) {
				const routes = {
					1: `/pages/detail/detail?id=${value}`,
					2: this.getApplyPath(),
					4: '/pages/clockIn/clockIn',
					6: '/pages/courseDetail/courseDetail',
					7: `/pages/detail/detail?id=${uni.getStorageSync('config').agreement_about}&type=1`,
					8: '/pages/loan/loan',
					99: '/pages/detail/detail'
				}

				if (routes[type]) {
					uni.navigateTo({
						url: routes[type],
						...(type === 1 && {
							animationType: 'pop-in',
							animationDuration: 3000
						})
					})
				}
			},
			getApplyPath() {
				const detail = uni.getStorageSync('info')
				return detail.is_auth === 1 ? '/pages/apply/apply_three' : '/pages/apply/apply'
			},
			onRefresh() {
				// 告知z-paging下拉刷新结束，这样才可以开始下一次的下拉刷新
				setTimeout(() => {
					// 1.5秒之后停止刷新动画
					this.$refs.paging.complete();
				}, 1500)
			},
			btnClick(e) {
				uni.navigateTo({
					url: '/pages/helpList/helpList'
				})
			},
			btnTouchstart(e) {
				console.log(e)
			},
			btnTouchend(e) {
				console.log(e)
			},
			changeTab(item) {
				this.currentCategoryId = item.id
				this.getCourseList(item.id)
			},
			goUrl(type, value) {
				this.handleNavigation(type, value)
			},
			gosign() {

				uni.navigateTo({
					url: '/pages/sign/sign'
				})
			},
			async getGoodClass() {
				try {
					const res = await this.http.ajax({
						url: this.http.api.categoryhomepage,
						method: 'GET'
					})
					
					if(res.code === 0) {
						const categories = res.data || []
						this.list1 = [
							{
								name: '大家都在学',
								id: 0
							},
							...categories.map(item => ({
								name: item.name,
								id: item.id
							}))
						]
						
						this.getCourseList(0)
					}
				} catch(err) {
					console.error('获取课程分类失败:', err)
					console.error('返回数据:', res?.data)
				}
			},
			async getCourseList(categoryId) {
				try {
					if(categoryId === 0) {
						const res = await this.http.ajax({
							url: this.http.api.spurecommend,
							method: 'GET'
						})
						
						if(res.code === 0) {
							this.courseList = res.data.list || []
						}
					} else {
						const res = await this.http.ajax({
							url: this.http.api.spupage,
							method: 'GET',
							data: {
								categoryId: categoryId,
								pageSize: 10,
								pageNo: 1
							}
						})
						
						if(res.code === 0) {
							this.courseList = res.data.list || []
						}
					}
				} catch(err) {
					console.error('获取课程列表失败:', err)
				}
			},
			gotoDetail(id) {
				uni.navigateTo({
					url: '/pages/detail/detail?id=' + id + "&type=1"
				})
			},
			clickSwiper(index) {
				const item = this.activityList[index]
				// 根据需要处理点击事件，比如跳转到详情页
				if(item.url) {
						uni.navigateTo({
							url: item.url
						})
				}
			}
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #f3f3f3;
		background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 60%;
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>
	.content {
		padding: 33rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.user-info {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;

		&__left {
			display: flex;
			align-items: center;
		}

		&__avatar {
			width: 84rpx;
			height: 84rpx;
		}

		&__text {
			color: #fff;
			margin-left: 20rpx;

			&--name {
				font-size: 30rpx;
			}

			&--grade {
				font-size: 24rpx;
			}
		}

		&__sign {
			color: #fff;
			font-size: 26rpx;
			z-index: 1;
		}
	}
	
	.robot-image {
		height: 402rpx;
		position: absolute;
		right: 40rpx;
		top: -40upx;
		// bottom: 100rpx;
		z-index: 1;
	}

	.menu-item {
		font-size: 26rpx;
		flex-direction: column;
		width: 120rpx;
		display: flex;
		align-items: center;
		margin-top: 30rpx;

		&__icon {
			width: 76rpx;
			height: 76rpx;
			margin-bottom: 15rpx;
		}
	}

	.live-item {
		width: 100%;
		margin-bottom: 30rpx;
		display: flex;

		&__image {
			width: 220rpx;
			height: 292rpx;
			border-radius: 25rpx;
		}

		&__content {
			flex: 1;
			margin-left: 20rpx;
			height: 280rpx;
			padding: 20rpx 0 0;
			display: flex;
			flex-direction: column;
		}

		&__title {
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 10rpx;
		}

		&__time {
			font-size: 24rpx;
			margin-bottom: 20rpx;
		}

		&__icons {
			display: flex;
			align-items: center;
		}
	}

	.plan-banner {
		width: 100%;
		height: 280rpx;
		border-radius: 30rpx;
		overflow: hidden;

		image {
			width: 700rpx;
			height: 280rpx;
		}
	}

	.study-item {
		width: 100%;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;

		&__image {
			width: 175rpx;
			height: 175rpx;
			border-radius: 25rpx;
		}

		&__content {
			flex: 1;
			margin-left: 20rpx;
			height: 175rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}

		&__title {
			width: 300upx;
			font-size: 32rpx;
			font-weight: bold;
		}

		&__subtitle {
			font-size: 24rpx;
			margin-bottom: 20rpx;
		}

		&__tag {
			border: 1rpx solid #6C6C6C;
			width: fit-content;
			font-size: 22upx;
			padding: 4rpx 20rpx;
			border-radius: 4rpx;
			text-align: center;
		}

		&__detail {
			width: 140rpx;
			height: 70rpx;
			background: #D8D8D8;
			border-radius: 90rpx;
			font-size: 24rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.title_nav {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 32rpx;
		font-weight: 700;
		margin: 40rpx 0;

		&__more {
			font-size: 24rpx;
			color: #6C6C6C;
			font-weight: 350;
		}
	}

	.box_tab {
		width: 100%;
		background-color: #fbfcfd;
		justify-content: space-around;
		padding-bottom: 30upx;
		border-radius: 30upx;
		flex-wrap: wrap;
		padding: 30upx 30upx 30upx 30upx;
		position: relative;
		z-index: 5;
	}

	// 标题栏
	.study_box {
		width: 100%;
		justify-content: space-around;
		padding-bottom: 30upx;
		border-radius: 30upx;
		flex-wrap: wrap;
		flex-direction: column;
		background-color: #fff;
		padding: 20upx;
		margin-top: 30upx;
	}

	.activity_banner {
		width: 100%;
		height: 280rpx;
		border-radius: 30rpx;
		overflow: hidden;
		margin-bottom: 20rpx;

		/deep/ .u-swiper {
			border-radius: 30rpx;
			overflow: hidden;
		}

		/deep/ .u-swiper-image {
			width: 100%;
			height: 100%;
			border-radius: 30rpx;
		}
	}

	.text_box_view {
		margin: 30upx;
		width: 426rpx;
		min-height: 125rpx;
		max-height: 200upx;
		border-radius: 30rpx 30rpx 30rpx 30rpx;
		border: 1rpx solid #FFFFFF;
		padding: 20upx;
		color: #fff;
		font-size: 24upx;
		align-self: start;
		line-height: 1.5;
		overflow: auto;
	}

	.menu-wrapper {
		position: relative;
		width: 100%;
	}

	.menu-item {
		&--placeholder {
			width: 120rpx;
		}
	}

	.live-list {
		width: 100%;
		justify-content: space-around;
		border-radius: 30rpx;
		flex-wrap: wrap;
		flex-direction: column;
	}

	.live-item {
		&__icons {
			display: flex;
			align-items: center;
		}
	}

	.study-item {
		margin-top: 20upx;
		margin-bottom: 0;
		&__subtitle {
			font-size: 24rpx;
			margin-bottom: 20rpx;
		}
	}
	.box_list {
		width: 100%;
		background-color: #fff;
		justify-content: space-around;
		padding: 30upx 0;
		border-radius: 20upx;
		flex-wrap: wrap;
				height: 100%;
		// padding: 30upx 30upx 0 30upx;
	}

	.scroll-list {
		background-color: #f5f5f5;
		padding: 0;
		width: 100%;
	}

	.scroll-view {
		width: 100%;
		white-space: nowrap;
	}

	.scroll-content {
		display: inline-flex;
		padding: 0rpx 0rpx;
	}

	.scroll-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-right: 10rpx;
		
		&:last-child {
			margin-right: 0rpx;
		}
	}

	.scroll-image {
		width: 210rpx;
		height: 292rpx;
		margin-bottom: 15rpx;
		border-radius: 10rpx;
	}

	.scroll-title {
		font-size: 26rpx;
		color: #333;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		width: 220rpx;
	}
</style>
</style>