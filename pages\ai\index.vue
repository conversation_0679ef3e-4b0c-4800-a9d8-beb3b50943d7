<template>
	<view class="container">
		<view class="header">
			<view class="back-btn" @click="goBack">
				<u-icon name="arrow-left" color="#fff" size="20"></u-icon>
			</view>
			<view class="title">AI智能对话</view>
			<view class="status" :class="{ 'connected': isConnected }">
				{{ isConnected ? '已连接' : '未连接' }}
			</view>
		</view>
		
		<scroll-view 
			scroll-y="true" 
			class="chat-container" 
			:scroll-top="scrollTop"
			:scroll-with-animation="true"
			@scrolltolower="onScrollToLower"
			id="chatScroll"
		>
			<view class="welcome-message" v-if="chatMessages.length === 0">
				<image class="welcome-avatar" src="/static/images/ai-avatar.png" mode="aspectFill"></image>
				<view class="welcome-text">
					<text class="welcome-title">您好，我是您的智能助手</text>
					<text class="welcome-desc">我可以回答您的问题，或者您可以上传音频与我交流</text>
				</view>
			</view>
			
			<view 
				v-for="(message, index) in chatMessages" 
				:key="index" 
				class="message-item"
				:class="{ 'user-message': message.isUser, 'ai-message': !message.isUser }"
			>
				<view class="message-avatar" v-if="!message.isUser">
					<image src="/static/images/ai-avatar.png" mode="aspectFill"></image>
				</view>
				<view class="message-content">
					<text class="message-text">{{ message.content }}</text>
					<text class="message-time">{{ message.time }}</text>
				</view>
				<view class="message-avatar user-avatar" v-if="message.isUser">
					<image src="/static/images/user-avatar.png" mode="aspectFill"></image>
				</view>
			</view>
			
			<view class="loading" v-if="isResponseLoading">
				<view class="loading-dots">
					<view class="dot"></view>
					<view class="dot"></view>
					<view class="dot"></view>
				</view>
			</view>
		</scroll-view>
		
		<view class="audio-player" v-if="audioSources.length > 0">
			<view class="audio-controls">
				<view class="audio-btn" @click="togglePlay">
					<u-icon :name="isPlaying ? 'pause' : 'play-right'" color="#007AFF" size="28"></u-icon>
				</view>
				<view class="audio-progress">
					<slider 
						:value="audioProgress" 
						@change="onProgressChange" 
						activeColor="#007AFF" 
						block-size="12"
					/>
				</view>
				<text class="audio-time">{{ formatAudioTime(audioDuration) }}</text>
			</view>
		</view>
		
		<view class="input-container">
			<view class="audio-upload">
				<u-upload
					:fileList="uploadFileList"
					@afterRead="afterRead"
					@delete="deleteFile"
					:maxCount="1"
					:customBtn="true"
					accept="audio/*"
					:multiple="false"
				>
					<view slot="addBtn" class="upload-btn" :class="{'has-file': hasUploadFile}">
						<u-icon :name="hasUploadFile ? 'checkmark-circle' : 'folder-add'" :color="hasUploadFile ? '#07c160' : '#666'" size="24"></u-icon>
						<text v-if="hasUploadFile" class="upload-text">已选择音频</text>
					</view>
				</u-upload>
			</view>
			
			<view class="audio-record" @click="toggleRecording">
				<view class="record-btn" :class="{ 'recording': isRecording }">
					<u-icon :name="isRecording ? 'stop-circle' : 'mic'" :color="isRecording ? '#ff4d4f' : '#666'" size="24"></u-icon>
					<text class="record-status">{{ isRecording ? '录音中...' : '录音' }}</text>
				</view>
			</view>
			
			<view class="input-box">
				<textarea 
					class="input-textarea" 
					v-model="inputMessage" 
					placeholder="请输入您的问题..."
					:disabled="isResponseLoading"
					:auto-height="true"
					:show-confirm-bar="false"
					@confirm="sendMessage"
				></textarea>
			</view>
			<view class="send-btn" @click="sendMessage" :class="{ 'disabled': !inputMessage.trim() && !hasUploadFile && !hasRecordFile }">
				<u-icon name="arrow-upward" color="#fff" size="24"></u-icon>
			</view>
		</view>
		
		<!-- 录音组件 -->
		<mumu-recorder ref="recorder" @success="handleRecordSuccess" @error="handleRecordError"></mumu-recorder>
	</view>
</template>

<script>
import MumuRecorder from '@/uni_modules/mumu-recorder/components/mumu-recorder/mumu-recorder.vue';

const MAX_CHUNK_SIZE = 1024;
const WS_URL = 'wss://manage.boxuehao.cn/websocket/agent/291/1';

export default {
	components: {
		MumuRecorder
	},
	data() {
		return {
			wsInstance: null,
			isConnected: false,
			isResponseLoading: false,
			inputMessage: '',
			chatMessages: [],
			scrollTop: 0,
			audioContext: null,
			audioSources: [],
			audioPlayer: null,
			currentTrackIndex: 0,
			isPlaying: false,
			audioDuration: 0,
			audioProgress: 0,
			uploadFileList: [],
			hasUploadFile: false,
			audioPlayTimer: null,
			// 录音相关
			isRecording: false,
			recordData: null,
			hasRecordFile: false
		}
	},
	onLoad() {
		this.connectWebSocket();
		// 创建音频上下文
		this.audioContext = uni.createInnerAudioContext();
		this.audioContext.onEnded(() => {
			this.playNextTrack();
		});
		this.audioContext.onTimeUpdate(() => {
			if (this.audioContext.duration > 0) {
				this.audioProgress = (this.audioContext.currentTime / this.audioContext.duration) * 100;
				this.audioDuration = this.audioContext.duration;
			}
		});
	},
	onUnload() {
		this.closeWebSocket();
		if (this.audioContext) {
			this.audioContext.destroy();
		}
		if (this.audioPlayTimer) {
			clearInterval(this.audioPlayTimer);
		}
	},
	methods: {
		connectWebSocket() {
			this.wsInstance = uni.connectSocket({
				url: WS_URL,
				complete: () => {}
			});
			
			uni.onSocketOpen(() => {
				this.isConnected = true;
				console.log('WebSocket连接已建立');
			});
			
			uni.onSocketError((error) => {
				this.isConnected = false;
				console.error('WebSocket连接错误:', error);
				uni.showToast({
					title: 'WebSocket连接失败',
					icon: 'none'
				});
			});
			
			uni.onSocketClose(() => {
				this.isConnected = false;
				console.log('WebSocket连接已关闭');
			});
			
			uni.onSocketMessage((res) => {
				this.handleSocketMessage(res.data);
			});
		},
		
		closeWebSocket() {
			if (this.wsInstance && this.isConnected) {
				uni.closeSocket();
			}
		},
		
		handleSocketMessage(data) {
			try {
				// 判断数据类型
				if (typeof data === 'string') {
					// 处理文本消息
					this.isResponseLoading = false;
					
					// 添加AI回复消息
					this.chatMessages.push({
						content: data,
						isUser: false,
						time: this.formatTime(new Date())
					});
					
					this.scrollToBottom();
				} else {
					// 检查是否为结束标记 [255, 255]
					const bytes = new Uint8Array(data);
					if (data.byteLength === 2 && bytes[0] === 255 && bytes[1] === 255) {
						// 收到结束标记，表示AI回复结束
						this.isResponseLoading = false;
						console.log('AI回复结束');
					} else {
						// 处理音频数据
						this.processAudioData(data);
					}
				}
			} catch (error) {
				console.error('处理WebSocket消息时出错:', error);
				this.isResponseLoading = false;
			}
		},
		
		processAudioData(data) {
			try {
				// 将二进制数据转为 blob
				const audioBlob = new Blob([data], { type: 'audio/wav' });
				const audioUrl = URL.createObjectURL(audioBlob);
				
				// 添加到音频源列表
				this.audioSources.push(audioUrl);
				
				// 如果未播放，则开始播放
				if (!this.isPlaying) {
					this.playAudio(this.audioSources[this.currentTrackIndex]);
				}
			} catch (error) {
				console.error('处理音频数据时出错:', error);
			}
		},
		
		toggleRecording() {
			if (this.isRecording) {
				// 停止录音
				this.$refs.recorder.stop();
				this.isRecording = false;
			} else {
				// 开始录音
				this.$refs.recorder.start();
				this.isRecording = true;
				
				// 显示录音提示
				uni.showToast({
					title: '开始录音',
					icon: 'none'
				});
			}
		},
		
		handleRecordSuccess(res) {
			console.log('录音成功:', res);
			this.recordData = res;
			this.hasRecordFile = true;
			
			// 显示成功提示
			uni.showToast({
				title: `录音完成 (${res.duration}s)`,
				icon: 'success'
			});
		},
		
		handleRecordError(code) {
			console.error('录音错误, 代码:', code);
			this.isRecording = false;
			
			switch (code) {
				case '100':
					uni.showModal({
						content: '请在HTTPS环境中使用录音功能'
					});
					break;
				case '101':
					uni.showModal({
						content: '当前浏览器不支持录音功能，请更换浏览器'
					});
					break;
				case '201':
					uni.showModal({
						content: '麦克风权限被拒绝，请允许使用麦克风'
					});
					break;
				default:
					uni.showModal({
						content: '录音失败，请重试'
					});
					break;
			}
		},
		
		sendMessage() {
			if (!this.inputMessage.trim() && !this.hasUploadFile && !this.hasRecordFile) return;
			
			if (!this.isConnected) {
				uni.showToast({
					title: 'WebSocket未连接，请稍后再试',
					icon: 'none'
				});
				return;
			}
			
			// 发送文本消息
			if (this.inputMessage.trim()) {
				// 添加用户消息到聊天记录
				this.chatMessages.push({
					content: this.inputMessage,
					isUser: true,
					time: this.formatTime(new Date())
				});
				
				// 发送消息到WebSocket
				this.sendTextMessage(this.inputMessage);
				
				// 清空输入框
				this.inputMessage = '';
			}
			
			// 发送上传的音频文件
			if (this.hasUploadFile && this.uploadFileList.length > 0) {
				this.sendAudioFile(this.uploadFileList[0].url);
				
				// 添加用户音频消息到聊天记录
				this.chatMessages.push({
					content: '[上传的音频]',
					isUser: true,
					time: this.formatTime(new Date())
				});
				
				// 清空文件列表
				this.uploadFileList = [];
				this.hasUploadFile = false;
			}
			
			// 发送录制的音频文件
			if (this.hasRecordFile && this.recordData) {
				// 添加用户音频消息到聊天记录
				this.chatMessages.push({
					content: `[录音 ${this.recordData.duration}s]`,
					isUser: true,
					time: this.formatTime(new Date())
				});
				
				// 发送录音文件
				this.sendRecordedAudio(this.recordData.data);
				
				// 清空录音数据
				this.recordData = null;
				this.hasRecordFile = false;
			}
			
			this.isResponseLoading = true;
			this.scrollToBottom();
		},
		
		sendTextMessage(message) {
			// 创建 TextEncoder 实例
			const encoder = new TextEncoder();
			const uint8Array = encoder.encode(message);
			
			// 发送文本消息
			uni.sendSocketMessage({
				data: uint8Array.buffer,
				fail: (error) => {
					console.error('发送消息失败:', error);
				}
			});
			
			// 发送结束标记
			const byteArray = new Uint8Array([255, 255]);
			setTimeout(() => {
				uni.sendSocketMessage({
					data: byteArray.buffer,
					fail: (error) => {
						console.error('发送结束标记失败:', error);
					}
				});
			}, 100);
		},
		
		async sendAudioFile(filePath) {
			try {
				// 显示加载提示
				uni.showLoading({
					title: '处理音频中...'
				});
				
				// 读取文件
				let fileData;
				try {
					fileData = await this.readFile(filePath);
					if (!fileData) {
						throw new Error('无法读取文件数据');
					}
				} catch (readError) {
					console.error('文件读取失败，尝试直接使用文件路径:', readError);
					
					// 如果是H5环境，尝试直接使用File对象
					if (typeof window !== 'undefined' && typeof FormData !== 'undefined') {
						if (filePath instanceof File) {
							// 如果是文件对象，使用FileReader
							fileData = await new Promise((resolve, reject) => {
								const reader = new FileReader();
								reader.onload = e => resolve(e.target.result);
								reader.onerror = reject;
								reader.readAsArrayBuffer(filePath);
							});
						} else if (typeof filePath === 'string' && (filePath.indexOf('blob:') === 0 || filePath.indexOf('http') === 0)) {
							// 如果是URL，使用fetch
							const response = await fetch(filePath);
							fileData = await response.arrayBuffer();
						} else {
							throw new Error('无法处理文件类型');
						}
					} else {
						throw readError; // 非H5环境直接抛出原始错误
					}
				}
				
				const byteArray = new Uint8Array(fileData);
				
				console.log('音频文件大小:', byteArray.byteLength, '字节');
				
				// 分块发送
				let offset = 0;
				while (offset < byteArray.byteLength) {
					const end = Math.min(offset + MAX_CHUNK_SIZE, byteArray.byteLength);
					const chunk = byteArray.slice(offset, end);
					
					await new Promise((resolve) => {
						uni.sendSocketMessage({
							data: chunk.buffer,
							success: resolve,
							fail: (error) => {
								console.error('发送音频块失败:', error);
								resolve(); // 即使失败也继续
							}
						});
						
						setTimeout(resolve, 50); // 给一个小延迟，避免发送过快
					});
					
					offset = end;
				}
				
				// 发送结束标记
				const endMarker = new Uint8Array([255, 255]);
				uni.sendSocketMessage({
					data: endMarker.buffer,
					fail: (error) => {
						console.error('发送结束标记失败:', error);
					},
					complete: () => {
						uni.hideLoading();
					}
				});
			} catch (error) {
				console.error('处理音频文件失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: '处理音频文件失败',
					icon: 'none'
				});
			}
		},
		
		readFile(filePath) {
			return new Promise((resolve, reject) => {
				try {
					// 检查运行环境
					const isH5 = typeof window !== 'undefined' && typeof document !== 'undefined';
					
					// H5环境直接使用fetch API
					if (isH5) {
						console.log('当前在H5环境中，使用fetch API处理文件');
						// 检查是否是本地文件还是远程URL
						if (filePath.indexOf('blob:') === 0 || filePath.indexOf('http') === 0) {
							fetch(filePath)
								.then(response => response.arrayBuffer())
								.then(buffer => resolve(buffer))
								.catch(err => {
									console.error('通过fetch获取文件失败:', err);
									reject(err);
								});
						} else if (filePath instanceof File || filePath instanceof Blob) {
							// 如果是文件对象，使用FileReader
							const reader = new FileReader();
							reader.onload = function(e) {
								resolve(e.target.result);
							};
							reader.onerror = function(error) {
								console.error('FileReader读取失败:', error);
								reject(error);
							};
							reader.readAsArrayBuffer(filePath);
						} else {
							// 尝试通过XHR获取文件
							const xhr = new XMLHttpRequest();
							xhr.open('GET', filePath, true);
							xhr.responseType = 'arraybuffer';
							xhr.onload = function() {
								if (this.status === 200) {
									resolve(this.response);
								} else {
									reject(new Error('XHR获取文件失败，状态码: ' + this.status));
								}
							};
							xhr.onerror = function(error) {
								console.error('XHR获取文件失败:', error);
								reject(error);
							};
							xhr.send();
						}
						return; // 提前返回，避免执行下面的原生API代码
					}
					
					// 非H5环境，检查文件路径类型
					if (filePath.indexOf('blob:') === 0 || filePath.indexOf('http') === 0) {
						// 如果是blob URL或远程URL，先下载文件
						uni.downloadFile({
							url: filePath,
							success: (res) => {
								if (res.statusCode === 200) {
									// 下载成功后读取文件
									try {
										// 先检查是否支持文件系统API
										if (typeof uni.getFileSystemManager === 'function') {
											uni.getFileSystemManager().readFile({
												filePath: res.tempFilePath,
												success: (readRes) => {
													resolve(readRes.data);
												},
												fail: (error) => {
													console.error('读取文件失败:', error);
													reject(error);
												}
											});
										} else {
											console.error('当前环境不支持getFileSystemManager');
											reject(new Error('当前环境不支持getFileSystemManager'));
										}
									} catch (fsError) {
										console.error('文件系统操作错误:', fsError);
										reject(fsError);
									}
								} else {
									reject(new Error('下载文件失败: ' + res.statusCode));
								}
							},
							fail: (error) => {
								console.error('下载文件失败:', error);
								reject(error);
							}
						});
					} else {
						// 直接读取本地文件
						try {
							if (typeof uni.getFileSystemManager === 'function') {
								uni.getFileSystemManager().readFile({
									filePath,
									success: (res) => {
										resolve(res.data);
									},
									fail: (error) => {
										console.error('读取本地文件失败:', error);
										reject(error);
									}
								});
							} else {
								console.error('当前环境不支持getFileSystemManager');
								reject(new Error('当前环境不支持getFileSystemManager'));
							}
						} catch (fsError) {
							console.error('文件操作错误:', fsError);
							reject(fsError);
						}
					}
				} catch (error) {
					console.error('文件处理过程中出错:', error);
					reject(error);
				}
			});
		},
		
		afterRead(event) {
			console.log('上传文件:', event);
			try {
				const files = [].concat(event.file);
				if (!files || files.length === 0 || !files[0]) {
					uni.showToast({
						title: '文件上传失败',
						icon: 'none'
					});
					return;
				}
				
				// 记录文件详细信息以便调试
				const fileInfo = {
					name: files[0].name || '未命名',
					size: files[0].size || 0,
					type: files[0].type || '未知类型',
					url: files[0].url || '',
					isFile: files[0] instanceof File,
					isBlob: files[0] instanceof Blob
				};
				console.log('文件详细信息:', fileInfo);
				
				// 获取文件名和扩展名
				const fileName = files[0].name || '';
				const fileExt = fileName.split('.').pop().toLowerCase();
				
				// 检查文件类型或扩展名
				const isAudioFile = 
					files[0].type === 'audio/mpeg' || 
					files[0].type === 'audio/mp3' || 
					fileExt === 'mp3' ||
					/\.(mp3|wav|m4a)$/i.test(fileName);
					
				if (!isAudioFile) {
					console.log('文件类型检查：', {
						fileName,
						fileExt,
						type: files[0].type
					});
					
					// 用更宽松的检查，只要不是明确的非音频文件就接受
					if (files[0].type && !files[0].type.includes('audio') && 
						!['mp3', 'wav', 'm4a', 'audio'].includes(fileExt)) {
						uni.showToast({
							title: '请选择音频文件',
							icon: 'none'
						});
						return;
					}
				}
				
				this.uploadFileList = files;
				this.hasUploadFile = true;
				
				// 显示成功提示
				uni.showToast({
					title: '音频已选择',
					icon: 'success'
				});
			} catch (error) {
				console.error('文件上传处理失败:', error);
				uni.showToast({
					title: '文件处理失败',
					icon: 'none'
				});
			}
		},
		
		deleteFile() {
			this.uploadFileList = [];
			this.hasUploadFile = false;
		},
		
		playAudio(audioUrl) {
			if (!this.audioContext) return;
			
			this.audioContext.src = audioUrl;
			this.audioContext.play();
			this.isPlaying = true;
		},
		
		playNextTrack() {
			this.currentTrackIndex++;
			if (this.currentTrackIndex < this.audioSources.length) {
				this.playAudio(this.audioSources[this.currentTrackIndex]);
			} else {
				// 播放完毕
				this.isPlaying = false;
				this.currentTrackIndex = 0;
				this.audioProgress = 0;
			}
		},
		
		togglePlay() {
			if (!this.audioContext) return;
			
			if (this.isPlaying) {
				this.audioContext.pause();
				this.isPlaying = false;
			} else {
				this.audioContext.play();
				this.isPlaying = true;
			}
		},
		
		onProgressChange(e) {
			if (!this.audioContext || !this.audioContext.duration) return;
			
			const position = e.detail.value / 100 * this.audioContext.duration;
			this.audioContext.seek(position);
		},
		
		formatAudioTime(seconds) {
			if (!seconds) return '00:00';
			const min = Math.floor(seconds / 60);
			const sec = Math.floor(seconds % 60);
			return `${min < 10 ? '0' + min : min}:${sec < 10 ? '0' + sec : sec}`;
		},
		
		formatTime(date) {
			const hour = date.getHours();
			const minute = date.getMinutes();
			return `${hour < 10 ? '0' + hour : hour}:${minute < 10 ? '0' + minute : minute}`;
		},
		
		scrollToBottom() {
			this.$nextTick(() => {
				const query = uni.createSelectorQuery().in(this);
				query.select('#chatScroll').boundingClientRect((res) => {
					if (res) {
						this.scrollTop = res.height * 100; // 设置一个较大的值确保滚动到底部
					}
				}).exec();
			});
		},
		
		onScrollToLower() {
			// 滚动到底部事件，可以在这里处理加载更多历史消息
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		sendRecordedAudio(blob) {
			try {
				// 显示加载提示
				uni.showLoading({
					title: '处理录音中...'
				});
				
				// 读取Blob数据并分块发送
				blob.arrayBuffer().then(buffer => {
					const byteArray = new Uint8Array(buffer);
					console.log('录音文件大小:', byteArray.byteLength, '字节');
					
					// 分块发送
					let offset = 0;
					const sendNextChunk = () => {
						if (offset >= byteArray.byteLength) {
							// 发送完毕，发送结束标记
							const endMarker = new Uint8Array([255, 255]);
							uni.sendSocketMessage({
								data: endMarker.buffer,
								complete: () => {
									uni.hideLoading();
								}
							});
							return;
						}
						
						const end = Math.min(offset + MAX_CHUNK_SIZE, byteArray.byteLength);
						const chunk = byteArray.slice(offset, end);
						
						uni.sendSocketMessage({
							data: chunk.buffer,
							success: () => {
								offset = end;
								setTimeout(sendNextChunk, 50); // 小延迟避免发送过快
							},
							fail: (error) => {
								console.error('发送录音块失败:', error);
								offset = end;
								setTimeout(sendNextChunk, 50); // 即使失败也继续
							}
						});
					};
					
					// 开始发送第一块
					sendNextChunk();
				}).catch(error => {
					console.error('读取录音数据失败:', error);
					uni.hideLoading();
					uni.showToast({
						title: '处理录音失败',
						icon: 'none'
					});
				});
			} catch (error) {
				console.error('处理录音文件失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: '处理录音失败',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #f8f9ff;
}

.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	
	.header {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 90rpx;
		background: linear-gradient(135deg, #007AFF, #00b4ff);
		padding: 20rpx 30rpx;
		color: #fff;
		
		.back-btn {
			position: absolute;
			left: 30rpx;
			top: 50%;
			transform: translateY(-50%);
			z-index: 2;
			padding: 10rpx;
		}
		
		.title {
			font-size: 36rpx;
			font-weight: 600;
		}
		
		.status {
			position: absolute;
			right: 30rpx;
			top: 50%;
			transform: translateY(-50%);
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.7);
			background-color: rgba(0, 0, 0, 0.2);
			padding: 6rpx 16rpx;
			border-radius: 30rpx;
			
			&.connected {
				background-color: rgba(39, 174, 96, 0.3);
			}
		}
	}
	
	.chat-container {
		flex: 1;
		padding: 30rpx;
		overflow-y: auto;
		
		.welcome-message {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 60rpx 40rpx;
			
			.welcome-avatar {
				width: 120rpx;
				height: 120rpx;
				border-radius: 60rpx;
				margin-bottom: 30rpx;
			}
			
			.welcome-text {
				text-align: center;
				
				.welcome-title {
					display: block;
					font-size: 36rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 16rpx;
				}
				
				.welcome-desc {
					display: block;
					font-size: 28rpx;
					color: #666;
					line-height: 1.5;
				}
			}
		}
		
		.message-item {
			display: flex;
			margin-bottom: 40rpx;
			
			&.user-message {
				flex-direction: row-reverse;
				
				.message-content {
					background: linear-gradient(135deg, #007AFF, #00b4ff);
					border-top-right-radius: 6rpx;
					border-top-left-radius: 30rpx;
					border-bottom-left-radius: 30rpx;
					border-bottom-right-radius: 30rpx;
					margin-right: 20rpx;
					
					.message-text {
						color: #fff;
					}
					
					.message-time {
						color: rgba(255, 255, 255, 0.7);
						text-align: right;
					}
				}
			}
			
			&.ai-message {
				.message-content {
					background: #fff;
					border-top-right-radius: 30rpx;
					border-top-left-radius: 6rpx;
					border-bottom-left-radius: 30rpx;
					border-bottom-right-radius: 30rpx;
					margin-left: 20rpx;
					
					.message-time {
						color: #999;
					}
				}
			}
			
			.message-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 40rpx;
				overflow: hidden;
				flex-shrink: 0;
				
				image {
					width: 100%;
					height: 100%;
				}
			}
			
			.message-content {
				max-width: 70%;
				padding: 20rpx 30rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
				
				.message-text {
					font-size: 28rpx;
					color: #333;
					line-height: 1.5;
					word-break: break-word;
				}
				
				.message-time {
					font-size: 20rpx;
					margin-top: 10rpx;
				}
			}
		}
		
		.loading {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 20rpx;
			
			.loading-dots {
				display: flex;
				
				.dot {
					width: 16rpx;
					height: 16rpx;
					border-radius: 50%;
					background: #007AFF;
					margin: 0 6rpx;
					animation: bounce 1.4s infinite ease-in-out both;
					
					&:nth-child(1) {
						animation-delay: -0.32s;
					}
					
					&:nth-child(2) {
						animation-delay: -0.16s;
					}
				}
			}
		}
	}
	
	.audio-player {
		background: #fff;
		padding: 20rpx 30rpx;
		border-top: 1px solid #eeeeee;
		
		.audio-controls {
			display: flex;
			align-items: center;
			
			.audio-btn {
				flex-shrink: 0;
				margin-right: 20rpx;
			}
			
			.audio-progress {
				flex: 1;
			}
			
			.audio-time {
				font-size: 24rpx;
				color: #999;
				margin-left: 20rpx;
				width: 80rpx;
				text-align: right;
			}
		}
	}
	
	.input-container {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background: #fff;
		border-top: 1px solid #eeeeee;
		
		.audio-upload, .audio-record {
			margin-right: 16rpx;
			
			.upload-btn, .record-btn {
				padding: 20rpx;
				border-radius: 10rpx;
				background: #f2f2f2;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s;
				
				&.has-file {
					background: rgba(7, 193, 96, 0.1);
					border: 1px dashed #07c160;
				}
				
				&.recording {
					background: rgba(255, 77, 79, 0.1);
					border: 1px dashed #ff4d4f;
					animation: pulse 1.5s infinite;
				}
			}
			
			.upload-text, .record-status {
				margin-left: 10rpx;
				font-size: 24rpx;
				color: #666;
			}
		}
		
		.input-box {
			flex: 1;
			background: #f2f2f2;
			border-radius: 36rpx;
			padding: 16rpx 30rpx;
			max-height: 200rpx;
			overflow-y: auto;
			
			.input-textarea {
				width: 100%;
				min-height: 40rpx;
				font-size: 28rpx;
				color: #333;
				line-height: 1.5;
			}
		}
		
		.send-btn {
			width: 80rpx;
			height: 80rpx;
			border-radius: 40rpx;
			background: linear-gradient(135deg, #007AFF, #00b4ff);
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 20rpx;
			
			&.disabled {
				background: #cccccc;
			}
		}
	}
}

@keyframes pulse {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.05);
	}
	100% {
		transform: scale(1);
	}
}

@keyframes bounce {
	0%, 80%, 100% {
		transform: scale(0);
	}
	40% {
		transform: scale(1);
	}
}
</style> 