<template>
	<view>
		<!-- 加载页面 -->
		<u-loading-page :loading="pageLoading" bg-color="rgba(255, 255, 255, 0.9)"></u-loading-page>
		
		<!-- 顶部导航栏 -->
		<u-sticky>
			<u-navbar :title="topNavStyle.title" :height="topNavStyle.height" :titleStyle='topNavStyle.Tstyle'
				:left-icon-color="topNavStyle.Lstyle" :bgColor="topNavStyle.style" :auto-back="false"
				:placeholder='true' left-icon="" style="z-index: 1;margin-top: -88upx;">
			</u-navbar>
		</u-sticky>

		<!-- 主要内容区域 -->
		<view class="main-container">
			<!-- 用户信息 -->
			<view class="user-info-section" @click="goUrl('sz')">
				<view class="user-info__header">
					<view class="user-info__left">
						<view class="avatar-wrapper">
							<image class="avatar" :src="userInfo && userInfo.headUrl ? userInfo.headUrl : '../../static/images/default-avatar.png'"
								mode="aspectFill"></image>
						</view>
						<view class="user-info__content" >
							<view class="user-name">{{userInfo && userInfo.nickName ? userInfo.nickName : '用户'}}</view>
							<view class="user-phone">
								<text v-if="userInfo && userInfo.levelName">{{userInfo.levelName}}</text>
							</view>
						</view>
					</view>
					<view>
						<u-icon name="arrow-right" color="#fff" size="24" style="padding-right: 10rpx;"></u-icon>
					</view>
				</view>
			</view>

			<!-- 功能菜单列表 -->
			<view class="menu-list">
				<!-- 合作申请表 -->
				<view class="menu-item" @click="goUrl('hezuo')">
					<view class="menu-icon">
						<u-icon name="file-text" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">合作申请表</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>
				
				<!-- 参会押金 -->
				<view class="menu-item" @click="goUrl('yajin')">
					<view class="menu-icon">
						<u-icon name="rmb-circle" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">参会押金</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>
				
				<!-- 官方合作付款账号 -->
				<view class="menu-item" @click="goUrl('zhanghao')">
					<view class="menu-icon">
						<u-icon name="account" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">官方合作付款账号</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>
			</view>

			<!-- 第二组菜单 -->
			<view class="menu-list">
				<!-- 我的项目 -->
				<view class="menu-item" @click="goUrl('project')">
					<view class="menu-icon">
						<u-icon name="grid" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">我的项目</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>
				
				
				
				<!-- 投诉中心 -->
				<view class="menu-item" @click="goUrl('complaint')">
					<view class="menu-icon">
						<u-icon name="bell" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">投诉中心</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>
				<!-- 设置 -->
				<view class="menu-item" @click="goUrl('sz')">
					<view class="menu-icon">
						<u-icon name="setting" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">设置</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>
			</view>

			<!-- 第三组菜单 -->
			<view class="menu-list" v-if="userInfo&&userInfo.type==1">
				<!-- 我的直播 -->
				<view class="menu-item" @click="goUrl('live')">
					<view class="menu-icon">
						<u-icon name="play-right" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">我的直播</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>

				<!-- 正在直播统计 -->
				<view class="menu-item" @click="goUrl('liveStat')">
					<view class="menu-icon">
						<u-icon name="chat" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">正在直播统计</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>

				<!-- 历史直播统计 -->
				<view class="menu-item" @click="goUrl('historyLive')">
					<view class="menu-icon">
						<u-icon name="calendar" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">历史直播统计</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>
			</view>

			<!-- Toast测试菜单 -->
			<!-- <view class="menu-list">
				<view class="menu-item" @click="testToast('default')">
					<view class="menu-icon">
						<u-icon name="info-circle" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">测试默认Toast</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>

				<view class="menu-item" @click="testToast('success')">
					<view class="menu-icon">
						<u-icon name="checkmark-circle" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">测试成功Toast</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>

				<view class="menu-item" @click="testToast('error')">
					<view class="menu-icon">
						<u-icon name="close-circle" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">测试错误Toast</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>

				<view class="menu-item" @click="testToast('loading')">
					<view class="menu-icon">
						<u-icon name="reload" color="#666" size="24"></u-icon>
					</view>
					<view class="menu-text">测试加载Toast</view>
					<view class="menu-arrow">
						<u-icon name="arrow-right" color="#c0c0c0" size="18"></u-icon>
					</view>
				</view>
			</view> -->
			
		
		</view>
		
		<!-- 底部组件 -->
		<my-bottom></my-bottom>
		<!-- 添加底部间距，防止被tabbar遮挡 -->
		<view style="height: 50rpx;"></view>
		<!-- Toast提示 -->
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
	import myBottom from "@/components/my-bottom/my-bottom.vue"

	export default {
		components: {
			myBottom
		},
		
		data() {
			return {
				pageLoading: true, // 控制加载页面显示
				userInfo: null,
				scrollTop: 0,
			}
		},

		computed: {
			topNavStyle() {
				let r = this.scrollTop / 100
				return {
					style: `rgba(255,255,255,${r>=1?1:r})`,
					Tstyle: `color:${r>1?'#000':'#fff'}`,
					Lstyle: r > 1 ? '#000' : '#fff',
					title: r > 1 ? '我的' : '',
					height: r > 1 ? '44' : '44',
				}
			}
		},

		// 生命周期方法
		onLoad() {
			uni.setNavigationBarTitle({
				title: '个人中心'
			});
			
			// 获取用户信息
			// this.getUserInfo();
		},

		onShow() {
			// 刷新用户信息
			this.getUserInfo();
		},

		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},

		methods: {
			// 获取用户信息
			getUserInfo() {
				// 先从缓存获取
				// try {
				// 	const syguser = uni.getStorageSync('syguser');
				// 	if (syguser) {
				// 		this.userInfo = JSON.parse(syguser);
				// 		this.pageLoading = false; // 关闭加载页
				// 		return;
				// 	}
				// } catch (e) {
				// 	console.error('获取缓存用户信息失败:', e);
				// }
				
				// 缓存不存在则调用接口获取
				this.syghttp.ajax({
					url: this.syghttp.api.myMemberInfo,
					method: 'POST',
					data: {},
					success: (res) => {
						if (res.code === 1000 && res.data) {
							this.userInfo = res.data;
							
							// 存储用户信息到缓存
							try {
								uni.setStorageSync('syguser', JSON.stringify(res.data));
							} catch (e) {
								console.error('存储用户信息失败:', e);
							}
						} else {
							this.showToast(res.msg || '获取用户信息失败', 'error');
						}
						this.pageLoading = false; // 关闭加载页
					},
					fail: (err) => {
						this.showToast('获取用户信息失败', 'error');
						console.error('获取用户信息失败:', err);
						this.pageLoading = false; // 关闭加载页
					}
				});
			},

			// 导航方法
			goUrl(type) {
				const routes = {
					'hezuo': '/pages/investment/application',
					'yajin': '/pages/investment/deposit',
					'zhanghao': '/pages/investment/account',
					'project': '/pages/investment/projectList',
					'sz': '/pages/setting/setting',
					'complaint': '/pages/setting/complaints',
					'live': '/pages/live/live-pusher',
					'liveStat': '/pages/investment/liveStatistics',
					'historyLive': '/pages/investment/historyLiveStatistics',
				}

				const route = routes[type];
				if (!route) return;

				// 特殊处理直播页面
				if (type === 'live') {
					this.handleLiveNavigation();
					return;
				}

				// 处理普通路由
				uni.navigateTo({
					url: route
				});
			},

			// 处理直播页面导航
			async handleLiveNavigation() {
				try {
					// 显示加载提示
					uni.showLoading({
						title: '检查直播间状态...',
						mask: true
					});

					// 1. 先检查直播间状态
					const statusRes = await this.syghttp.ajax({
						url: this.syghttp.api.selectMyPlayLive,
						method: 'GET'
					});

					uni.hideLoading();

					if (statusRes.code !== 1000) {
						this.showToast(statusRes.msg || '获取直播间状态失败', 'error');
						return;
					}

					const playLiveStatus = statusRes.data?.playLiveStatus;
					console.log(playLiveStatus)
					// 2. 检查直播间状态
					if (playLiveStatus !== 0) {
						this.showLiveStatusDialog(playLiveStatus);
						return;
					}

					// 3. 状态正常，获取推流地址
					await this.getLivePushUrl();

				} catch (error) {
					uni.hideLoading();
					console.error('处理直播导航失败:', error);
					this.showToast('检查直播间状态失败，请重试', 'error');
				}
			},

			// 显示直播状态提示弹窗
			showLiveStatusDialog(status) {
				let title = '直播间状态提示';
				let content = '';

				switch (status) {
					case 1:
						content = '当前直播间正在直播中，请等待直播结束后再试';
						break;
					case 2:
						content = '直播间暂时不可用，请稍后重试';
						break;
					default:
						content = '当前直播间状态异常，请重新确认直播间状态';
				}

				// 使用uni.showModal显示精美弹窗
				uni.showModal({
					title: title,
					content: content,
					showCancel: true,
					cancelText: '取消',
					confirmText: '重新检查',
					confirmColor: '#FFA500',
					success: (res) => {
						if (res.confirm) {
							// 用户选择重新检查，再次调用检查逻辑
							this.handleLiveNavigation();
						}
					}
				});
			},

			// 获取推流地址
			async getLivePushUrl() {
				try {
					// 检查用户信息中的liveId
					if (!this.userInfo || !this.userInfo.liveId) {
						this.showToast('开启失败，请联系平台', 'error');
						return;
					}

					uni.showLoading({
						title: '获取推流地址...',
						mask: true
					});

					// 调用获取推流地址接口
					const pushRes = await this.syghttp.ajax({
						url: this.syghttp.api.getLivePush,
						method: 'GET',
						data: {
							dataId: this.userInfo.liveId
						}
					});

					uni.hideLoading();

					if (pushRes.code !== 1000) {
						this.showToast(pushRes.msg || '获取推流地址失败', 'error');
						return;
					}

					// 推流地址直接在data字段中
					const pushUrl = pushRes.data;
					if (!pushUrl || typeof pushUrl !== 'string') {
						this.showToast('推流地址为空，请联系管理员', 'error');
						return;
					}

					// 跳转到直播推流页面
					uni.navigateTo({
						url: `/pages/live/live-pusher?liveId=${this.userInfo.liveId}&pushUrl=${encodeURIComponent(pushUrl)}`
					});

				} catch (error) {
					uni.hideLoading();
					console.error('获取推流地址失败:', error);
					this.showToast('获取推流地址失败，请重试', 'error');
				}
			},

			// 显示Toast提示
			showToast(message, type = 'default') {
				if (this.$refs.uToast) {
					this.$refs.uToast.show({
						type: type,
						message: message,
						duration: 2000
					});
				} else {
					uni.showToast({
						title: message,
						icon: type === 'success' ? 'success' : 'none'
					});
				}
			},

			// 测试Toast功能
			testToast(type) {
				const toastConfig = {
					default: {
						type: 'default',
						message: '锦瑟无端五十弦'
					},
					success: {
						type: 'success',
						message: '庄生晓梦迷蝴蝶'
					},
					error: {
						type: 'error',
						message: '一弦一柱思华年'
					},
					loading: {
						type: 'loading',
						message: '正在加载'
					}
				};

				const config = toastConfig[type];
				if (config && this.$refs.uToast) {
					this.$refs.uToast.show(config);
				}
			}
		}
	}
</script>

<style lang="scss">
	$primary-color: #FFA500;

	page,
	body {
		background-color: #f5f5f5;
		background-image: linear-gradient(180deg, $primary-color, #f5f5f5);
		background-size: 100% 70%;
		height: 100%;
		padding-bottom: 100rpx;  /* 为tabbar留出空间 */
	}
</style>
<style lang="scss" scoped>
	// 基础变量
	$text-white: #fff;
	$text-gray: #6C6C6C;
	$bg-white: #fff;
	$border-radius: 20upx;
	$primary-color: #FFA500;

	// 主容器
	.main-container {
		padding: 30upx 20upx;
		width: 100%;
		box-sizing: border-box;
	}

	// 用户信息区域
	.user-info-section {
		padding: 20upx 0 20upx;
		
		.user-info__header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			margin-bottom: 20upx;

			.user-info__left {
				display: flex;
				align-items: center;

				.avatar {
					width: 100upx;
					height: 100upx;
					border-radius: 50%;
					border: 1upx solid #fff;
					background-color: #e0e0e0;
				}

				.user-info__content {
					margin-left: 20upx;

					.user-name {
						color: $text-white;
						font-size: 32upx;
						font-weight: 500;
					}

					.user-phone {
						color: $text-white;
						font-size: 26upx;
						margin-top: 10upx;
						opacity: 0.8;
					}
				}
			}
		}
	}

	// 菜单列表样式
	.menu-list {
		background-color: $bg-white;
		border-radius: $border-radius;
		margin-bottom: 20upx;
		overflow: hidden;
	}
	.menu-list:last-child{
		margin: 0;
	} 
	.menu-item {
		display: flex;
		align-items: center;
		padding: 30upx 20upx;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			left: 20upx;
			right: 20upx;
			bottom: 0;
			height: 1px;
			background-color: #f2f2f2;
		}
		
		.menu-icon {
			margin-right: 20upx;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50upx;
		}
		
		.menu-text {
			flex: 1;
			font-size: 30upx;
			color: #333;
		}
		
		.menu-arrow {
			margin-left: 10upx;
		}
	}

	// 通用间距
	.mt20 {
		margin-top: 20upx;
	}
</style>