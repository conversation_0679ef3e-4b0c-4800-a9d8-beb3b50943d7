<template>
	<view class="mask" v-if="show">
		<!-- 红包封面 -->
		<view class="hongbao-container" @click.stop="showAnimation" v-if="!showOverlay">
			<!-- 信封标题 -->
			<view class="envelope-title">
				<text class="title-text">来自勃学超市的一封信</text>
				<view class="title-decoration"></view>
			</view>
			
			<image src="/static/hongbao.png" mode="aspectFit" class="hongbao-image"></image>
			
			<!-- 欢迎文字 -->
			<view class="welcome-text">
				<!-- <text class="welcome-main">点击打开您的欢迎礼包</text> -->
				<text class="welcome-sub">精彩内容等待您的探索</text>
				<view class="icon-container">
					<text class="icon-item">🎁</text>
					<text class="icon-item">✨</text>
					<text class="icon-item">🎉</text>
				</view>
				<!-- <view class="hongbao-pulse"></view> -->
			</view>
		</view>
		
		<!-- 动画内容 -->
		<view class="animation-container" v-if="showOverlay">
			<!-- 成功信息 -->
			<view class="success-message" v-if="showSuccessMessage">
				<view class="success-icon">🎊</view>
				<text class="success-text">恭喜您成功加入勃学超市!</text>
				<text class="success-sub">开启您的学习之旅</text>
			</view>
			
			<image 
				src="/static/open.webp" 
				mode="widthFix"
				class="animation-image"
				@load="startAnimation"
			></image>
		</view>
		
		<!-- 飘落的金币效果 -->
		<view class="coins-container" v-if="showCoins">
			<view 
				class="coin" 
				v-for="(coin, index) in 30" 
				:key="index"
				:style="{
					left: `${(index * 3) % 100}%`,
					width: `${30 + (index % 20)}rpx`,
					height: `${30 + (index % 20)}rpx`,
					animationDelay: `${Math.random() * 2}s`,
					animationDuration: `${2 + Math.random() * 3}s`
				}"
			></view>
		</view>
		
		<!-- 装饰元素 -->
		<view class="decoration-elements" v-if="showOverlay">
			<view class="star" v-for="(star, index) in 15" :key="'star-'+index"
				:style="{
					left: `${Math.random() * 100}%`,
					top: `${Math.random() * 100}%`,
					animationDelay: `${Math.random() * 3}s`,
					animationDuration: `${1 + Math.random() * 2}s`
				}"
			></view>
		</view>
		
		<!-- 关闭按钮 - 绝对定位到底部 -->
		<view class="close-btn" v-if="showCloseBtn && showOverlay" @click="closeMask">
			<text>开始体验</text>
		</view>
		
		<!-- 音频元素 -->
		<audio 
			id="audioPlayer" 
			src="/static/zengzhang.mp3" 
			ref="audioPlayer"
		></audio>
	</view>
</template>

<script>
export default {
	name: 'simple-red-packet',
	data() {
		return {
			show: true,
			showOverlay: false,
			showCloseBtn: false,
			showCoins: false,
			showSuccessMessage: false,
			closeTimer: null,
			animationTimer: null,
			audioPlayer: null
		}
	},
	onReady() {
		// 初始化音频播放器
		this.audioPlayer = uni.createInnerAudioContext();
		this.audioPlayer.src = '/static/zengzhang.mp3';
		this.audioPlayer.onError((res) => {
			console.error('音频播放错误：', res);
		});
	},
	methods: {
		// 显示动画并播放音效
		showAnimation() {
			this.showOverlay = true;
			this.showCoins = true; // 立即显示金币效果
			
			// 播放音效
			if (this.audioPlayer) {
				this.audioPlayer.play();
			}
			
			// 延迟显示成功信息
			setTimeout(() => {
				this.showSuccessMessage = true;
			}, 1500);
			
			// 延迟显示关闭按钮
			this.closeTimer = setTimeout(() => {
				this.showCloseBtn = true;
			}, 3000);
		},
		
		// 图片加载完成后的回调
		startAnimation() {
			console.log('动画图片加载完成');
		},
		
		// 关闭蒙版并返回
		closeMask() {
			// 清理计时器
			if (this.closeTimer) {
				clearTimeout(this.closeTimer);
				this.closeTimer = null;
			}
			
			if (this.animationTimer) {
				clearTimeout(this.animationTimer);
				this.animationTimer = null;
			}
			
			// 返回上一页
			uni.redirectTo({
				url:'/pages/guide/guide'
			})
			// uni.navigateBack();
		}
	},
	onUnload() {
		// 清理资源
		if (this.closeTimer) {
			clearTimeout(this.closeTimer);
			this.closeTimer = null;
		}
		
		if (this.animationTimer) {
			clearTimeout(this.animationTimer);
			this.animationTimer = null;
		}
		
		// 释放音频资源
		if (this.audioPlayer) {
			this.audioPlayer.destroy();
		}
	}
}
</script>

<style>
page,body{
	background-color: transparent;
}
</style>

<style lang="scss" scoped>
.mask {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: rgba(0, 0, 0, 0.7);
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	height: 100vh !important;
}

/* 信封标题样式 */
.envelope-title {
	position: absolute;
	top: -80rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 5;
	width: 90%;
	text-align: center;
	animation: titleFloat 3s ease-in-out infinite;
}

.title-text {
	font-size: 40rpx;
	color: #FFD700;
	font-weight: bold;
	letter-spacing: 6rpx;
	text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
	background: linear-gradient(to right, #FFD700, #FFA500);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	display: inline-block;
	padding: 15rpx 30rpx;
	border-radius: 40rpx;
	background-color: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(5px);
	// box-shadow: 0 8rpx 32rpx rgba(255, 193, 7, 0.3);
}

.title-decoration {
	width: 80%;
	height: 6rpx;
	background: linear-gradient(90deg, transparent, #FFD700, transparent);
	margin: 10rpx auto;
	border-radius: 3rpx;
}

.hongbao-container {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	animation: zoomIn 0.5s ease;
	padding-top: 60rpx;
}

.hongbao-image {
	width: 700rpx;
	height: 700rpx;
	animation: float 3s ease-in-out infinite;
	filter: drop-shadow(0 10rpx 30rpx rgba(255, 87, 34, 0.6));
}

.welcome-text {
	margin-top: 40rpx;
	color: #FFD54F;
	font-weight: bold;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.7);
	letter-spacing: 5rpx;
	width: 100%;
	text-align: center;
}

.welcome-main {
	font-size: 36rpx;
	margin-bottom: 15rpx;
	background: linear-gradient(to right, #FFD54F, #FFC107);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.welcome-sub {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	margin-bottom: 20rpx;
}

.icon-container {
	display: flex;
	justify-content: center;
	margin: 15rpx 0;
	width: 100%;
}

.icon-item {
	font-size: 40rpx;
	margin: 0 15rpx;
	animation: bounce 2s infinite;
}

.icon-item:nth-child(1) { animation-delay: 0s; }
.icon-item:nth-child(2) { animation-delay: 0.3s; }
.icon-item:nth-child(3) { animation-delay: 0.6s; }

.hongbao-pulse {
	width: 20rpx;
	height: 20rpx;
	background-color: #FFD54F;
	border-radius: 50%;
	margin-top: 20rpx;
	animation: pulse 1.5s infinite;
}

.animation-container {
	position: relative;
	width: 100%;
	max-width: 750rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10;
}

.animation-image {
	width: 100%;
	height: auto;
	animation: zoomIn 0.5s ease;
}

/* 成功信息样式 */
.success-message {
	width: 100%;
	position: absolute;
	top: 150rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 15;
	display: flex;
	flex-direction: column;
	align-items: center;
	// background: rgba(255, 255, 255, 0.1);
	// backdrop-filter: blur(10px);
	padding: 30rpx 60rpx;
	border-radius: 30rpx;
	// box-shadow: 0 10rpx 30rpx rgba(255, 193, 7, 0.4);
	animation: fadeInDown 0.8s ease forwards;
}

.success-icon {
	font-size: 70rpx;
	margin-bottom: 20rpx;
	// animation: rotate 3s linear infinite;
}

.success-text {
	font-size: 36rpx;
	color: #FFD700;
	font-weight: bold;
	margin-bottom: 10rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.success-sub {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
}

/* 装饰星星 */
.decoration-elements {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 5;
}

.star {
	position: absolute;
	width: 20rpx;
	height: 20rpx;
	background-color: #FFD700;
	clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
	opacity: 0.7;
	animation: twinkle 2s ease-in-out infinite;
}

.close-btn {
	position: absolute;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	padding: 20rpx 80rpx;
	background: linear-gradient(135deg, #FFC107 0%, #FF9800 100%);
	border-radius: 50rpx;
	animation: fadeInUp 0.5s ease;
	z-index: 20;
	box-shadow: 0 10rpx 20rpx rgba(255, 152, 0, 0.4);
	
	text {
		color: #FFF;
		font-size: 32rpx;
		font-weight: bold;
		letter-spacing: 4rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	}
}

/* 金币飘落效果 */
.coins-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	pointer-events: none;
	z-index: 5; /* 降低金币层级，确保不会遮挡动画 */
}

.coin {
	position: absolute;
	top: -50rpx;
	background-image: url('/static/jinbi.png');
	background-size: 100% 100%;
	background-repeat: no-repeat;
	border-radius: 50%;
	animation: coinFall 3s linear forwards;
	opacity: 0.9;
}

/* 动画定义 */
@keyframes zoomIn {
	from {
		opacity: 0;
		transform: scale(0.3);
	}
	to {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translate(-50%, 30rpx);
	}
	to {
		opacity: 1;
		transform: translate(-50%, 0);
	}
}

@keyframes fadeInDown {
	from {
		opacity: 0;
		transform: translate(-50%, -30rpx);
	}
	to {
		opacity: 1;
		transform: translate(-50%, 0);
	}
}

@keyframes float {
	0% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-15rpx);
	}
	100% {
		transform: translateY(0);
	}
}

@keyframes titleFloat {
	0% {
		transform: translateX(-50%) translateY(0);
	}
	50% {
		transform: translateX(-50%) translateY(-10rpx);
	}
	100% {
		transform: translateX(-50%) translateY(0);
	}
}

@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(255, 213, 79, 0.7);
	}
	70% {
		box-shadow: 0 0 0 20rpx rgba(255, 213, 79, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(255, 213, 79, 0);
	}
}

@keyframes coinFall {
	0% {
		top: -50rpx;
		transform: translateX(0) rotate(0deg);
		opacity: 1;
	}
	100% {
		top: 120%;
		transform: translateX(100rpx) rotate(360deg);
		opacity: 0;
	}
}

@keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		transform: translateY(0);
	}
	40% {
		transform: translateY(-20rpx);
	}
	60% {
		transform: translateY(-10rpx);
	}
}

@keyframes twinkle {
	0% {
		opacity: 0.3;
		transform: scale(0.8);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.2);
	}
	100% {
		opacity: 0.3;
		transform: scale(0.8);
	}
}

@keyframes rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
</style> 