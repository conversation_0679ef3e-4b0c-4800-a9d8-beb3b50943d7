## 1.1.1（2024-01-05）
添加监听，优化网络请求返回数据不回显
## 1.1.0（2023-09-25）
修改数据回显的bug
## 1.0.9（2023-07-11）
感谢评论区小伙伴的纠正，粗心把indexPop写成indexsPop了，已修改，欢迎大家进入群聊一起摸鱼，也可以一起学习进步
## 1.0.8（2023-07-06）
修复评论区719***@qq.com小伙伴提出的问题，增加了参数默认值
## 1.0.7（2023-07-04）
新增根据type选择省、省市、省市区，修复历史遗留bug
## 1.0.6（2023-07-04）
新增根据type选择省、省市、省市区，修复历史遗留bug
## 1.0.5（2023-04-29）
新增选择器根据地区Id和地区索引回显，优先级addressData > indexs > areaId
## 1.0.4（2023-04-09）
返回了地址id，可以将地址id存到数据库，地址回显时使用地址索引
不会使用可以咨询我，<EMAIL>
## 1.0.3（2022-12-18）
三级联动地区选择器，根据uView的picker选择器二次封装，所以使用时记得安装uVIew和scss
## 1.0.2（2022-12-18）
三级联动地区选择器，根据uView的picker选择器二次封装，所以使用时记得安装uVIew和scss
## 1.0.1（2022-12-18）
基于uView封装的地区选择器，内置了地区数据，只在vue2的微信小程序做了测试，所以其余平台全部是X
## 1.0.0（2022-12-18）
基于uView封装的地区选择器，内置了地区数据，只在vue2的微信小程序做了测试，所以其余平台全部是X
