<template>
	<view class="text-detail-container">
		<!-- 自定义导航栏 -->
		<u-navbar height="44" :title="pageTitle" :bgColor="bgColor" :placeholder='true' :auto-back="true">
			<view slot="left">
				<u-icon name="arrow-left" size="20" color="#000"></u-icon>
			</view>
		</u-navbar>
		
		<!-- 内容区域 -->
		<view class="text-content">
			<!-- 标题区域 -->
			<view class="text-header">
				<text class="text-title">{{textDetail.tittle || pageTitle || '无标题'}}</text>
				<view class="text-meta" v-if="textDetail.creationTime">
					<text class="text-date">{{formatDate(textDetail.creationTime)}}</text>
				</view>
			</view>
			
			<!-- 分隔线 -->
			<view class="text-divider"></view>
			
			<!-- 富文本内容 -->
			<view class="text-body">
				<template v-if="textContent">
					<mp-html :content="textContent" />
				</template>
				<template v-else-if="!loading">
					<view class="no-content">
						<u-icon name="file-text" size="60" color="#ccc"></u-icon>
						<text>暂无内容</text>
					</view>
				</template>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<u-loading-page loading-text="加载中" fontSize='14' icon-size="30" loading-mode="semicircle" :loading="loading"></u-loading-page>
		
		<!-- 提示组件 -->
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				bgColor: 'rgba(255,255,255,1)',
				pageTitle: '详情',
				textId: '',
				textDetail: {},
				textContent: '',
				companyId: ''
			}
		},
		onLoad(options) {
			// 获取传递的参数
			if (options.id) {
				this.textId = options.id;
			}
			if (options.title) {
				this.pageTitle = decodeURIComponent(options.title);
				this.textDetail.tittle = this.pageTitle;
			}
			if (options.content) {
				this.textContent = decodeURIComponent(options.content);
			}
			if (options.creationTime) {
				this.textDetail.creationTime = decodeURIComponent(options.creationTime);
			}

			// 直接设置加载完成
			this.loading = false;
		},
		methods: {
			
			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return '';
				
				try {
					const date = new Date(dateStr);
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					
					return `${year}-${month}-${day}`;
				} catch (e) {
					console.error('日期格式化错误:', e);
					return dateStr;
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #f8f9fa;
	}
	
	.text-detail-container {
		min-height: 100vh;
		background-color: #f8f9fa;
	}
	
	.text-content {
		padding: 0 30upx 30upx;
		background-color: #fff;
		margin: 20upx;
		border-radius: 16upx;
		box-shadow: 0 4upx 20upx rgba(0, 0, 0, 0.06);
	}
	
	.text-header {
		padding: 40upx 0 30upx;
		text-align: center;
	}
	
	.text-title {
		font-size: 36upx;
		font-weight: bold;
		color: #333;
		line-height: 1.4;
		margin-bottom: 20upx;
		display: block;
	}
	
	.text-meta {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.text-date {
		font-size: 24upx;
		color: #999;
	}
	
	.text-divider {
		height: 2upx;
		background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
		margin-bottom: 40upx;
	}
	
	.text-body {
		line-height: 1.8;
		font-size: 28upx;
		color: #333;
		min-height: 200upx;
		
		// 富文本内容样式
		:deep(.mp-html) {
			font-size: 28upx !important;
			line-height: 1.8 !important;
			color: #333 !important;
			
			p {
				margin-bottom: 20upx;
				text-align: justify;
			}
			
			img {
				max-width: 100% !important;
				height: auto !important;
				border-radius: 8upx;
				margin: 20upx 0;
			}
			
			h1, h2, h3, h4, h5, h6 {
				font-weight: bold;
				margin: 30upx 0 20upx;
				color: #2BCBD4;
			}
			
			h1 { font-size: 36upx; }
			h2 { font-size: 32upx; }
			h3 { font-size: 30upx; }
			h4 { font-size: 28upx; }
			
			ul, ol {
				padding-left: 40upx;
				margin: 20upx 0;
			}
			
			li {
				margin-bottom: 10upx;
			}
			
			blockquote {
				border-left: 6upx solid #2BCBD4;
				padding-left: 20upx;
				margin: 20upx 0;
				background-color: #f8f9fa;
				padding: 20upx;
				border-radius: 8upx;
			}
			
			table {
				width: 100%;
				border-collapse: collapse;
				margin: 20upx 0;
			}
			
			th, td {
				border: 1upx solid #e0e0e0;
				padding: 16upx;
				text-align: left;
			}
			
			th {
				background-color: #f8f9fa;
				font-weight: bold;
			}
		}
	}
	
	.no-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100upx 0;
		color: #999;
		
		text {
			margin-top: 20upx;
			font-size: 28upx;
		}
	}
</style>
