# 跨平台视频播放配置指南

## 问题解决

### ✅ 1. CSS语法错误修复
修复了 `videoPlayer.vue` 中的CSS语法错误：
```scss
// 错误：多余的大括号
}
}
</style>

// 修复：移除多余大括号
}
</style>
```

### ✅ 2. 跨平台跳转逻辑
更新了 `courseDetail.vue` 的跳转逻辑，根据平台自动选择合适的播放页面：

```javascript
// 根据平台选择不同的播放页面
// #ifdef APP-PLUS
// App环境使用WebView页面
uni.navigateTo({
    url: `/pages/hall/webview?${queryString}`
});
// #endif

// #ifdef H5
// H5环境使用原生播放器页面
uni.navigateTo({
    url: `/pages/hall/videoPlayer?${queryString}`
});
// #endif

// #ifdef MP-WEIXIN
// 微信小程序环境，可以选择使用WebView或原生播放器
uni.navigateTo({
    url: `/pages/hall/webview?${queryString}`
});
// #endif
```

## 当前架构

### 📱 平台分离策略

| 平台 | 使用页面 | 说明 |
|------|----------|------|
| **H5浏览器** | `videoPlayer.vue` | 直接使用阿里云播放器，功能完整 |
| **App** | `webview.vue` | 通过WebView加载H5页面，解决兼容性 |
| **微信小程序** | `webview.vue` | 使用WebView（需要业务域名配置） |

### 🔄 参数传递流程

```
courseDetail.vue (课程详情)
    ↓ 点击"去观看"
    ↓ 判断平台
    ↓
┌─────────────────┬─────────────────┐
│   H5环境        │   App环境       │
│ videoPlayer.vue │  webview.vue    │
│ (阿里云播放器)   │ (加载H5页面)     │
└─────────────────┴─────────────────┘
```

## WebView配置

### 🌐 H5页面部署

您需要完成以下步骤：

#### 1. 创建H5视频播放页面
```html
<!DOCTYPE html>
<html>
<head>
    <title>视频播放</title>
    <!-- 引入阿里云播放器CSS -->
    <link rel="stylesheet" href="https://g.alicdn.com/de/prismplayer/2.15.2/skins/default/aliplayer-min.css" />
</head>
<body>
    <div id="player-container"></div>
    
    <!-- 引入阿里云播放器JS -->
    <script src="https://g.alicdn.com/de/prismplayer/2.15.2/aliplayer-min.js"></script>
    <script>
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const chapterId = urlParams.get('id');
        const videoTitle = urlParams.get('title');
        
        // 初始化播放器
        // ... 您的播放器初始化代码
    </script>
</body>
</html>
```

#### 2. 配置WebView URL
在 `webview.vue` 的 `buildWebviewUrl` 方法中配置：

```javascript
// 方案1: 使用您的域名
const baseUrl = 'https://boxue.com/h5/video-player.html';

// 方案2: 使用相对路径（如果在同一域名）
const baseUrl = '/h5/video-player.html';

// 方案3: 使用CDN
const baseUrl = 'https://cdn.boxue.com/h5/video-player.html';
```

### 📡 消息通信（可选）

如果需要H5页面与App通信，可以在H5页面中添加：

```javascript
// H5页面向App发送消息
function sendMessageToApp(type, data) {
    if (window.uni && window.uni.postMessage) {
        window.uni.postMessage({
            data: [{
                type: type,
                data: data
            }]
        });
    }
}

// 示例：播放器准备就绪
sendMessageToApp('loaded', 'Player ready');

// 示例：播放器错误
sendMessageToApp('error', 'Player initialization failed');

// 示例：更新标题
sendMessageToApp('title', '当前播放：' + videoTitle);
```

## 测试指南

### 🧪 测试步骤

#### 1. H5环境测试
1. 在浏览器中打开应用
2. 进入课程详情页面
3. 点击"去观看"按钮
4. 验证跳转到 `videoPlayer.vue`
5. 确认阿里云播放器正常工作

#### 2. App环境测试
1. 在App中打开应用
2. 进入课程详情页面
3. 点击"去观看"按钮
4. 验证跳转到 `webview.vue`
5. 检查WebView是否正常加载

#### 3. 错误处理测试
1. 配置错误的URL
2. 验证错误提示是否友好
3. 测试重试功能
4. 测试返回功能

### 🔍 调试技巧

#### 1. 查看控制台日志
```javascript
// 在courseDetail.vue中
console.log('跳转到视频播放页面，参数:', params);

// 在webview.vue中
console.log('WebView URL:', this.webviewUrl);
```

#### 2. 检查平台编译条件
```javascript
// 确认编译条件是否正确
// #ifdef APP-PLUS
console.log('当前是App环境');
// #endif

// #ifdef H5
console.log('当前是H5环境');
// #endif
```

#### 3. WebView调试
- 在App中启用WebView调试
- 使用Chrome DevTools连接调试
- 检查网络请求和控制台错误

## 常见问题

### ❓ Q1: WebView显示空白页面
**A1:** 检查以下几点：
- URL是否正确配置
- H5页面是否可以正常访问
- 网络连接是否正常
- 是否有跨域问题

### ❓ Q2: 参数传递不正确
**A2:** 确认参数编码：
```javascript
// 正确的参数传递
title: this.videoTitle, // 在webview.vue中已经解码，不需要再次编码

// 错误的参数传递
title: encodeURIComponent(this.videoTitle), // 会导致双重编码
```

### ❓ Q3: App环境播放器不工作
**A3:** 这是预期行为，App环境使用WebView加载H5页面，需要：
1. 部署H5视频播放页面
2. 配置正确的URL
3. 确保H5页面中的阿里云播放器正常工作

### ❓ Q4: 微信小程序WebView限制
**A4:** 微信小程序的WebView需要：
1. 配置业务域名白名单
2. 域名必须支持HTTPS
3. 域名必须备案

## 下一步计划

### 🚀 立即执行
1. **部署H5页面**：创建并部署视频播放的H5页面
2. **配置URL**：在webview.vue中设置正确的baseUrl
3. **测试功能**：在不同平台测试播放功能

### 🔮 未来优化
1. **离线缓存**：支持视频离线播放
2. **播放记录**：记录播放进度和历史
3. **清晰度切换**：支持多种视频清晰度
4. **弹幕功能**：增加互动体验

现在您的应用已经具备了完整的跨平台视频播放能力！🎉
