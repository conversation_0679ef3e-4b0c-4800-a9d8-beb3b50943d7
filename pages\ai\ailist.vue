<template>
	<view style="position: relative;height: 100%;">
		<!-- 顶部状态栏 -->
		<u-sticky>
			<u-navbar title="AI实验室" height="44" :titleStyle='topNavStyle.Tstyle' :left-icon-color="topNavStyle.Lstyle"
				:bgColor="topNavStyle.style" :auto-back="true" :placeholder='true' style="z-index: 1;">
			</u-navbar>
		</u-sticky>
		
		<view v-if="loading">
			<x-skeleton type="banner" :loading="true">
				<view></view>
			</x-skeleton>
			<x-skeleton type="menu" :loading="true">
				<view></view>
			</x-skeleton>
			<x-skeleton type="waterfall" :loading="true" :configs="{
					gridColumns: 2,
					headHeight: '200rpx',
					textRows: 1,
					gridRows: 1,
				}">
				<view></view>
			</x-skeleton>
		</view>
		<view v-if="!loading">
			
		
		<!-- 标题区域 -->
		<view style="padding:30rpx 30rpx 10rpx;">
			<!-- <view style="font-size: 48rpx;color: #fff;font-weight: bold;margin-top: 10rpx;">
				AI实验室
			</view> -->
			<view style="font-size:48rpx;color: #fff;margin: 10rpx 0;font-weight: bold;">
				探索AI的无限可能
			</view>
		</view>
		
		<!-- 主要内容区域 -->
		<view style="padding:10rpx 30rpx;margin-bottom: 120rpx;">
			<!-- DeepSeek对话区域 -->
			<view class="ai-card" @click="navigateToResource(1)">
				<view class="ai-card-header">
					<image src="/static/avatar.jpg" class="ai-icon"></image>
					<view class="ai-title">与DeepSeek对话</view>
					<view class="ai-btn">对话一下</view>
				</view>
				<view class="ai-card-body">
					<view class="ai-desc">基于大语言模型，为您提供各类问题的解答和帮助</view>
				</view>
			</view>
			<!-- 素材中心 -->
			<view class="resource-center" @click="navigateToResource">
				<view class="resource-text">素材中心</view>
				<view class="resource-desc">快速下载客户素材</view>
				<view class="resource-btn">GO</view>
			</view>
			<!-- 功能区域 -->
			<view class="func-grid">
				<view class="func-row">
					<view class="func-item" @click="navigateToAI(aiData)">
						<view class="func-icon ai-video">
							<image src="/static/image/icons/yingwen.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">英语学习</view>
							<view class="func-desc">孩子轻松学英语</view>
						</view>
					</view>
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-text">
							<image src="/static/image/icons/tingxie.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">英语听写</view>
							<view class="func-desc">轻松背单词提效率</view>
						</view>
					</view>
				</view>
				
				<view class="func-row">
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-math">
							<image src="/static/image/icons/ai/icon-01.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">数学解题</view>
							<view class="func-desc">智能辅导解题训练</view>
						</view>
					</view>
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-chinese">
							<image src="/static/image/icons/ai/icon-02.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">语文学习</view>
							<view class="func-desc">古诗词文言文辅导</view>
						</view>
					</view>
				</view>
				
				<view class="func-row">
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-science">
							<image src="/static/image/icons/ai/icon-03.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">科学实验</view>
							<view class="func-desc">趣味科学知识探索</view>
						</view>
					</view>
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-story">
							<image src="/static/image/icons/ai/icon-04.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">故事大全</view>
							<view class="func-desc">智讲故事培养想象</view>
						</view>
					</view>
				</view>
				
				<view class="func-row">
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-write">
							<image src="/static/image/icons/ai/icon-05.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">作文辅导</view>
							<view class="func-desc">提供写作思路和修改</view>
						</view>
					</view>
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-art">
							<image src="/static/image/icons/ai/icon-06.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">AI绘画</view>
							<view class="func-desc">激发创意绘画能力</view>
						</view>
					</view>
				</view>
				
				<view class="func-row">
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-music">
							<image src="/static/image/icons/ai/icon-07.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">音乐创作</view>
							<view class="func-desc">学习音乐基础知识</view>
						</view>
					</view>
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-history">
							<image src="/static/image/icons/ai/icon-08.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">历史探索</view>
							<view class="func-desc">趣味了解历史知识</view>
						</view>
					</view>
				</view>
				
				<view class="func-row">
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-puzzle">
							<image src="/static/image/icons/ai/icon-09.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">益智游戏</view>
							<view class="func-desc">智力开发逻辑训练</view>
						</view>
					</view>
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-habit">
							<image src="/static/image/icons/ai/icon-10.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">好习惯养成</view>
							<view class="func-desc">培养学习好习惯</view>
						</view>
					</view>
				</view>
				
				<view class="func-row">
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-geography">
							<image src="/static/image/icons/ai/icon-11.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">地理探索</view>
							<view class="func-desc">世界地理知识学习</view>
						</view>
					</view>
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-program">
							<image src="/static/image/icons/ai/icon-12.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">少儿编程</view>
							<view class="func-desc">培养逻辑思维能力</view>
						</view>
					</view>
				</view>
				
				<view class="func-row">
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-nature">
							<image src="/static/image/icons/ai/icon-13.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">自然百科</view>
							<view class="func-desc">探索动植物知识</view>
						</view>
					</view>
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-safety">
							<image src="/static/image/icons/ai/icon-14.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">安全教育</view>
							<view class="func-desc">学习生活安全知识</view>
						</view>
					</view>
				</view>
				
				<view class="func-row">
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-reading">
							<image src="/static/image/icons/ai/icon-15.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">阅读理解</view>
							<view class="func-desc">提升阅读理解能力</view>
						</view>
					</view>
					<view class="func-item" @click="navigateToResource(2)">
						<view class="func-icon ai-speech">
							<image src="/static/image/icons/ai/icon-16.png" mode="widthFix" style="width: 50upx;"></image>
						</view>
						<view class="ml10">
							<view class="func-name">口语训练</view>
							<view class="func-desc">提高语言表达能力</view>
						</view>
					</view>
				</view>
			</view>
			
			
		</view>
		
		<!-- 帮助与客服按钮 -->
		<view class="bottom-bar">
			<view class="help-btn" @click="navigateToHelp">
				<u-icon name="question-circle" size="20px" color="#00C1CC"></u-icon>
				<text class="help-text">帮助</text>
			</view>
			<view class="contact-btn" @click="makePhone">
				<u-icon name="kefu-ermai" size="20px" color="#00C1CC"></u-icon>
				<text class="contact-text">联系客服</text>
			</view>
		</view>
		</view>
		<!-- 底部导航 -->
		<!-- <view class="tab-bar">
			<view class="tab-item active">
				<u-icon name="home" size="24px" color="#00C1CC"></u-icon>
				<text class="tab-text">首页</text>
			</view>
			<view class="tab-item">
				<u-icon name="file-text" size="24px" color="#888"></u-icon>
				<text class="tab-text">课程</text>
			</view>
			<view class="tab-item">
				<u-icon name="account" size="24px" color="#888"></u-icon>
				<text class="tab-text">我的</text>
			</view>
		</view> -->
		
		<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
			:loading="loading"></u-loading-page>
	</view>
</template>

<script>
	var that
	import {
		requestPermissions
	} from "@/app-permission.js"
	export default {
		data() {
			return {
				loading: true,
				aiData: null,
				pageScrollTop: 0, // 页面滚动距离
				bgColor: '#ffffff00',
			}
		},
		computed: {
			topNavStyle() {
				let r = this.pageScrollTop / 100;
				return {
					"style": `rgba(255,255,255,${r>=1?1:r})`,
					"Tstyle": `color:${r>1?'#000':'#fff'}`,
					"Lstyle": r > 1 ? '#000' : '#fff'
				}
			}
		},
		onLoad() {
			that = this
			this.getAI()
		},
		onPageScroll(e) {
			this.pageScrollTop = Math.floor(e.scrollTop);
		},
		methods: {
			// 获取智能体数据
			async getAI() {
				try {
					const res = await that.http.ajax({
						url: that.http.api.agentconfigget,
						method: 'GET',
						data: {
							id: 1
						}
					})
					
					if (res.code === 0) {
						this.aiData = res.data || null
					} else {
						this.showError(res.msg || '获取智能体数据失败')
					}
				} catch (err) {
					this.showError('获取智能体数据失败')
					console.error('获取智能体数据失败:', err)
				} finally {
					this.loading = false
				}
			},
			
			// 跳转到AI页面
			navigateToAI(aiData) {
				if (!aiData) return
				
				// 获取用户信息和token
				const userInfo = uni.getStorageSync('userInfo') || {}
				const token = uni.getStorageSync('token') || ''
				
				// 构建传递的参数
				const params = {
					token: aiData.agentSecretToken,
					id: aiData.id ,
					botid: aiData.agentBotId ,
					userId: userInfo.id || ''
				}
				
				// 跳转到AI页面并传递参数
				uni.navigateTo({
					url: '/pages/ai/ai?detail='+JSON.stringify(params),
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						res.eventChannel.emit('acceptDataFromOpenerPage', params)
					}
				})
			},
			
			// 跳转到帮助页面
			navigateToHelp() {
				uni.navigateTo({
					url: '/pages/helpList/helpList'
				})
			},
			
			// 跳转到素材中心
			navigateToResource(type) {
				console.log(type)
				var title=''
				// uni.navigateTo({
				// 	url:'/pages/ai/project/pzst'
				// })
				// return;
				if(type==1){
					title='大模型功能即将上线'
				}else if(type==2){
					title='智能学习功能即将上线'
				}else{
					title='素材中心功能即将上线'
				}
				uni.showToast({
					title: title,
					icon: 'none'
				})
				
			},
			
			// 拨打客服电话
			makePhone() {
				// #ifdef APP-PLUS
				// 先申请电话权限
				requestPermissions({
					title: "拨打电话权限申请说明",
					content: "便于您使用该功能直接拨打电话等场景中使用",
					permissionID: "CALL_PHONE"
				}).then(cameraResult => {
					console.log(cameraResult)
					if (!cameraResult.isSuc) {
						uni.showToast({
							title: '未获得电话权限',
							icon: 'none'
						});
						return;
					}
					// 拨打电话
					uni.makePhoneCall({
						phoneNumber: '400-6199-839',
						success: () => {
							console.log('拨打电话成功');
						},
						fail: (err) => {
							console.log('拨打电话失败:', err);
							// 拨打失败时也检查权限
							if (err.errMsg && err.errMsg.includes('denied')) {
								this.makePhone(); // 重新触发权限检查
							}
						}
					});
				}).catch(error => {
					uni.showToast({
						title: '权限申请失败',
						icon: 'none'
					});
				});
				// #endif
				
				// #ifdef H5
				uni.makePhoneCall({
					phoneNumber: '400-6199-839',
					success: () => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.log('拨打电话失败:', err);
					}
				});
				// #endif
			},
			
			// 错误提示
			showError(message) {
				uni.showToast({
					title: message,
					icon: 'none'
				})
			}
		}
	}
</script>

<style>
	page,
	body {
		background-image: linear-gradient(180deg, #00C1CC, #f3f3f3);
		background-size: 100% 45%;
		height: 100%;
	}
</style>

<style lang="scss" scoped>
	.ai-card {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
		
		.ai-card-header {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			
			.ai-icon {
				width: 80rpx;
				height: 80rpx;
				border-radius: 40rpx;
				margin-right: 20rpx;
			}
			
			.ai-title {
				flex: 1;
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
			}
			
			.ai-btn {
				background-color: #00C1CC;
				color: #fff;
				padding: 10rpx 30rpx;
				border-radius: 30rpx;
				font-size: 24rpx;
			}
		}
		
		.ai-card-body {
			.ai-desc {
				font-size: 26rpx;
				color: #666;
				line-height: 1.5;
			}
		}
	}
	
	.ml10 {
		margin-left: 10rpx;
	}
	
	.func-grid {
		margin-bottom: 30rpx;
		
		.func-row {
			display: flex;
			margin-bottom: 20rpx;
			
			.func-item {
				display: flex;
				flex: 1;
				background-color: #fff;
				border-radius: 20rpx;
				padding: 30rpx 15rpx ;
				margin: 0 10rpx;
				position: relative;
				// height: 180rpx;
				
				&:first-child {
					margin-left: 0;
				}
				
				&:last-child {
					margin-right: 0;
				}
				
				.func-icon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 90rpx;
					height: 90rpx;
					border-radius: 45rpx;
					// margin-bottom: 15rpx;
					
					&.ai-video { background-color: #7ED321; }
					&.ai-text { background-color: #50E3C2; }
					&.ai-math { background-color: #FF9500; }
					&.ai-chinese { background-color: #FF3B30; }
					&.ai-science { background-color: #5AC8FA; }
					&.ai-story { background-color: #9B59B6; }
					&.ai-write { background-color: #4CD964; }
					&.ai-art { background-color: #FF2D55; }
					&.ai-music { background-color: #007AFF; }
					&.ai-history { background-color: #8E8E93; }
					&.ai-puzzle { background-color: #FFCC00; }
					&.ai-habit { background-color: #34AADC; }
					&.ai-geography { background-color: #5856D6; }
					&.ai-program { background-color: #FF9500; }
					&.ai-nature { background-color: #4CD964; }
					&.ai-safety { background-color: #FF3B30; }
					&.ai-reading { background-color: #007AFF; }
					&.ai-speech { background-color: #5AC8FA; }
				}
				
				.func-name {
					font-size: 32rpx;
					color: #333;
					font-weight: bold;
					margin-bottom: 18rpx;
				}
				
				.func-desc {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
	
	.resource-center {
		margin-bottom: 30rpx;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		display: flex;
		align-items: center;
		position: relative;
		height: 90rpx;
		
		.resource-text {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-right: 20rpx;
		}
		
		.resource-desc {
			font-size: 24rpx;
			color: #999;
			flex: 1;
		}
		
		.resource-btn {
			background-color: #00C1CC;
			color: #fff;
			width: 70rpx;
			height: 70rpx;
			border-radius: 35rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			font-weight: bold;
		}
	}
	
	.bottom-bar {
		position: fixed;
		bottom: 40rpx;
		left: 0;
		right: 0;
		display: flex;
		justify-content: center;
		z-index: 10;
		
		.help-btn, .contact-btn {
			background-color: #fff;
			border-radius: 40rpx;
			padding: 15rpx 40rpx;
			display: flex;
			align-items: center;
			margin: 0 20rpx;
			box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
			
			.help-text, .contact-text {
				margin-left: 10rpx;
				font-size: 28rpx;
				color: #333;
			}
		}
	}
	
	.tab-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #fff;
		display: flex;
		border-top: 1px solid #eee;
		
		.tab-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			
			&.active {
				.tab-text {
					color: #00C1CC;
				}
			}
			
			.tab-text {
				font-size: 24rpx;
				color: #888;
				margin-top: 5rpx;
			}
		}
	}
</style>