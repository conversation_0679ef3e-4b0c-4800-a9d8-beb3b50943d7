<template>
  <view class="photo-cropper" :class="{ 'photo-cropper--visible': isVisible }">
    <view class="photo-cropper__mask" @click="cancel"></view>
    
    <view class="photo-cropper__container" :style="{ paddingTop: `${statusBarHeight}px` }">
      <view class="photo-cropper__header">
        <text class="photo-cropper__title">调整图片</text>
      </view>
      
      <view class="photo-cropper__content">
        <view class="photo-cropper__canvas-container">
          <canvas
            canvas-id="photo-cropper-canvas"
            class="photo-cropper__canvas"
            :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
            disable-scroll="true"
            @touchstart="touchStart"
            @touchmove="touchMove"
            @touchend="touchEnd"
          ></canvas>
          
          <!-- 裁剪框提示 -->
          <view class="crop-frame" v-if="imagePath" :style="cropFrameStyle" :class="{'active-dragging': isDragging && touchType === 'frame'}">
            <!-- 裁剪框中央拖动区域 -->
            <view class="drag-area" @touchstart="handleDragStart">
              <!-- 添加中央拖动图标 -->
              <view class="drag-icon">
                <view class="drag-dot"></view>
              </view>
            </view>
            
            <!-- 四个角缩放控制点 -->
            <view 
              class="corner top-left" 
              :class="{'active': activeCorner === 'top-left'}"
              @touchstart="handleCornerStart('top-left')"
            >
              <view class="corner-icon"></view>
            </view>
            <view 
              class="corner top-right" 
              :class="{'active': activeCorner === 'top-right'}"
              @touchstart="handleCornerStart('top-right')"
            >
              <view class="corner-icon"></view>
            </view>
            <view 
              class="corner bottom-left" 
              :class="{'active': activeCorner === 'bottom-left'}"
              @touchstart="handleCornerStart('bottom-left')"
            >
              <view class="corner-icon"></view>
            </view>
            <view 
              class="corner bottom-right" 
              :class="{'active': activeCorner === 'bottom-right'}"
              @touchstart="handleCornerStart('bottom-right')"
            >
              <view class="corner-icon"></view>
            </view>
            
            <!-- 边框线 -->
            <view class="border top"></view>
            <view class="border right"></view>
            <view class="border bottom"></view>
            <view class="border left"></view>
            
            <!-- 网格线 -->
            <view class="grid-line horizontal"></view>
            <view class="grid-line vertical"></view>
          </view>
          
          <!-- 操作提示 -->
          <view class="crop-hint" v-if="imagePath && !isDragging">
            <text>拖动中心区域移动，拖动四角调整大小</text>
          </view>
          
          <!-- 当前操作状态提示 -->
          <view class="mode-hint" v-if="isDragging">
            <text v-if="touchType === 'frame'">移动位置</text>
            <text v-else-if="touchType === 'corner'">调整大小</text>
          </view>
        </view>
      </view>
      
      <!-- 底部操作栏 -->
      <view class="photo-cropper__actions">
        <button class="action-btn cancel-btn" @click="cancel">取消</button>
        <view class="action-ratio">
          <text class="ratio-label">比例</text>
          <view class="ratio-options">
            <view 
              class="ratio-option" 
              :class="{ active: selectedRatio === 'free' }"
              @click="setRatio('free')"
            >
              自由
            </view>
            <view 
              class="ratio-option" 
              :class="{ active: selectedRatio === '1:1' }"
              @click="setRatio(1)"
            >
              1:1
            </view>
            <view 
              class="ratio-option" 
              :class="{ active: selectedRatio === '4:3' }"
              @click="setRatio(4/3)"
            >
              4:3
            </view>
            <view 
              class="ratio-option" 
              :class="{ active: selectedRatio === '16:9' }"
              @click="setRatio(16/9)"
            >
              16:9
            </view>
          </view>
        </view>
        <button class="action-btn confirm-btn" @click="confirm">确定</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PhotoCropper',
  props: {
    // 裁剪框比例
    aspectRatio: {
      type: Number,
      default: 0 // 0表示自由裁剪
    },
    // 图片质量
    quality: {
      type: Number,
      default: 0.8
    }
  },
  data() {
    return {
      isVisible: false,
      statusBarHeight: 0,
      canvasWidth: 300,
      canvasHeight: 300,
      imagePath: '',
      imageInfo: null,
      currentRatio: 0, // 当前使用的比例
      selectedRatio: 'free', // 选择的比例选项
      ctx: null,
      // 裁剪框相关
      cropRect: {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      },
      initialCropRect: null,
      isDragging: false,
      dragStartX: 0,
      dragStartY: 0,
      touchType: '', // frame:移动框, corner:调整大小
      activeCorner: '', // top-left, top-right, bottom-left, bottom-right
      minSize: 80, // 裁剪框最小尺寸
      canvasToImageRatio: 1, // 画布到实际图片的比例
      lastTouchTime: 0, // 用于防止快速连续触摸
      preventTouchMove: false // 用于控制是否阻止touchmove
    }
  },
  computed: {
    cropFrameStyle() {
      return {
        left: `${this.cropRect.x}px`,
        top: `${this.cropRect.y}px`,
        width: `${this.cropRect.width}px`,
        height: `${this.cropRect.height}px`
      }
    }
  },
  created() {
    // 获取状态栏高度
    // #ifdef APP-PLUS || MP
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 20;
    // #endif
    
    // #ifdef H5
    this.statusBarHeight = 0;
    // #endif
    
    // 计算画布尺寸
    const sysInfo = uni.getSystemInfoSync();
    this.canvasWidth = sysInfo.windowWidth;
    this.canvasHeight = sysInfo.windowHeight - this.statusBarHeight - 200; // 留出底部操作区域的空间
    
    // 初始化裁剪比例
    this.currentRatio = this.aspectRatio;
    
    // 设置默认选中的比例
    if (this.aspectRatio === 0) {
      this.selectedRatio = 'free';
    } else if (this.aspectRatio === 1) {
      this.selectedRatio = '1:1';
    } else if (Math.abs(this.aspectRatio - 4/3) < 0.1) {
      this.selectedRatio = '4:3';
    } else if (Math.abs(this.aspectRatio - 16/9) < 0.1) {
      this.selectedRatio = '16:9';
    }
  },
  methods: {
    // 显示相机
    showCamera() {
      // #ifdef APP-PLUS || MP
      uni.chooseImage({
        count: 1,
        sourceType: ['camera'],
        success: (res) => {
          this.handleImageSelected(res.tempFilePaths[0]);
        }
      });
      // #endif
      
      // #ifdef H5
      uni.showToast({
        title: 'H5环境无法调用相机',
        icon: 'none'
      });
      // #endif
    },
    
    // 打开相册
    showAlbum() {
      uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        success: (res) => {
          this.handleImageSelected(res.tempFilePaths[0]);
        }
      });
    },
    
    // 处理选择的图片
    handleImageSelected(path) {
      this.imagePath = path;
      this.isVisible = true;
      
      // 获取图片信息
      uni.getImageInfo({
        src: path,
        success: (info) => {
          this.imageInfo = info;
          
          setTimeout(() => {
            this.initCanvas();
          }, 100);
        },
        fail: (err) => {
          console.error('获取图片信息失败:', err);
          this.cancel();
          uni.showToast({
            title: '获取图片信息失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 初始化Canvas
    initCanvas() {
      if (!this.imagePath || !this.imageInfo) return;
      
      this.ctx = uni.createCanvasContext('photo-cropper-canvas', this);
      
      // 计算图片在Canvas中的位置，保持比例
      const imgRatio = this.imageInfo.width / this.imageInfo.height;
      let imgWidth, imgHeight;
      
      if (imgRatio > 1) {
        // 宽图
        imgWidth = this.canvasWidth;
        imgHeight = this.canvasWidth / imgRatio;
      } else {
        // 长图
        imgHeight = this.canvasHeight;
        imgWidth = this.canvasHeight * imgRatio;
        
        // 如果宽度超出画布，调整高度
        if (imgWidth > this.canvasWidth) {
          imgWidth = this.canvasWidth;
          imgHeight = this.canvasWidth / imgRatio;
        }
      }
      
      // 计算绘制图片的位置，居中显示
      const x = (this.canvasWidth - imgWidth) / 2;
      const y = (this.canvasHeight - imgHeight) / 2;
      
      // 绘制图片
      this.ctx.drawImage(this.imagePath, x, y, imgWidth, imgHeight);
      this.ctx.draw();
      
      // 计算裁剪框的初始位置和大小
      let cropWidth, cropHeight;
      
      if (this.currentRatio === 0) {
        // 自由裁剪，默认使用80%的图片区域
        cropWidth = imgWidth * 0.8;
        cropHeight = imgHeight * 0.8;
      } else {
        // 固定比例
        if (imgWidth / imgHeight > this.currentRatio) {
          // 以高度为基准
          cropHeight = imgHeight * 0.8;
          cropWidth = cropHeight * this.currentRatio;
        } else {
          // 以宽度为基准
          cropWidth = imgWidth * 0.8;
          cropHeight = cropWidth / this.currentRatio;
        }
      }
      
      // 计算裁剪框位置，居中
      const cropX = x + (imgWidth - cropWidth) / 2;
      const cropY = y + (imgHeight - cropHeight) / 2;
      
      // 设置裁剪框
      this.cropRect = {
        x: cropX,
        y: cropY,
        width: cropWidth,
        height: cropHeight,
        imgX: x,
        imgY: y,
        imgWidth: imgWidth,
        imgHeight: imgHeight
      };
      
      // 保存原始图片位置和尺寸，用于计算裁剪比例
      this.canvasToImageRatio = this.imageInfo.width / imgWidth;
    },
    
    // 设置裁剪比例
    setRatio(ratio) {
      if (ratio === 'free') {
        this.currentRatio = 0;
        this.selectedRatio = 'free';
      } else {
        this.currentRatio = ratio;
        
        if (ratio === 1) {
          this.selectedRatio = '1:1';
        } else if (Math.abs(ratio - 4/3) < 0.1) {
          this.selectedRatio = '4:3';
        } else if (Math.abs(ratio - 16/9) < 0.1) {
          this.selectedRatio = '16:9';
        }
      }
      
      // 重新调整裁剪框
      this.adjustCropFrameByRatio();
    },
    
    // 根据比例调整裁剪框
    adjustCropFrameByRatio() {
      if (!this.cropRect || !this.imageInfo) return;
      
      const imgArea = this.cropRect;
      let newWidth, newHeight;
      
      // 自由裁剪时保持当前尺寸
      if (this.currentRatio === 0) return;
      
      // 计算新的裁剪框尺寸
      if (this.cropRect.width / this.cropRect.height > this.currentRatio) {
        // 以高度为基准
        newHeight = this.cropRect.height;
        newWidth = newHeight * this.currentRatio;
      } else {
        // 以宽度为基准
        newWidth = this.cropRect.width;
        newHeight = newWidth / this.currentRatio;
      }
      
      // 确保裁剪框不会超出图片区域
      if (newWidth > imgArea.imgWidth) {
        newWidth = imgArea.imgWidth;
        newHeight = newWidth / this.currentRatio;
      }
      
      if (newHeight > imgArea.imgHeight) {
        newHeight = imgArea.imgHeight;
        newWidth = newHeight * this.currentRatio;
      }
      
      // 更新裁剪框位置，保持居中
      const newX = this.cropRect.x + (this.cropRect.width - newWidth) / 2;
      const newY = this.cropRect.y + (this.cropRect.height - newHeight) / 2;
      
      // 确保不会超出图片边界
      let finalX = Math.max(imgArea.imgX, newX);
      finalX = Math.min(finalX, imgArea.imgX + imgArea.imgWidth - newWidth);
      
      let finalY = Math.max(imgArea.imgY, newY);
      finalY = Math.min(finalY, imgArea.imgY + imgArea.imgHeight - newHeight);
      
      // 更新裁剪框
      this.cropRect.x = finalX;
      this.cropRect.y = finalY;
      this.cropRect.width = newWidth;
      this.cropRect.height = newHeight;
    },
    
    // 处理拖动区域的触摸开始
    handleDragStart(e) {
      e.stopPropagation(); // 阻止事件冒泡
      
      // 防抖，避免快速连续触发
      const now = Date.now();
      if (now - this.lastTouchTime < 100) return;
      this.lastTouchTime = now;
      
      const touch = e.touches[0];
      this.isDragging = true;
      this.touchType = 'frame';
      this.dragStartX = touch.clientX || touch.x;
      this.dragStartY = touch.clientY || touch.y;
      this.initialCropRect = {...this.cropRect};
      
      // 添加触觉反馈
      this.vibrate();
      
      // 阻止后续的touchmove事件传播到canvas
      this.preventTouchMove = true;
    },
    
    // 处理角点的触摸开始
    handleCornerStart(corner) {
      return function(e) {
        e.stopPropagation(); // 阻止事件冒泡
        
        // 防抖，避免快速连续触发
        const now = Date.now();
        if (now - this.lastTouchTime < 100) return;
        this.lastTouchTime = now;
        
        const touch = e.touches[0];
        this.isDragging = true;
        this.touchType = 'corner';
        this.activeCorner = corner;
        this.dragStartX = touch.clientX || touch.x;
        this.dragStartY = touch.clientY || touch.y;
        this.initialCropRect = {...this.cropRect};
        
        // 添加触觉反馈
        this.vibrate();
        
        // 阻止后续的touchmove事件传播到canvas
        this.preventTouchMove = true;
      }.bind(this);
    },
    
    // 触觉反馈函数
    vibrate() {
      // #ifdef APP-PLUS
      uni.vibrateShort({
        success: function () {
          console.log('震动成功');
        }
      });
      // #endif
    },
    
    // 触摸开始事件 - 用于画布的触摸
    touchStart(e) {
      // 如果已经在拖动中，不处理
      if (this.isDragging) return;
      
      const touch = e.touches[0];
      const x = touch.x;
      const y = touch.y;
      
      // 检查是否在裁剪框外
      if (!this.isPointInCropFrame(x, y)) {
        // 如果点击在框架外部，可以考虑移动框架到该位置
        return;
      }
    },
    
    // 触摸移动事件
    touchMove(e) {
      if (!this.isDragging) return;
      
      // 如果当前的操作处于阻止状态，不继续处理
      if (this.preventTouchMove) {
        this.preventTouchMove = false;
        return;
      }
      
      const touch = e.touches[0];
      let x = touch.clientX;
      let y = touch.clientY;
      
      // 兼容处理不同平台的差异
      if (!x && touch.x) x = touch.x;
      if (!y && touch.y) y = touch.y;
      
      const dx = x - this.dragStartX;
      const dy = y - this.dragStartY;
      
      // 添加最小移动距离阈值，避免微小抖动
      if (Math.abs(dx) < 3 && Math.abs(dy) < 3) return;
      
      if (this.touchType === 'frame') {
        // 移动裁剪框
        let newX = this.initialCropRect.x + dx;
        let newY = this.initialCropRect.y + dy;
        
        // 限制不超出图片范围
        const imgArea = this.cropRect;
        newX = Math.max(imgArea.imgX, newX);
        newX = Math.min(newX, imgArea.imgX + imgArea.imgWidth - this.cropRect.width);
        
        newY = Math.max(imgArea.imgY, newY);
        newY = Math.min(newY, imgArea.imgY + imgArea.imgHeight - this.cropRect.height);
        
        this.cropRect.x = newX;
        this.cropRect.y = newY;
      } else if (this.touchType === 'corner') {
        // 增加移动倍数，提高灵敏度
        const sensitivity = 1.2;
        const adjustedDx = dx * sensitivity;
        const adjustedDy = dy * sensitivity;
        
        // 调整裁剪框大小
        let newX = this.initialCropRect.x;
        let newY = this.initialCropRect.y;
        let newWidth = this.initialCropRect.width;
        let newHeight = this.initialCropRect.height;
        
        // 根据活动的角点调整尺寸
        switch (this.activeCorner) {
          case 'top-left':
            newX = this.initialCropRect.x + adjustedDx;
            newY = this.initialCropRect.y + adjustedDy;
            newWidth = this.initialCropRect.width - adjustedDx;
            newHeight = this.initialCropRect.height - adjustedDy;
            break;
          case 'top-right':
            newY = this.initialCropRect.y + adjustedDy;
            newWidth = this.initialCropRect.width + adjustedDx;
            newHeight = this.initialCropRect.height - adjustedDy;
            break;
          case 'bottom-left':
            newX = this.initialCropRect.x + adjustedDx;
            newWidth = this.initialCropRect.width - adjustedDx;
            newHeight = this.initialCropRect.height + adjustedDy;
            break;
          case 'bottom-right':
            newWidth = this.initialCropRect.width + adjustedDx;
            newHeight = this.initialCropRect.height + adjustedDy;
            break;
        }
        
        // 限制最小尺寸
        newWidth = Math.max(newWidth, this.minSize);
        newHeight = Math.max(newHeight, this.minSize);
        
        // 如果是固定比例，则按比例调整
        if (this.currentRatio !== 0) {
          if (this.activeCorner === 'top-left' || this.activeCorner === 'bottom-right') {
            // 以宽度为基准
            newHeight = newWidth / this.currentRatio;
          } else {
            // 以高度为基准
            newWidth = newHeight * this.currentRatio;
          }
        }
        
        // 限制不超出图片范围
        const imgArea = this.cropRect;
        
        if (this.activeCorner === 'top-left' || this.activeCorner === 'bottom-left') {
          newX = Math.max(imgArea.imgX, newX);
          if (newX + newWidth > imgArea.imgX + imgArea.imgWidth) {
            newWidth = imgArea.imgX + imgArea.imgWidth - newX;
            if (this.currentRatio !== 0) {
              newHeight = newWidth / this.currentRatio;
            }
          }
        } else {
          if (newWidth > imgArea.imgX + imgArea.imgWidth - this.cropRect.x) {
            newWidth = imgArea.imgX + imgArea.imgWidth - this.cropRect.x;
            if (this.currentRatio !== 0) {
              newHeight = newWidth / this.currentRatio;
            }
          }
        }
        
        if (this.activeCorner === 'top-left' || this.activeCorner === 'top-right') {
          newY = Math.max(imgArea.imgY, newY);
          if (newY + newHeight > imgArea.imgY + imgArea.imgHeight) {
            newHeight = imgArea.imgY + imgArea.imgHeight - newY;
            if (this.currentRatio !== 0) {
              newWidth = newHeight * this.currentRatio;
            }
          }
        } else {
          if (newHeight > imgArea.imgY + imgArea.imgHeight - this.cropRect.y) {
            newHeight = imgArea.imgY + imgArea.imgHeight - this.cropRect.y;
            if (this.currentRatio !== 0) {
              newWidth = newHeight * this.currentRatio;
            }
          }
        }
        
        // 更新裁剪框
        if (this.activeCorner === 'top-left') {
          this.cropRect.x = newX;
          this.cropRect.y = newY;
        } else if (this.activeCorner === 'top-right') {
          this.cropRect.y = newY;
        } else if (this.activeCorner === 'bottom-left') {
          this.cropRect.x = newX;
        }
        
        this.cropRect.width = newWidth;
        this.cropRect.height = newHeight;
      }
    },
    
    // 触摸结束事件
    touchEnd() {
      // 确保在操作结束时重置状态
      setTimeout(() => {
        this.isDragging = false;
        this.touchType = '';
        this.activeCorner = '';
        this.preventTouchMove = false;
      }, 50);
    },
    
    // 判断点是否在裁剪框内或附近
    isPointInCropFrame(x, y) {
      // 由于现在有专门的拖动区和角点，此方法仅用于canvas触摸事件
      return this.isPointInRect(
        x, y, 
        this.cropRect.x, 
        this.cropRect.y, 
        this.cropRect.width, 
        this.cropRect.height
      );
    },
    
    // 判断点是否在矩形内
    isPointInRect(x, y, rectX, rectY, rectWidth, rectHeight) {
      return x >= rectX && x <= rectX + rectWidth && y >= rectY && y <= rectY + rectHeight;
    },
    
    // 确认裁剪
    confirm() {
      if (!this.imagePath || !this.cropRect) {
        this.cancel();
        return;
      }
      
      // 计算实际裁剪参数（相对于原图）
      const imgArea = this.cropRect;
      const originalImageX = (this.cropRect.x - imgArea.imgX) * this.canvasToImageRatio;
      const originalImageY = (this.cropRect.y - imgArea.imgY) * this.canvasToImageRatio;
      const originalImageWidth = this.cropRect.width * this.canvasToImageRatio;
      const originalImageHeight = this.cropRect.height * this.canvasToImageRatio;
      
      // 进行裁剪
      uni.showLoading({
        title: '处理中...',
        mask: true
      });
      
      uni.canvasToTempFilePath({
        canvasId: 'photo-cropper-canvas',
        x: this.cropRect.x,
        y: this.cropRect.y,
        width: this.cropRect.width,
        height: this.cropRect.height,
        destWidth: originalImageWidth,
        destHeight: originalImageHeight,
        fileType: 'jpg',
        quality: this.quality,
        success: (res) => {
          uni.hideLoading();
          this.isVisible = false;
          this.$emit('success', {
            tempFilePath: res.tempFilePath,
            width: originalImageWidth,
            height: originalImageHeight
          });
          
          // 清理资源
          this.reset();
        },
        fail: (err) => {
          uni.hideLoading();
          console.error('裁剪失败:', err);
          uni.showToast({
            title: '裁剪失败',
            icon: 'none'
          });
        }
      }, this);
    },
    
    // 取消裁剪
    cancel() {
      this.isVisible = false;
      this.reset();
      this.$emit('cancel');
    },
    
    // 重置状态
    reset() {
      this.imagePath = '';
      this.imageInfo = null;
      this.cropRect = {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      };
      this.ctx = null;
      this.isDragging = false;
      this.touchType = '';
      this.activeCorner = '';
      this.preventTouchMove = false;
    }
  }
}
</script>

<style lang="scss">
.photo-cropper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  
  &--visible {
    opacity: 1;
    pointer-events: auto;
  }
  
  &__mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
  }
  
  &__container {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    
    .photo-cropper__title {
      color: #fff;
      font-size: 34rpx;
      font-weight: 500;
    }
  }
  
  &__content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }
  
  &__canvas-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__canvas {
    background-color: #000;
  }
  
  // 裁剪框样式
  .crop-frame {
    position: absolute;
    border: 3rpx solid #fff;
    box-sizing: border-box;
    box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.5);
    transition: border-color 0.2s;
    
    &.active-dragging {
      border-color: #4A90E2;
    }
    
    // 中央拖动区域
    .drag-area {
      position: absolute;
      top: 60rpx;
      left: 60rpx;
      right: 60rpx;
      bottom: 60rpx;
      cursor: move;
      background-color: rgba(255, 255, 255, 0.05);
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:active {
        background-color: rgba(74, 144, 226, 0.1);
      }
      
      // 中央拖动图标
      .drag-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.15);
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none; // 防止阻挡触摸事件传递
        
        .drag-dot {
          width: 30rpx;
          height: 30rpx;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.5);
          transition: transform 0.2s, background-color 0.2s;
        }
      }
    }
    
    // 添加网格线
    .grid-line {
      position: absolute;
      background-color: rgba(255, 255, 255, 0.5);
      
      &.horizontal {
        left: 0;
        right: 0;
        top: 50%;
        height: 2rpx;
        transform: translateY(-50%);
      }
      
      &.vertical {
        top: 0;
        bottom: 0;
        left: 50%;
        width: 2rpx;
        transform: translateX(-50%);
      }
    }
    
    // 四个角
    .corner {
      position: absolute;
      width: 60rpx; // 增大拖拽区域
      height: 60rpx;
      z-index: 20;
      
      &.active {
        .corner-icon {
          transform: scale(1.2);
          background-color: #4A90E2;
        }
      }
      
      .corner-icon {
        position: absolute;
        width: 32rpx;
        height: 32rpx;
        background-color: rgba(255, 255, 255, 0.7);
        border: 2rpx solid #fff;
        border-radius: 50%;
        transition: transform 0.2s, background-color 0.2s;
      }
      
      &.top-left {
        top: -30rpx;
        left: -30rpx;
        cursor: nw-resize;
        
        .corner-icon {
          top: 5rpx;
          left: 5rpx;
        }
      }
      
      &.top-right {
        top: -30rpx;
        right: -30rpx;
        cursor: ne-resize;
        
        .corner-icon {
          top: 5rpx;
          right: 5rpx;
        }
      }
      
      &.bottom-left {
        bottom: -30rpx;
        left: -30rpx;
        cursor: sw-resize;
        
        .corner-icon {
          bottom: 5rpx;
          left: 5rpx;
        }
      }
      
      &.bottom-right {
        bottom: -30rpx;
        right: -30rpx;
        cursor: se-resize;
        
        .corner-icon {
          bottom: 5rpx;
          right: 5rpx;
        }
      }
    }
    
    // 边框
    .border {
      position: absolute;
      background-color: transparent;
      
      &.top {
        top: 0;
        left: 0;
        right: 0;
        height: 60rpx;
      }
      
      &.right {
        top: 0;
        right: 0;
        bottom: 0;
        width: 60rpx;
      }
      
      &.bottom {
        bottom: 0;
        left: 0;
        right: 0;
        height: 60rpx;
      }
      
      &.left {
        top: 0;
        left: 0;
        bottom: 0;
        width: 60rpx;
      }
    }
  }
  
  // 操作提示
  .crop-hint {
    position: absolute;
    bottom: 20rpx;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 10rpx 30rpx;
    border-radius: 30rpx;
    font-size: 24rpx;
    text-align: center;
    z-index: 100;
  }
  
  // 当前操作模式提示
  .mode-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(74, 144, 226, 0.8);
    color: #fff;
    padding: 15rpx 40rpx;
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: bold;
    text-align: center;
    z-index: 100;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
  }
  
  // 底部操作栏
  &__actions {
    height: 180rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx;
    padding-bottom: env(safe-area-inset-bottom, 20rpx);
    background-color: rgba(0, 0, 0, 0.8);
    
    .action-btn {
      width: 160rpx;
      height: 80rpx;
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30rpx;
      background-color: transparent;
      
      &::after {
        border: none;
      }
      
      &.cancel-btn {
        color: #fff;
        border: 2rpx solid rgba(255, 255, 255, 0.6);
        
        &:active {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
      
      &.confirm-btn {
        color: #fff;
        background: linear-gradient(135deg, #4A90E2, #5B6EF9);
        
        &:active {
          opacity: 0.9;
        }
      }
    }
    
    .action-ratio {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .ratio-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 24rpx;
        margin-bottom: 10rpx;
      }
      
      .ratio-options {
        display: flex;
        gap: 15rpx;
        
        .ratio-option {
          padding: 10rpx 20rpx;
          border-radius: 20rpx;
          font-size: 26rpx;
          color: #fff;
          background-color: rgba(255, 255, 255, 0.1);
          
          &.active {
            background-color: rgba(91, 110, 249, 0.8);
          }
          
          &:active {
            opacity: 0.8;
          }
        }
      }
    }
  }
}
</style> 