<template>
	<view class="carousel">
		<swiper circular="true" duration="400" touchable @change="swiperchange" class="swiper" :style="{'height':height}">
			<swiper-item class="swiper-item" style="background-color: #000;" v-if="goodsData.videos">
				<view class="video-container">
					<!-- 视频封面图层 -->
					<view class="video-cover" v-if="!isPlaying || currentIndex !== 1">
						<image
							class="video-poster"
							:src="goodsData.imgList[0]"
							mode="aspectFit"
						></image>
						<!-- 大播放按钮 -->
						<view v-if="!isPlaying" class="play-center" @click="togglePlay">
							<view class="play-icon-wrapper">
								<image src="/static/image/icons/play.png" mode="aspectFit" class="play-icon"></image>
							</view>
						</view>
					</view>
					
					<DomVideoPlayer
						v-show="isPlaying && currentIndex === 1"
						ref='domVideoPlayer'
						:controls="false"
						:autoplay="false"
						:loop="true"
						:src="goodsData.videos"
						objectFit="contain"
						@timeupdate="onTimeUpdate"
						@durationchange="onDurationChange"
						@play="onPlay"
						@pause="onPause"
						@loadedmetadata="onVideoLoaded"
						@error="onVideoError"
						@canplay="onVideoCanPlay"
					/>
					
					<!-- 自定义控制器 -->
					<view class="custom-controls" v-show="isPlaying && currentIndex === 1 && playing">
						<!-- 播放/暂停按钮 -->
						<view class="play-btn" @click="togglePlay">
							<u-icon :name="playing ? 'pause' : 'play-right'" size="24" color="#fff"></u-icon>
						</view>
						<!-- 进度条 -->
						<view class="progress-bar">
							<u-slider 
								v-model="progress" 
								class="custom-slider" 
								min="0"
								max="100"
								:blockSize="14"
								activeColor="#00C1CC" inactiveColor="#c0c4cc"
								@change="onSliderChange"
								@moving="onSliderMoving"
								:blockStyle="{
									backgroundColor: '#fff',
									borderRadius: '50%',
									boxShadow: '0 0 4rpx rgba(0,0,0,0.3)'
								}"
							></u-slider>
							<view class="time">
								<text>{{formatTime(currentTime)}}</text>
								<text>/</text>
								<text>{{formatTime(duration)}}</text>
							</view>
						</view>
						
						<!-- 全屏按钮 -->
						<view class="fullscreen-btn" @click.stop="enterFullScreen">
							<image src="/static/image/icons/quanping.png" mode=""></image>
						</view>
					</view>
				</view>
			</swiper-item>
			<swiper-item 
				v-for="(item, index) in displayImages" 
				:key="index" 
				class="swiper-item" 
				@click="preview(displayImages,index)"
			>
				<view class="image-wrapper">
					<image :src="item" class="loaded" mode="widthFix" :style="{'height':height}" ></image>
				</view>
			</swiper-item>
		</swiper>
		<view class="dots" :style="{'top': statusBarHeight + 'upx'}" v-if="shouldShowDots">
			<block v-if="goodsData.videos && goodsData.imgList && goodsData.imgList.length > 1">
				<text :class="{'active': currentIndex === 1}">视频</text>
				<text class="separator">|</text>
				<text :class="{'active': currentIndex > 1}">{{currentIndex > 1 ? getImageIndexText() : '图集'}}</text>
			</block>
			<block v-else-if="!goodsData.videos && goodsData.imgList && goodsData.imgList.length">
				<text class="active">{{getImageIndexText()}}</text>
			</block>
			<block v-else-if="goodsData.videos && (!goodsData.imgList || goodsData.imgList.length <= 1)">
				<text class="active">视频</text>
			</block>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentIndex: 1,
				showVideo: false,
				minutes:'00',
				seconds:'18',
				isH5: false ,//针对H5做处理
				platform:'',
				videos:'',
				currentTime:0,
				duration:0,
				playing: false,
				progress: 0,
				controls: false,
				isPlaying: false,
				statusBarHeight: 0, // 默认状态栏高度
				isAndroid: false, // 是否是安卓系统
				isVideoReady: false,
				userPaused: false, // 新增：标记用户是否主动暂停
				hasFirstPlay: false, // 新增：是否已经第一次播放过
				isInitializing: true, // 新增：标记是否在初始化阶段
				isFullscreening: false, // 新增：标记是否正在执行全屏操作
			}
		},
		computed: {
			shouldShowDots() {
				return (this.goodsData.videos && this.goodsData.imgList && this.goodsData.imgList.length >= 1) || 
					   (!this.goodsData.videos && this.goodsData.imgList && this.goodsData.imgList.length > 0);
			},
			// 处理显示的图片列表
			displayImages() {
				if(this.goodsData.videos) {
					// 如果有视频，从第二张开始显示
					return this.goodsData.imgList.slice(1)
				} else {
					// 如果没有视频，显示所有图片
					return this.goodsData.imgList
				}
			}
		},
		props: {
			goodsData: {
				type: Object
			},
			height:{
				type:String,
				default:'750upx'
			},
			statusBar:{
				type:Boolean,
				default:false
			}
		},
		created() {
			this.platform = uni.getSystemInfoSync().platform  //判断当前是安卓还是ios
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync()
			if(!this.statusBar){
				this.statusBarHeight = systemInfo.statusBarHeight + 44 // 44是导航栏的高度
			}
			this.isAndroid = this.platform.toLowerCase() === 'android'
			console.log(this.goodsData)
		},
		methods: {
			play(){
				this.$refs.domVideoPlayer.play()
			},
			pause(){
				this.$refs.domVideoPlayer.pause()
			},
			stop(){
				this.$refs.domVideoPlayer.stop()
			},
			onshowVideo() {
				// #ifndef H5
				this.showVideo = true
				// #endif
				// #ifdef H5
				// h5 在真机上测试
				if(this.platform == 'android'){ //判断是安卓还是ios来对视频做适配
					this.isH5 = true
				}else{
					this.showVideo = true
				}
				// #endif
				this.$emit('setShowVideo',this.showVideo,this.isH5)
			},
			swiperchange(e) {
				this.currentIndex = e.detail.current + 1;
				const idx = e.detail.current;
				
				if (0 === idx && this.goodsData.videos) {
					// 视频项
					if(this.hasFirstPlay && !this.userPaused) {
						// 只有在用户曾经播放过，且不是用户主动暂停的情况下，才自动播放
						this.startPlay();
					}
				} else {
					// 非视频项，暂停视频
					if(this.goodsData.videos) {
						this.$refs.domVideoPlayer.pause();
						this.playing = false;
						this.isPlaying = false;
					}
				}
			},
			preview(imgs, index) { //预览图片
			console.log(123)
				const previewImages = this.goodsData.videos ? this.goodsData.imgList.slice(1) : this.goodsData.imgList
				uni.previewImage({
					current: index,
					urls: previewImages,
					showmenu:false,
					longPressActions: false 
				})
			},
			// 格式化时间
			formatTime(time) {
				const minutes = Math.floor(time / 60)
				const seconds = Math.floor(time % 60)
				return `${minutes.toString().padStart(2,'0')}:${seconds.toString().padStart(2,'0')}`
			},
			
			// 播放/暂停切换
			async togglePlay() {
				try {
					if(this.playing) {
						await this.$refs.domVideoPlayer.pause();
						this.playing = false;
						this.isPlaying = false;
						this.userPaused = true;
					} else {
						this.hasFirstPlay = true; // 标记已经第一次播放
						this.userPaused = false;
						await this.startPlay();
					}
				} catch(err) {
					console.log('播放控制错误:', err);
					this.isPlaying = false;
					this.playing = false;
				}
			},
			
			// 进度条变化
			onSliderChange(e) {
				const time = (e / 100) * this.duration
				this.$refs.domVideoPlayer.toSeek(time)
			},
			
			// 进度条移动中
			onSliderMoving(value) {
				// 更新进度，但不跳转
				this.progress = value
			},
			
			// 进入全屏
			enterFullScreen() {
				this.isFullscreening = true;
				this.$refs.domVideoPlayer.fullScreen();
				// 添加延时，确保全屏操作完成后重置标记
				// setTimeout(() => {
				// 	this.isFullscreening = false;
				// }, 1000);
			},
			
			// 视频播放状态监听
			onPlay() {
				// 只在非初始化阶段响应播放事件
				if(!this.isInitializing) {
					this.playing = true;
					this.isPlaying = true;
				} else {
					// 如果是初始化阶段，强制暂停
					this.$refs.domVideoPlayer.pause();
				}
			},
			
			onPause() {
				// 只在非初始化阶段且非全屏操作时响应暂停事件
				if(!this.isInitializing && !this.isFullscreening) {
					this.playing = false;
					if(this.isAndroid) {
						this.isPlaying = false;
					}
				}
			},
			
			// 更新进度
			onTimeUpdate(val) {
				this.currentTime = val
				this.progress = (val / this.duration) * 100
			},
			
			onDurationChange(val) {
				this.duration = val
			},
			
			// 获取图片索引文本
			getImageIndexText() {
				if(!this.goodsData?.imgList?.length) {
					return '图集';
				}
				
				if(!this.goodsData.videos) {
					return `图集 ${this.currentIndex}/${this.goodsData.imgList.length}`;
				} else {
					if(this.displayImages.length === 0) {
						return '图集';
					}
					return `图集 ${this.currentIndex-1}/${this.displayImages.length}`;
				}
			},
			
			// 重置视频状态
			resetVideoState() {
				this.playing = false
				this.isPlaying = false
				this.progress = 0
				this.currentTime = 0
				if(this.$refs.domVideoPlayer) {
					this.$refs.domVideoPlayer.pause()
					// this.$refs.domVideoPlayer.toSeek(0)
				}
			},
			
			onVideoError(err) {
				console.log('视频加载错误:', err);
				this.isPlaying = false;
				this.playing = false;
			},
			
			onVideoLoaded() {
				console.log('视频加载完成');
				this.isVideoReady = true;
				// 确保视频初始状态是暂停的
				if(this.isInitializing) {
					this.$refs.domVideoPlayer.pause();
					setTimeout(() => {
						this.isInitializing = false;
					}, 100);
				}
			},
			
			onVideoCanPlay() {
				// 视频可以播放时，如果是初始化阶段，确保视频是暂停的
				if(this.isInitializing) {
					this.$refs.domVideoPlayer.pause();
				}
			},
			
			async startPlay() {
				if(!this.isVideoReady || this.isInitializing) return;
				try {
					this.isPlaying = true;
					await this.$nextTick();
					await this.$refs.domVideoPlayer.play();
					this.playing = true;
				} catch(err) {
					console.log('播放失败:', err);
					this.isPlaying = false;
					this.playing = false;
				}
			},
		}
	}
</script>
<style scoped lang="scss">
	.carousel {
		position: relative;
		overflow: hidden;
		.dots {
			height: 40upx;
			line-height: 40upx;
			font-size: 24upx;
			text-align: center;
			background-color: rgba(0, 0, 0, .2);
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			z-index: 1;
			color: rgba(255, 255, 255, 0.6);
			padding: 0 20upx;
			border-radius: 20upx;
			display: flex;
			align-items: center;
			
			text {
				font-weight: 400;
				&.active {
					color: #FFFFFF;
				}
				&.separator {
					margin: 0 10rpx;
					color: rgba(255, 255, 255, 0.3);
				}
			}
		}
	}

	.swiper {
		width: 100vw;
		height: 750upx;
		z-index: 100;
	}

	.loaded {
		width: 100vw;
		height: 750upx;
		display: block;
	}

	.video_btn {
		// width: 165upx;
		// height: 60upx;
		// background: url(data:image/png;base64,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) no-repeat;
		// background-size: 100% 100%;
		position: absolute;
		bottom: 50%;
		left: 50%;
		// transform: translateX(-50%);
		transform: translateY(50%) translateX(-50%);
		text-align: right;
		line-height: 60upx;
		padding: 0 20upx;
		font-size: 24upx;
		letter-spacing: 2upx;

		&:active {
			opacity: .8;
		}
	}

	.video-container {
		position: relative;
		width: 100%;
		height: 100%;
		
		.video-cover {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 2;
			background-color: #000;
		}
		
		.video-poster {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
			background-color: #000;
		}
		
		.play-center {
			position: absolute;
			top: 50%;
			left: 50%;
			margin-left: -60rpx;
			margin-top: -60rpx;
			z-index: 100;
			width: 120rpx;
			height: 120rpx;
			// background: rgba(0, 0, 0, 0.3);
			border-radius: 50%;
			overflow: hidden;
			
			.play-icon-wrapper {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.play-icon {
					width: 120rpx;
					height: 120rpx;
					flex: none;
					max-width: 100%;
					max-height: 100%;
					object-fit: contain;
				}
			}
		}
		
		.custom-controls {
			position: absolute;
			left: 0;
			right: 0;
			bottom: 0upx;
			// height: 60rpx;
			background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
			display: flex;
			align-items: center;
			padding: 0 20rpx;
			
			.play-btn {
				width: 60rpx;
				height: 70rpx;
				// display: flex;
				// align-items: flex-start;
				// justify-content: flex-start;
			}
			
			.progress-bar {
				width: 100%;
				display: flex;
				flex-direction: column;
				margin: 0 0rpx;
				position: relative;
				z-index: 1000;
				
				.custom-slider {
					margin: 0;
				}
				
				.time {
					font-size: 24rpx;
					color: #fff;
					text-align: right;
					margin-top: -20rpx;
					margin-bottom: 10upx;
					margin-right: 30rpx;
					text {
						margin: 0 4rpx;
					}
				}
			}
			
			.fullscreen-btn {
				width: 60rpx;
				height: 80rpx;
				// display: flex;
				// align-items: center;
				// justify-content: center;
				image{
					width: 60rpx;
					height: 60rpx;
				}
			}
		}
	}

	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.2s ease;
	}
	.fade-enter-from,
	.fade-leave-to {
		opacity: 0;
	}
</style>
