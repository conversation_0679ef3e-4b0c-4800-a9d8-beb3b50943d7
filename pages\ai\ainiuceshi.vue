<template>
  <view class="container">
    <view class="header">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <u-icon name="arrow-left" color="#fff" size="25"></u-icon>
        </view>
        <view class="title-box">
          <text class="title">英语陪练</text>
          <text class="desc" style="display: block;">基于AI大模型，为您提供英语陪练服务</text>
        </view>
        <view class="phone-btn" @click="createRoom">
          <u-icon name="phone" color="#fff" size="25"></u-icon>
        </view>
      </view>
    </view>
    
    <view class="chat-container">
      <scroll-view 
        scroll-y="true" 
        class="chat-list"
        :scroll-top="scrollTop"
        :scroll-with-animation="true"
      >
        <view class="chat-section" v-if="chatHistory.length > 0">
          <view 
            class="chat-item" 
            :class="message.role"
            v-for="(message, index) in chatHistory" 
            :key="index"
          >
            <view class="message-wrapper">
              <view class="avatar">
                <image 
                  :src="message.role === 'assistant' ? '/static/science-captain-avatar.png' : '/static/user-avatar.png'"
                  mode="aspectFill"
                ></image>
              </view>
              <view class="message">
                <view class="message-content">
                  <template v-if="message.role === 'assistant'">
                    <ua2-markdown 
                      :source="message.content" 
                      :show-line="true"
                    ></ua2-markdown>
                    <view class="typing-indicator" v-if="message.isTyping">
                      <text class="dot">.</text>
                      <text class="dot">.</text>
                      <text class="dot">.</text>
                    </view>
                  </template>
                  <template v-else>
                    <template v-if="message.isVoice">
                      <view class="voice-message" @click="playVoice(message)">
                        <view class="voice-content dis-ali">
                          <u-icon :name="message.isPlaying ? 'pause' : 'play-right'" color="#fff" size="20"></u-icon>
                          <text class="duration com-fff ml10" >{{message.duration}}''</text>
                        </view>
                      </view>
                    </template>
                    <text v-else class="message-text" :class="{'typing': message.isTyping}">{{message.content}}</text>
                  </template>
                </view>
                <view class="message-actions" v-if="message.role === 'assistant' && !message.isTyping">
                  <button class="btn-voice" v-if="message.audioUrl" @click="playAssistantVoice(message)">
                    <u-icon :name="message.isPlaying ? 'pause' : 'volume'" color="#a7a7a7" size="22"></u-icon>
                  </button>
                  <button class="btn-copy" @click="copyMessage(message.content)">
                    <u-icon name="file-text" color="#a7a7a7" size="22"></u-icon>
                  </button>
                  <button class="btn-delete" @click="deleteMessage(index)">
                    <u-icon name="trash" color="#a7a7a7" size="22"></u-icon>
                  </button>
                </view>
                <view class="message-actions" v-if="message.role === 'user' && message.canRetry">
                  <button class="btn-retry" @click="retryChat(message.content)">
                    <u-icon name="reload" color="#a7a7a7" size="22"></u-icon>
                  </button>
                  <button class="btn-delete" @click="deleteMessage(index)">
                    <u-icon name="trash" color="#a7a7a7" size="22"></u-icon>
                  </button>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="empty-state" v-else>
          <image src="/static/empty-state.png" mode="aspectFit" class="empty-image"></image>
        </view>

        <view class="cancel-btn-wrapper" v-if="chatting">
          <button class="btn-stop" @click="cancelChat">
            <u-icon name="close" color="#fff" size="18" style="margin-right: 6rpx;"></u-icon>
            <text>停止响应</text>
          </button>
        </view>
      </scroll-view>
    </view>
    <!-- 录音弹窗 -->
    <view class="record-popup" v-if="isRecording">
      <view class="inner-content">
        <view class="title">正在录音</view>
        <view class="voice-line-wrap">
          <view class="voice-line one"></view>
          <view class="voice-line two"></view>
          <view class="voice-line three"></view>
          <view class="voice-line four"></view>
          <view class="voice-line five"></view>
          <view class="voice-line six"></view>
          <view class="voice-line seven"></view>
          <view class="voice-line six"></view>
          <view class="voice-line five"></view>
          <view class="voice-line four"></view>
          <view class="voice-line three"></view>
          <view class="voice-line two"></view>
          <view class="voice-line one"></view>
        </view>
        <view class="tips">{{ isCancelling ? '松手取消录音' : '上滑取消录音' }}</view>
      </view>
    </view>
    <view class="input-section">
      <view class="input-box" v-if="inputMode === 'text'">
        <view class="voice-btn" @click="switchInputMode">
          <u-icon name="mic" color="#666" size="30"></u-icon>
        </view>
        <u--textarea 
          type="text" 
          class="url-inputerea"  
          v-model="userInput" 
          placeholder="请输入您想问的问题" 
          :disabled="chatting"
          @confirm="startChat"
          @keyboardheightchange="onKeyboardheightchange"  
          autoHeight 
        ></u--textarea>
        <button 
          class="btn-extract" 
          :loading="chatting"
          :disabled="!userInput || chatting" 
          @click="startChat"
        >
          <u-icon :name="chatting?'':'arrow-right'" color="#fff" size="22"></u-icon>
        </button>
      </view>
      <view class="voice-input-box" v-else>
        <view class="voice-btn" @click="switchInputMode">
			<image src="/static/keyboard-fill.png" mode="" style="width: 60upx;height: 60upx;"></image>
          <!-- <u-icon name="keyboard" color="#666" size="30"></u-icon> -->
        </view>
        <view 
          class="voice-record-btn" 
          @touchstart="startRecording" 
          @touchmove="touchMove"
          @touchend="stopRecording"
          :class="{'recording': isRecording}"
        >
          {{ isRecording ? '松开结束' : '按住说话' }}
        </view>
      </view>
      <mumu-recorder ref='recorder' @success='handlerSuccess' @error='handlerError'></mumu-recorder>
    </view>
  </view>
</template>

<script>
import { extractVideoText, cancelConversation, createRoom, transcribeAudio, getVoiceList, textToSpeech } from '@/utils/coze.js'
import ua2Markdown from '@/uni_modules/ua2-markdown/ua-markdown.vue'
import MumuRecorder from '@/uni_modules/mumu-recorder/components/mumu-recorder/mumu-recorder.vue'

export default {
  components: {
    ua2Markdown,
    MumuRecorder
  },
  data() {
    return {
      userInput: '',
      chatting: false,
      chatHistory: [],
      scrollTop: 0,
      currentChatId: '',
      currentConversationId: '',
      botId: '7472654572633686066', // 科学队长的botId
      isRecording: false,
      isCancelling: false,
      startTouchY: 0,
      recorder: null,
      recorderStatus: false,
      currentPlayingMessage: null,
      messageIdCounter: 0,
      // H5录音相关
      audioContext: null,
      audioStream: null,
      chunks: [],
      // 平台判断
      isH5: false,
      isMobile: false,
      inputMode: 'text',
      audioContextMap: new Map(),
      // 房间相关
      roomId: '',
      isConnecting: false,
      // 语音合成相关
      voiceList: [], // 存储音色列表
      currentVoiceId: '', // 当前使用的音色ID
      isPlayingTTS: false, // 是否正在播放TTS
      ttsAudioContext: null, // TTS音频上下文
      assistantAudioMap: new Map(), // 存储助手的语音URL
      fromSettings: false,
    }
  },
  async onLoad() {
    // #ifdef H5
    this.isH5 = true;
    this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IOS/i.test(navigator.userAgent);
    // #endif

    // #ifndef H5
    // 初始化录音管理器
    this.recorderManager = uni.getRecorderManager();
    
    // 监听录音结束事件
    this.recorderManager.onStop((res) => {
      const { tempFilePath } = res;
      this.uploadVoiceFile(tempFilePath);
    });
    // #endif

    // 获取音色列表
    try {
      const result = await getVoiceList();
      this.voiceList = result.voice_list || [];
      if(this.voiceList.length > 0) {
        // 随机选择一个音色
        const randomIndex = Math.floor(Math.random() * this.voiceList.length);
        this.currentVoiceId = this.voiceList[randomIndex].voice_id;
      }
    } catch(error) {
      console.error('获取音色列表失败:', error);
    }
  },
  onShow() {
    // 如果从设置页面返回，尝试重新检查权限
    if (this.fromSettings) {
      console.log('从设置页面返回，重新检查权限');
      this.fromSettings = false;
      
      // 等待短暂时间后检查权限
      setTimeout(() => {
        if (this.$refs.recorder) {
          // 直接调用recorder的权限检查方法
          this.$refs.recorder.checkAppPermission(true);
        }
      }, 500);
    }
  },
  methods: {
    async startChat() {
      if(!this.userInput || this.chatting) return
      
      this.chatting = true
      const currentInput = this.userInput
      this.userInput = ''
      
      try {
        console.log('开始发送对话请求...');
        
        const userMessage = {
          role: 'user',
          content: currentInput,
          canRetry: false
        }
        this.chatHistory.push(userMessage)
        this.scrollToBottom()
        
        const assistantMessage = {
          role: 'assistant',
          content: '',
          isTyping: true
        }
        this.chatHistory.push(assistantMessage)
        
        console.log('调用AI接口，发送内容:', currentInput);
        
        const { content } = await extractVideoText(
          currentInput,
          this.chatHistory.slice(0, -1),
          ({ chat_id, conversation_id, content }) => {
            console.log('流式返回:', { chat_id, conversation_id, has_content: !!content });
            
            if (chat_id && conversation_id) {
              this.currentChatId = chat_id
              this.currentConversationId = conversation_id
            }
            
            if (content) {
              assistantMessage.content = content
              this.scrollToBottom()
            }
          },
          this.botId
        )
        
        console.log('对话请求完成');
        
        assistantMessage.isTyping = false
        this.chatting = false
        this.currentChatId = ''
        this.currentConversationId = ''
        
        // 如果有音色ID，进行语音合成
        if(this.currentVoiceId && content) {
          try {
            console.log('开始语音合成...');
            const ttsResult = await textToSpeech(content, this.currentVoiceId);
            // 立即更新消息对象的audioUrl和播放状态
            this.$set(assistantMessage, 'audioUrl', this.isH5 ? ttsResult.audioUrl : ttsResult.tempFilePath);
            this.$set(assistantMessage, 'isPlaying', false);
            console.log('语音合成结果:', ttsResult);
            // 强制更新视图
            this.$forceUpdate();
            
            // 自动播放语音
            this.playAssistantVoice(assistantMessage);
            
          } catch(error) {
            console.error('语音合成失败:', error);
          }
        }
        
        this.scrollToBottom()
      } catch(error) {
        console.error('聊天失败:', error)
        
        // 详细记录错误信息
        if (error.response) {
          console.error('API返回错误:', error.response.status, error.response.data);
        }
        
        // 移除正在输入的消息
        if (this.chatHistory.length > 0 && this.chatHistory[this.chatHistory.length - 1].role === 'assistant') {
          this.chatHistory.pop()
        }
        
        // 恢复用户输入
        this.userInput = currentInput
        
        // 允许重试
        if (this.chatHistory.length > 0) {
          const lastMessage = this.chatHistory[this.chatHistory.length - 1]
          if (lastMessage.role === 'user') {
            lastMessage.canRetry = true
          }
        }
        
        // 处理特定错误
        if (error.code === 4015) {
          uni.showModal({
            title: '服务未启用',
            content: '科学队长服务暂未启用，请稍后再试',
            showCancel: false
          })
        } else if (error.message !== '对话已取消') {
          // 其他错误显示通用提示
          uni.showModal({
            title: '对话失败',
            content: '对话请求失败，请检查网络或服务器状态后重试',
            showCancel: false
          })
        }
      } finally {
        // 重置状态
        this.chatting = false
        this.currentChatId = ''
        this.currentConversationId = ''
      }
    },
    
    startRecording(e) {
      this.startTouchY = e.touches[0].clientY;
      this.isCancelling = false;
      
      // 如果是H5环境下的移动端，使用mumu-recorder
      if (this.isH5) {
        this.isRecording = true;
        this.$refs.recorder.start();
        // 震动反馈
        if (navigator.vibrate) {
          navigator.vibrate(50);
        }
        return;
      }
      
      // #ifndef H5
      this.isRecording = true;
      this.recorderManager.start({
        duration: 60000,
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 48000,
        format: 'mp3'
      });
      
      // 添加onStart事件处理
      this.recorderManager.onStart(() => {
        console.log('录音开始');
        uni.showToast({
          title: '开始录音',
          icon: 'none'
        });
      });
      
      // 添加onError事件处理
      this.recorderManager.onError((err) => {
        console.error('录音错误:', err);
        this.isRecording = false;
        
        uni.showToast({
          title: '录音失败，请检查权限设置',
          icon: 'none',
          duration: 2000
        });
      });
      // #endif
    },
    
    touchMove(e) {
      if (!this.isRecording) return;
      
      const moveY = e.touches[0].clientY - this.startTouchY;
      if (moveY < -50) { // 上滑超过50像素
        this.isCancelling = true;
        // 震动反馈
        if (navigator.vibrate) {
          navigator.vibrate(50);
        }
      } else {
        this.isCancelling = false;
      }
    },
    
    stopRecording(e) {
      if (!this.isRecording) return;
      
      // 如果是H5环境
      if (this.isH5) {
        if (this.isCancelling) {
          // 取消录音
          this.isRecording = false;
          this.isCancelling = false;
          uni.showToast({
            title: '已取消录音',
            icon: 'none'
          });
        } else {
          this.$refs.recorder.stop();
        }
        return;
      }
      
      // #ifndef H5
      this.isRecording = false;
      if (this.isCancelling) {
        this.recorderManager.stop();
        uni.showToast({
          title: '已取消录音',
          icon: 'none'
        });
      } else {
        this.recorderManager.stop();
        uni.showToast({
          title: '录音结束',
          icon: 'none'
        });
      }
      // #endif
    },

    // 处理录音成功的回调
    async handlerSuccess(res) {
      this.recorder = res;
      this.isRecording = false;
      console.log('录音结果:', res);
      
      try {
        // 显示加载提示
        uni.showLoading({
          title: '正在识别语音...'
        });
        
        // 调用语音识别API
        try {
          const result = await transcribeAudio(res.localUrl);
          console.log('语音识别结果:', result);
          
          // 语音识别成功后立即关闭加载提示
          uni.hideLoading();
          
          // 设置用户输入并调用startChat
          if(result && result.text) {
            this.userInput = result.text;
            // 显示文字已识别的提示
            uni.showToast({
              title: '语音已识别',
              icon: 'success',
              duration: 1500
            });
            
            // 异步调用startChat，不等待其完成
            this.startChat();
          } else {
            throw new Error('未获取到识别结果');
          }
        } catch (error) {
          // 处理语音识别失败的情况
          console.error('语音识别失败:', error);
          uni.hideLoading();
          
          // 显示错误提示
          uni.showModal({
            title: '语音识别失败',
            content: '无法识别您的语音，是否重试？',
            confirmText: '重试',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                // 用户选择重试，重新上传录音文件
                this.retryTranscribe(this.recorder.localUrl);
              } else {
                // 用户选择取消，添加提示消息
                uni.showToast({
                  title: '已取消识别',
                  icon: 'none'
                });
              }
            }
          });
        }
        
      } catch (error) {
        console.error('处理录音失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: error.message || '语音处理失败',
          icon: 'none'
        });
      }
    },

    // 添加重试语音识别的方法
    async retryTranscribe(audioUrl) {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '正在重新识别...'
        });
        
        // 重新调用语音识别API
        const result = await transcribeAudio(audioUrl);
        console.log('重新识别结果:', result);
        
        // 识别成功
        uni.hideLoading();
        
        if(result && result.text) {
          this.userInput = result.text;
          // 显示识别成功提示
          uni.showToast({
            title: '语音已识别',
            icon: 'success',
            duration: 1500
          });
          this.startChat();
        } else {
          throw new Error('未获取到识别结果');
        }
      } catch (error) {
        console.error('重新识别失败:', error);
        uni.hideLoading();
        
        uni.showToast({
          title: '识别失败，请尝试使用文字输入',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 添加语音消息到聊天记录
    addVoiceMessage(recorder) {
      const voiceMessage = {
        id: ++this.messageIdCounter,
        role: 'user',
        isVoice: true,
        audioUrl: recorder.localUrl,
        duration: Math.round(recorder.duration),
        isPlaying: false,
        canRetry: false
      };
      this.chatHistory.push(voiceMessage);
      this.scrollToBottom();
    },
    
    // 处理录音错误的回调
    handlerError(code) {
      this.isRecording = false;
      console.log('录音组件返回错误代码:', code);
      
      switch (code) {
        case '100':
          uni.showModal({
            title: '提示',
            content: '请在HTTPS环境下使用录音功能',
            showCancel: false
          });
          break;
        case '101':
          uni.showModal({
            title: '提示',
            content: '当前设备不支持录音功能',
            showCancel: false
          });
          break;
        case '201':
          // 添加一个延迟以避免过快弹出设置对话框
          setTimeout(() => {
            uni.showModal({
              title: '权限提示',
              content: '录音权限被拒绝，需要在设置中开启麦克风权限才能使用语音功能',
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  // #ifdef H5
                  // H5环境下引导用户刷新页面
                  uni.showModal({
                    title: '提示',
                    content: '请刷新页面并允许麦克风权限',
                    showCancel: false
                  });
                  // #endif
                  
                  // #ifdef APP-PLUS
                  // APP环境下跳转到系统设置
                  if (typeof plus !== 'undefined' && plus.runtime) {
                    console.log('正在打开系统设置...');
                    plus.runtime.openURL('app-settings:');
                    
                    // 设置一个标记，表示用户已经从设置返回
                    this.fromSettings = true;
                    
                    // 监听应用恢复事件，当用户从设置页面返回时，重新检查权限
                    document.addEventListener('resume', this.checkPermissionAfterSettings);
                  } else {
                    // 兼容方案
                    uni.openSetting({
                      success: (settingRes) => {
                        console.log('打开设置页面成功');
                      }
                    });
                  }
                  // #endif
                }
              }
            });
          }, 300);
          break;
        case '500':
          uni.showModal({
            title: '提示',
            content: '录音功能内部错误，请重启应用后重试',
            showCancel: false
          });
          break;
        default:
          uni.showModal({
            title: '提示',
            content: `录音功能异常(错误码:${code})，请重试`,
            showCancel: false
          });
          break;
      }
    },
    
    // 从设置返回后检查权限
    checkPermissionAfterSettings() {
      console.log('从设置返回，重新检查权限');
      // 移除事件监听，避免重复执行
      document.removeEventListener('resume', this.checkPermissionAfterSettings);
      
      // 等待一段时间，确保设置已应用
      setTimeout(() => {
        // 尝试重新开始录音，这将触发权限检查
        if (this.isH5) {
          this.$refs.recorder.checkAppPermission(true);
        } else {
          this.recorderManager.start();
          this.recorderManager.stop();
        }
      }, 1000);
    },
    
    async uploadVoiceFile(file) {
      // 如果不是H5环境，使用小程序原生录音
      // #ifndef H5
      const voiceMessage = {
        role: 'user',
        isVoice: true,
        audioUrl: file,
        canRetry: false
      };
      this.chatHistory.push(voiceMessage);
      this.scrollToBottom();
      // #endif
    },
    
    async recognizeVoice(file) {
      // TODO: 实现语音识别逻辑
      return '语音识别示例文本';
    },
    
    async cancelChat() {
      if (!this.currentChatId || !this.currentConversationId) {
        return
      }
      
      try {
        const result = await cancelConversation(this.currentChatId, this.currentConversationId)
        if (result === true) {
          if (this.chatHistory.length > 0 && this.chatHistory[this.chatHistory.length - 1].role === 'assistant') {
            this.chatHistory.pop()
          }
          if (this.chatHistory.length > 0) {
            const lastMessage = this.chatHistory[this.chatHistory.length - 1]
            if (lastMessage.role === 'user') {
              lastMessage.canRetry = true
            }
          }
          
          uni.showToast({
            title: '已取消响应',
            icon: 'none'
          })
        } else {
          if (result.reason === 'STATUS_NOT_CANCELABLE') {
            uni.showToast({
              title: '当前状态不支持取消',
              icon: 'none'
            })
          } else {
            uni.showToast({
              title: '取消失败，请重试',
              icon: 'none'
            })
          }
        }
      } catch(error) {
        console.error('取消失败:', error)
        uni.showToast({
          title: '取消失败，请重试',
          icon: 'none'
        })
      } finally {
        this.chatting = false
        this.currentChatId = ''
        this.currentConversationId = ''
      }
    },
    
    copyMessage(content) {
      uni.setClipboardData({
        data: content,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          })
        }
      })
    },
    
    scrollToBottom() {
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this)
        query.select('.chat-section').boundingClientRect(res => {
          if (res) {
            this.scrollTop = res.height
          }
        }).exec()
      }, 200)
    },
    
    goBack() {
      uni.navigateBack({
        delta: 1
      })
    },

    onKeyboardheightchange(e) {
      this.scrollToBottom()
    },

    async retryChat(content) {
      if (this.chatting) return
      this.userInput = content
      await this.startChat()
    },

    deleteMessage(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这条消息吗？',
        success: (res) => {
          if (res.confirm) {
            if (this.chatHistory[index].role === 'user' && 
                index + 1 < this.chatHistory.length && 
                this.chatHistory[index + 1].role === 'assistant') {
              this.chatHistory.splice(index, 2)
            } else {
              if (this.chatHistory[index].role === 'assistant' && 
                  index > 0 && 
                  this.chatHistory[index - 1].role === 'user') {
                this.chatHistory.splice(index - 1, 2)
              } else {
                this.chatHistory.splice(index, 1)
              }
            }
          }
        }
      })
    },

    playVoice(message) {
      if (this.currentPlayingMessage && this.currentPlayingMessage.id !== message.id) {
        // 停止当前正在播放的语音
        const currentAudioContext = this.audioContextMap.get(this.currentPlayingMessage.id);
        if (currentAudioContext) {
          currentAudioContext.stop();
          this.currentPlayingMessage.isPlaying = false;
        }
      }

      let audioContext = this.audioContextMap.get(message.id);
      
      if (!audioContext) {
        // 创建新的音频上下文
        audioContext = uni.createInnerAudioContext();
        audioContext.src = message.audioUrl;
        
        // 监听播放结束
        audioContext.onEnded(() => {
          message.isPlaying = false;
          this.currentPlayingMessage = null;
          // 清理音频上下文
          audioContext.destroy();
          this.audioContextMap.delete(message.id);
        });
        
        // 监听错误
        audioContext.onError((res) => {
          console.error('播放错误:', res);
          uni.showToast({
            title: '播放失败',
            icon: 'none'
          });
          message.isPlaying = false;
          this.currentPlayingMessage = null;
          // 清理音频上下文
          audioContext.destroy();
          this.audioContextMap.delete(message.id);
        });
        
        this.audioContextMap.set(message.id, audioContext);
      }
      
      try {
        if (message.isPlaying) {
          audioContext.pause();
          message.isPlaying = false;
          this.currentPlayingMessage = null;
        } else {
          audioContext.play();
          message.isPlaying = true;
          this.currentPlayingMessage = message;
        }
      } catch (error) {
        console.error('播放出错:', error);
        message.isPlaying = false;
        this.currentPlayingMessage = null;
        // 清理音频上下文
        audioContext.destroy();
        this.audioContextMap.delete(message.id);
      }
    },

    // 语音播放结束
    audioEnded(message) {
      message.isPlaying = false;
      this.currentPlayingMessage = null;
    },

    switchInputMode() {
      this.inputMode = this.inputMode === 'text' ? 'voice' : 'text';
    },

    // 创建实时对话房间
    async createRoom() {
      if(this.isConnecting) {
        uni.showToast({
          title: '正在连接中...',
          icon: 'none'
        })
        return
      }
      
      try {
        this.isConnecting = true
        uni.showLoading({
          title: '正在创建房间...'
        })
        
        const result = await createRoom(this.botId)
		console.log(result)
        if(result.room_id) {
          this.roomId = result.room_id
          // 创建成功后跳转到实时对话页面
          uni.navigateTo({
            url: `/pages/tools/science-captain/room?botId=${this.botId}`
          })
        } else {
          throw new Error('创建房间失败')
        }
        
      } catch(error) {
        console.error('创建房间失败:', error)
        uni.showToast({
          title: '创建房间失败，请重试',
          icon: 'none'
        })
      } finally {
        this.isConnecting = false
        uni.hideLoading()
      }
    },

    // 修改播放助手语音的方法
    playAssistantVoice(message) {
      // 如果当前消息正在播放，则停止播放
      if(message.isPlaying) {
        if(this.ttsAudioContext) {
          this.ttsAudioContext.stop();
          this.ttsAudioContext.destroy();
          this.ttsAudioContext = null;
        }
        message.isPlaying = false;
        return;
      }
      
      // 如果有其他消息在播放，先停止
      if(this.ttsAudioContext) {
        this.ttsAudioContext.stop();
        this.ttsAudioContext.destroy();
        this.ttsAudioContext = null;
        
        // 重置所有消息的播放状态
        this.chatHistory.forEach(msg => {
          if(msg.role === 'assistant') {
            msg.isPlaying = false;
          }
        });
      }
      
      // 创建新的音频上下文
      this.ttsAudioContext = uni.createInnerAudioContext();
      this.ttsAudioContext.src = message.audioUrl;
      
      // 监听播放结束
      this.ttsAudioContext.onEnded(() => {
        message.isPlaying = false;
        this.ttsAudioContext.destroy();
        this.ttsAudioContext = null;
      });
      
      // 监听错误
      this.ttsAudioContext.onError((res) => {
        console.error('TTS播放错误:', res);
        message.isPlaying = false;
        uni.showToast({
          title: '播放失败',
          icon: 'none'
        });
        this.ttsAudioContext.destroy();
        this.ttsAudioContext = null;
      });
      
      // 开始播放
      message.isPlaying = true;
      this.ttsAudioContext.play();
    },
  }
}
</script>

<style>
page,body{
  height: 100%;
  background-color: #f8f9ff;
}
</style>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8f9ff;
  
  .header {
    padding: 0;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.15);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    
    .header-content {
      position: relative;
      padding: 40rpx 30rpx;
      padding-left: 100rpx;
      
      .back-btn {
        position: absolute;
        left: 15rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:active {
          opacity: 0.7;
        }
      }
      
      .title-box {
        .title {
          font-size: 36rpx;
          font-weight: 600;
          color: #fff;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
        }
        
        .desc {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.9);
          margin-top: 12rpx;
          line-height: 1.4;
        }
      }
      
      .phone-btn {
        position: absolute;
        right: 15rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:active {
          opacity: 0.7;
        }
      }
    }
  }
  
  .chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-top: 160rpx;
    padding-bottom: calc(140rpx + env(safe-area-inset-bottom, 0));
    
    .chat-list {
      flex: 1;
      padding: 20rpx;
    }
    
    .chat-section {
      .chat-item {
        margin-bottom: 30rpx;
        
        .message-wrapper {
          display: flex;
          align-items: flex-start;
          
          .avatar {
            width: 80rpx;
            height: 80rpx;
            margin-right: 20rpx;
            flex-shrink: 0;
            border: 4rpx solid #fff;
            border-radius: 50%;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
            overflow: hidden;
            
            image {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
          }
          
          .message {
            max-width: 70%;
            
            .message-content {
              background: #fff;
              padding: 24rpx;
              border-radius: 4rpx 20rpx 20rpx 20rpx;
              position: relative;
              box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
              word-break: break-all;
              min-height: 80rpx;
              
              .voice-message {
                audio {
                  width: 100%;
                  height: 60rpx;
                  margin: 10rpx 0;
                }
                
                .voice-duration {
                  font-size: 24rpx;
                  color: #999;
                  margin-top: 8rpx;
                }
              }
              
              .typing-indicator {
                margin-top: 12rpx;
                padding: 8rpx 0;
                
                .dot {
                  display: inline-block;
                  width: 8rpx;
                  height: 8rpx;
                  margin: 0 4rpx;
                  background: #4a90e2;
                  border-radius: 50%;
                  animation: typing 1s infinite;
                  
                  &:nth-child(2) {
                    animation-delay: 0.2s;
                  }
                  
                  &:nth-child(3) {
                    animation-delay: 0.4s;
                  }
                }
              }
              
              .message-text {
                font-size: 28rpx;
                color: #333;
                line-height: 1.6;
                white-space: pre-wrap;
                word-wrap: break-word;
                
                &.typing {
                  opacity: 0.8;
                }
              }
            }
            
            .message-actions {
              margin-top: 12rpx;
              display: flex;
              justify-content: flex-end;
              gap: 10rpx;
              
              .btn-copy, .btn-retry, .btn-delete, .btn-voice {
                background: none;
                padding: 12rpx;
                transition: all 0.3s ease;
                
                &::after {
                  border: none;
                }
                
                &:active {
                  transform: scale(0.95);
                }
              }

              .btn-delete {
                &:active {
                  .u-icon {
                    color: #ff3b30 !important;
                  }
                }
              }
            }
          }
        }
        
        &.user {
          .message-wrapper {
            flex-direction: row-reverse;
            
            .avatar {
              margin-right: 0;
              margin-left: 20rpx;
            }
            
            .message {
              .message-content {
                background: linear-gradient(135deg, #4a90e2, #357abd);
                border-radius: 20rpx 4rpx 20rpx 20rpx;
                
                .message-text {
                  color: #fff;
                }
              }
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 120rpx 40rpx;
      
      .empty-image {
        width: 280rpx;
        height: 280rpx;
        margin-bottom: 40rpx;
        opacity: 0.9;
      }
    }
  }
}

.input-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 1);
  backdrop-filter: blur(10px);
  padding: 24rpx 30rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom, 0));
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.06);
  z-index: 100;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  
  .input-box {
    display: flex;
    align-items: center;
    gap: 20rpx;
    
    .voice-btn {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      
      &:active {
        background: #e0e0e0;
      }
    }
    
    .url-inputerea {
      flex: 1;
      background: #f5f5f5;
      border-radius: 44rpx;
      padding: 20rpx 36rpx;
      font-size: 28rpx;
      color: #333;
      transition: all 0.3s ease;
      
      &:focus {
        background: #fff;
        box-shadow: 0 2rpx 12rpx rgba(74, 144, 226, 0.1);
      }
      
      &:disabled {
        background: #f0f2f5;
        color: #999;
      }
    }
    
    .btn-extract {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #4a90e2, #357abd);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
      
      &:active {
        transform: scale(0.95);
      }
      
      &[disabled] {
        background: #e0e0e0;
        box-shadow: none;
      }
      
      &::after {
        border: none;
      }
    }
  }
}

@keyframes typing {
  0%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-4rpx);
    opacity: 1;
  }
}

.cancel-btn-wrapper {
  position: fixed;
  bottom: calc(150rpx + env(safe-area-inset-bottom, 0));
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  padding: 12rpx 0;
  
  .btn-stop {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff3b30, #ff6b6b);
    color: #fff;
    font-size: 24rpx;
    padding: 12rpx 30rpx;
    border-radius: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.2);
    transition: all 0.3s ease;
    height: 60rpx;
    line-height: 1;
    
    &:active {
      transform: scale(0.95);
    }
    
    &::after {
      border: none;
    }
  }
}

.record-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .inner-content {
    background: #fff;
    padding: 40rpx;
    border-radius: 20rpx;
    text-align: center;
    min-width: 400rpx;

    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 40rpx;
    }

    .voice-line-wrap {
      height: 100rpx;
      width: 100%;
      margin-bottom: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .voice-line {
        height: 100%;
        width: 10rpx;
        background: #4a90e2;
        margin: 0 4rpx;
        border-radius: 4rpx;

        &.one {
          animation: wave 0.4s 1s linear infinite alternate;
        }
        &.two {
          animation: wave 0.4s 0.9s linear infinite alternate;
        }
        &.three {
          animation: wave 0.4s 0.8s linear infinite alternate;
        }
        &.four {
          animation: wave 0.4s 0.7s linear infinite alternate;
        }
        &.five {
          animation: wave 0.4s 0.6s linear infinite alternate;
        }
        &.six {
          animation: wave 0.4s 0.5s linear infinite alternate;
        }
        &.seven {
          animation: wave 0.4s linear infinite alternate;
        }
      }
    }

    .tips {
      font-size: 28rpx;
      color: #999;
    }
  }
}

@keyframes wave {
  0% {
    transform: scale(1, 1);
    background-color: #4a90e2;
  }
  100% {
    transform: scale(1, 0.2);
    background-color: #357abd;
  }
}

.voice-input-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 0 30rpx;
  
  .voice-btn {
    width: 88rpx;
    height: 88rpx;
    border-radius: 50%;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    
    &:active {
      background: #e0e0e0;
    }
  }
  
  .voice-record-btn {
    flex: 1;
    height: 88rpx;
    background: #f5f5f5;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #666;
    transition: all 0.3s ease;
    
    &.recording {
      background: #e0e0e0;
      color: #357abd;
    }
    
    &:active {
      background: #e0e0e0;
    }
  }
}

.message-content {
  &.user {
    .voice-message {
      background: linear-gradient(135deg, #4a90e2, #357abd);
      border-radius: 20rpx 4rpx 20rpx 20rpx;
      
      .voice-content {
        .u-icon {
          color: #fff !important;
        }
        
        .duration {
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }
  
  .voice-message {
	  display: flex;
	  align-items: center;
    max-width: 300rpx;
    border-radius: 4rpx 20rpx 20rpx 20rpx;
    // background: #fff;
    // box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  }
}

.voice-actions {
  margin-top: 12rpx;
  display: flex;
  justify-content: flex-end;
  
  .btn-voice {
    background: none;
    padding: 12rpx;
    transition: all 0.3s ease;
    
    &::after {
      border: none;
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}
</style> 