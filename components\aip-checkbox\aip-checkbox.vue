<template>
	<view>
		<view :class="check==true?'check-box':'box'">
			<u-icon name="checkbox-mark" :color="check==true?'#FFFFFF':'#c8c9cc'" size="14" ></u-icon>
		</view>
	</view>
</template>

<script>
	export default {
		name: "checkbox",
		props:{
			check:false,
			key: 0,
		},
		data() {
			return {	
			};
		}
	}
</script>

<style lang="scss"scoped>
	.box {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40upx;
		height: 40upx;
		border: 1px solid #c8c9cc;
		padding: 2px;
		border-radius: 50%;	
		margin: 10rpx;
	}
	.check-box{
		width: 40upx;
		height: 40upx;
		border: 1px solid #007AFF;
		padding: 2px;
		border-radius: 50%;
		background-color: #007AFF;
		margin: 10rpx;
	}
</style>
