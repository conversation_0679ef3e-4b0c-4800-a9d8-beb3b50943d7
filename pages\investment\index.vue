<template>
	<view>
		<z-paging :show-scrollbar="false" refresher-only  @onRefresh="onRefresh" refresher-background='#fffff00' ref="paging" :use-page-scroll='true'>
			<view slot="top">
				<u-navbar height="44" :title="navtitle" :bgColor="bgColor" leftIcon="" :placeholder='true' :auto-back="false">
					<!-- <view style="" slot="left">
						<u-icon name="arrow-left" size="20" color="#000"></u-icon>
					</view> -->
				</u-navbar>
				<view class="index" >
					<wu-app-update></wu-app-update>
				</view>
				<my-nav :title='navtitle' :pageScrollTop='pageScrollTop'></my-nav>
			</view>
			<template #refresher="{refresherStatus}">
				<custom-refresher color="#000" :status="refresherStatus" />
			</template>
			<template #loadingMoreNoMore>
				<custom-nomore />
			</template>
			<view v-if="showadd" class="welcome-container">
				<!-- 欢迎内容 -->
				<view class="welcome-content">
					<!-- 标题 -->
					<view class="welcome-title">欢迎使用生意港</view>
					<view class="welcome-subtitle">请在下方填写邀请码</view>

					<!-- 主图片区域 -->
					<view class="welcome-image-container">
						<image src="/static/syg/index/jiangbei.png" mode="widthFix" class="welcome-image"></image>
						<view class="welcome-badge">
							<text class="badge-text">邀请码</text>
						</view>
					</view>

					<!-- 邀请码输入区域 -->
					<view class="invite-input-section">
						<view class="invite-input-container" :class="{'shake': inputShake}">
							<input
								v-model="inviteCode"
								placeholder="请输入邀请码"
								class="invite-input"
								:class="{'error': inputError}"
								@focus="onInputFocus"
								@blur="onInputBlur"
							/>
						</view>

						<!-- 提交按钮 -->
						<view class="submit-button"  @click="confirmInviteCode" :class="{'loading': submittingInvite}">
							<view v-if="submittingInvite" class="loading-spinner"></view>
							<text v-else>提交</text>
						</view>
					</view>
				</view>
				<!-- 底部装饰区域 -->
				<view class="welcome-decoration">
					<image src="/static/syg/index/icon.png" mode="aspectFit" class="decoration-image"></image>
				</view>
			</view>
			<view v-else>
				<!-- 顶部轮播图 -->
				<view class="swiper-container">
					<swiper circular :indicator-dots="true" :autoplay="true" interval="3000" duration="500" class="swiper">
						<swiper-item v-for="(item, index) in bannerList" :key="index" class="swiper-item">
							<image :src="item.image" mode="aspectFill" class="swiper-image" @click="handleBannerClick(item)"></image>
						</swiper-item>
					</swiper>
				</view>
				
				<!-- 8大金刚区 -->
				<view class="icon-grid">
					<view class="icon-item" v-for="(item, index) in iconList" :key="index" @click="navigateTo(item.menuCode)">
						<image :src="item.icon" mode="aspectFit" class="icon-image"></image>
						<text class="icon-text">{{ item.name }}</text>
					</view>
				</view>
				
				<!-- 动态模块内容 -->
				<template v-for="(menu, menuIndex) in allMenuItems">
				
					<view :key="menuIndex" v-if="moduleData[menu.menuCode] && moduleData[menu.menuCode].length > 0 && menu.menuCode !== 'PromotionalVideo'" :id="'module-' + menu.menuCode">
						<!-- 自定义标题栏 -->
						<view class="section-title custom-title" :style="titleBgImage ? `background-image: url(${titleBgImage})` : ''">
							<view class="title-content" :style="{
								color: titleColor? `rgb(${titleColor})` : '#333',
								fontSize: titleFontSize ? `${titleFontSize}rpx` : '32rpx',
								left: titleX ? titleX : '50%',
								top: titleY ? titleY : '50%'
							}">
								<text class="title-text">{{ menu.menuName }}</text>
							</view>
						</view>
				
						<!-- 企业介绍模块(模板1) -->
						<template v-if="menu.template === 1">
							<!-- 第一项显示大图 -->
							<view class="intro-container" v-if="moduleData[menu.menuCode][0]">
								<view class="intro-item" @click="handleItemClick(moduleData[menu.menuCode][0])">
									<image :src="moduleData[menu.menuCode][0].cover" mode="widthFix" class="intro-image"></image>
								</view>
							</view>
				
							<!-- 第二项显示多图轮播 -->
							<view class="cert-container" v-if="moduleData[menu.menuCode][1] && moduleData[menu.menuCode][1].fileDTOList && moduleData[menu.menuCode][1].fileDTOList.length > 0">
								<view class="cert-grid">
									<!-- 最多显示前5个图片 -->
									<view 
										v-for="(item, index) in moduleData[menu.menuCode][1].fileDTOList.slice(0, 4)" 
										:key="index" 
										class="cert-grid-item"
										@click="handleImagePreview(item.fileUrl, moduleData[menu.menuCode][1].fileDTOList)"
									>
										<image :src="item.fileUrl" mode="aspectFill" class="cert-grid-image"></image>
										<view class="cert-grid-mask" v-if="index === 3 && moduleData[menu.menuCode][1].fileDTOList.length > 4">
											<text class="cert-grid-more">+{{ moduleData[menu.menuCode][1].fileDTOList.length - 4 }}</text>
										</view>
									</view>
								</view>
							</view>
						</template>
						
						<!-- 创始人说模块 -->
						<template v-else-if="menu.template === 9 ||menu.template === 2">
							<view class="founder-container">
								<view 
									v-for="(item, index) in moduleData[menu.menuCode]" 
									:key="index" 
									class="founder-item" 
									@click="playVideoItem(item)"
								>
									<image :src="item.cover" mode="aspectFill" class="founder-image"></image>
									<view class="founder-overlay">
										<view class="founder-play-icon">
											<u-icon name="play-right-fill" color="#fff" size="40"></u-icon>
										</view>
									</view>
									<!-- <view class="founder-info">
										<view class="founder-title">{{ item.tittle || '' }}</view>
										<view class="founder-digest">{{ item.digest || '' }}</view>
									</view> -->
								</view>
							</view>
						</template>
						
						<!-- 模板41 - 大图+水平滚动列表 -->
						<template v-else-if="menu.template === 41">
							<!-- 第一项显示大图 -->
							<view class="feature-container" v-if="moduleData[menu.menuCode] && moduleData[menu.menuCode].length > 0">
								<view 
									class="feature-main-item" 
									@click="handleItemClick(moduleData[menu.menuCode][0])"
								>
									<image :src="moduleData[menu.menuCode][0].cover" mode="aspectFill" class="feature-main-image"></image>
									<view class="feature-main-overlay">
										<view class="feature-main-play-icon" v-if="moduleData[menu.menuCode][0].type === 1">
											<u-icon name="play-right-fill" color="#fff" size="24"></u-icon>
										</view>
										<view class="feature-main-play-icon" v-else-if="moduleData[menu.menuCode][0].type === 2">
											<image src="/static/image/720.png" class="feature-vr-icon"></image>
										</view>
									</view>
									<!-- <view class="feature-main-info">
										<view class="feature-main-title">{{ moduleData[menu.menuCode][0].tittle || '' }}</view>
										<view class="feature-main-digest">{{ moduleData[menu.menuCode][0].digest || '' }}</view>
									</view> -->
								</view>
							</view>
							
							<!-- 其余项水平滚动显示 -->
							<view class="feature-scroll-container" v-if="moduleData[menu.menuCode] && moduleData[menu.menuCode].length > 1">
								<scroll-view scroll-x="true" class="feature-scroll-view">
									<view class="feature-scroll-list">
										<view 
											v-for="(item, index) in moduleData[menu.menuCode].slice(1)" 
											:key="index" 
											class="feature-scroll-item" 
											@click="handleItemClick(item)"
										>
											<image :src="item.cover" mode="aspectFill" class="feature-scroll-image"></image>
											<view class="feature-scroll-play-icon" v-if="item.type === 1">
												<u-icon name="play-right-fill" color="#fff" size="14"></u-icon>
											</view>
											<view class="feature-scroll-play-icon" v-else-if="item.type === 2">
												<image src="/static/image/720.png" class="feature-vr-icon-small"></image>
											</view>
											<!-- <view class="feature-scroll-title">{{ item.tittle || '' }}</view> -->
										</view>
									</view>
								</scroll-view>
							</view>
						</template>
						
						<!-- 模板3 - 全部横向滚动小图 -->
						<template v-else-if="menu.template === 3 || menu.template === 11">
							<view class="horizontal-scroll-container">
								<scroll-view scroll-x="true" class="horizontal-scroll-view" show-scrollbar="false">
									<view class="horizontal-scroll-list">
										<template v-for="(item, index) in moduleData[menu.menuCode]">
											<!-- 当type=0且有多张图片时，遍历fileDTOList展示每张图片 -->
											<template v-if="item.type === 0 && item.fileDTOList && item.fileDTOList.length > 0">
												<view 
													v-for="(imgItem, imgIndex) in item.fileDTOList" 
													:key="`${index}-${imgIndex}`" 
													class="horizontal-scroll-item" 
													@click="handleImagePreview(imgItem.fileUrl, item.fileDTOList)"
												>
													<image 
														:src="imgItem.fileUrl" 
														mode="aspectFill" 
														class="horizontal-scroll-image"
													></image>
													<view class="horizontal-scroll-title">{{ item.tittle || '' }}</view>
												</view>
											</template>
											<!-- 其他情况正常显示 -->
											<view 
												v-else
												:key="index" 
												class="horizontal-scroll-item" 
												@click="handleItemClick(item)"
											>
												<image 
													:src="item.cover || ''" 
													mode="aspectFill" 
													class="horizontal-scroll-image"
												></image>
												<view class="horizontal-scroll-play-icon" v-if="item.type === 1">
													<u-icon name="play-right-fill" color="#fff" size="20"></u-icon>
												</view>
												<view class="horizontal-scroll-play-icon" v-else-if="item.type === 2">
													<image src="/static/image/vr-icon.png" class="vr-icon"></image>
												</view>
												<view class="horizontal-scroll-title">{{ item.tittle || '' }}</view>
											</view>
										</template>
									</view>
								</scroll-view>
							</view>
						</template>
						
						<!-- 模板4 - 横向滚动卡片列表 -->
						<template v-else-if="menu.template === 4">
							<view class="slide-cards-container">
								<scroll-view scroll-x="true" class="slide-scroll-view" show-scrollbar="false">
									<view class="slide-card-list">
										<view 
											v-for="(item, index) in moduleData[menu.menuCode]" 
											:key="index" 
											class="slide-card-item" 
											:class="{
												'slide-card-item-single': moduleData[menu.menuCode].length === 1,
												'slide-card-item-double': moduleData[menu.menuCode].length === 2
											}"
											@click="handleItemClick(item)"
										>
											<image 
												:src="item.type === 0 && item.fileDTOList && item.fileDTOList.length > 0 ? 
													item.fileDTOList[0].fileUrl : 
													(item.cover || '')" 
												mode="widthFix" 
												class="slide-card-image"
												:class="{
													'slide-card-image-single': moduleData[menu.menuCode].length === 1,
													'slide-card-image-double': moduleData[menu.menuCode].length === 2
												}"
											></image>
											<view class="slide-card-play-icon" v-if="item.type === 1">
												<u-icon name="play-right-fill" color="#fff" size="20"></u-icon>
											</view>
											<view class="slide-card-title">{{ item.tittle || '' }}</view>
										</view>
									</view>
								</scroll-view>
							</view>
						</template>

						<!-- 模板31 - 双排横向滚动列表 -->
						<template v-else-if="menu.template === 31">
							<view class="double-row-container">
								<!-- 第一行 -->
								<view class="double-row-section" v-if="getRowData(moduleData[menu.menuCode], '第一行').length > 0">
									<scroll-view scroll-x="true" class="double-row-scroll-view" show-scrollbar="false">
										<view class="double-row-scroll-list">
											<view
												v-for="(item, index) in getRowData(moduleData[menu.menuCode], '第一行')"
												:key="'row1-' + index"
												class="double-row-scroll-item"
												@click="handleItemClick(item)"
											>
												<image
													:src="item.cover || ''"
													mode="aspectFill"
													class="double-row-scroll-image"
												></image>
												<view class="double-row-scroll-play-icon" v-if="item.type === 1">
													<u-icon name="play-right-fill" color="#fff" size="16"></u-icon>
												</view>
												<view class="double-row-scroll-play-icon" v-else-if="item.type === 2">
													<image src="/static/image/720.png" class="double-row-vr-icon"></image>
												</view>
												<view class="double-row-scroll-title">{{ item.tittle || '' }}</view>
											</view>
										</view>
									</scroll-view>
								</view>

								<!-- 第二行 -->
								<view class="double-row-section" v-if="getRowData(moduleData[menu.menuCode], '第二行').length > 0">
									<scroll-view scroll-x="true" class="double-row-scroll-view" show-scrollbar="false">
										<view class="double-row-scroll-list">
											<view
												v-for="(item, index) in getRowData(moduleData[menu.menuCode], '第二行')"
												:key="'row2-' + index"
												class="double-row-scroll-item"
												@click="handleItemClick(item)"
											>
												<image
													:src="item.cover || ''"
													mode="aspectFill"
													class="double-row-scroll-image"
												></image>
												<view class="double-row-scroll-play-icon" v-if="item.type === 1">
													<u-icon name="play-right-fill" color="#fff" size="16"></u-icon>
												</view>
												<view class="double-row-scroll-play-icon" v-else-if="item.type === 2">
													<image src="/static/image/720.png" class="double-row-vr-icon"></image>
												</view>
												<view class="double-row-scroll-title">{{ item.tittle || '' }}</view>
											</view>
										</view>
									</scroll-view>
								</view>
							</view>
						</template>

						<!-- 模板61 - 第一个大图+下方横向滚动小图 -->
						<template v-else-if="menu.template === 61">
							<!-- 第一项显示大图 -->
							<view class="main-feature-container" v-if="moduleData[menu.menuCode] && moduleData[menu.menuCode].length > 0">
								<view
									class="main-feature-item"
									@click="handleItemClick(moduleData[menu.menuCode][0])"
								>
									<image :src="moduleData[menu.menuCode][0].cover" mode="aspectFill" class="main-feature-image"></image>
									<view class="main-feature-overlay">
										<view class="main-feature-play-icon" v-if="moduleData[menu.menuCode][0].type === 1">
											<u-icon name="play-right-fill" color="#fff" size="24"></u-icon>
										</view>
										<view class="main-feature-play-icon" v-else-if="moduleData[menu.menuCode][0].type === 2">
											<image src="/static/image/720.png" class="main-feature-vr-icon"></image>
										</view>
									</view>
								</view>
							</view>

							<!-- 第二项的fileDTOList横向滚动显示 -->
							<view class="gallery-scroll-container" v-if="moduleData[menu.menuCode] && moduleData[menu.menuCode].length > 1 && moduleData[menu.menuCode][1].fileDTOList && moduleData[menu.menuCode][1].fileDTOList.length > 0">
								<scroll-view scroll-x="true" class="gallery-scroll-view" show-scrollbar="false">
									<view class="gallery-scroll-list">
										<view
											v-for="(imgItem, index) in moduleData[menu.menuCode][1].fileDTOList"
											:key="index"
											class="gallery-scroll-item"
											@click="handleImagePreview(imgItem.fileUrl, moduleData[menu.menuCode][1].fileDTOList)"
										>
											<image :src="imgItem.fileUrl" mode="aspectFill" class="gallery-scroll-image"></image>
										</view>
									</view>
								</scroll-view>
							</view>
						</template>

						<!-- 模板42 - 竖屏小图横向滚动 -->
						<template v-else-if="menu.template === 42">
							<view class="vertical-scroll-container">
								<scroll-view scroll-x="true" class="vertical-scroll-view" show-scrollbar="false">
									<view class="vertical-scroll-list">
										<view
											v-for="(item, index) in moduleData[menu.menuCode]"
											:key="index"
											class="vertical-scroll-item"
											@click="handleItemClick(item)"
										>
											<image
												:src="item.cover || ''"
												mode="aspectFill"
												class="vertical-scroll-image"
											></image>
											<view class="vertical-scroll-play-icon" v-if="item.type === 1">
												<u-icon name="play-right-fill" color="#fff" size="20"></u-icon>
											</view>
											<view class="vertical-scroll-play-icon" v-else-if="item.type === 2">
												<image src="/static/image/720.png" class="vertical-vr-icon"></image>
											</view>
											<view class="vertical-scroll-title">{{ item.tittle || '' }}</view>
										</view>
									</view>
								</scroll-view>
							</view>
						</template>
						<!-- 新闻资讯模块 -->
						<template v-else-if="menu.menuCode === 'PostInfomartion'||menu.menuCode === 'new'">
							<view class="news-container">
								<view class="news-item" v-for="(item, index) in moduleData[menu.menuCode]" :key="index" @click="handleItemClick(item)">
									<view class="news-header" :class="{'has-image': item.imgUrl}">
										<!-- 标题部分 -->
										<view class="news-title-area">
											<view class="news-title">{{ item.tittle || '' }}</view>
										</view>
										<!-- 图片部分，如果有图片则显示 -->
										<view class="news-image-container" v-if="item.imgUrl">
											<image :src="item.imgUrl" mode="aspectFill" class="news-image"></image>
										</view>
									</view>
									<!-- 内容摘要（整行显示）-->
									<view class="news-brief">{{ item.briefIntroduction || '' }}</view>
									<!-- 作者和统计信息 -->
									<view class="news-info-row">
										<view class="news-author">
											<image v-if="item.headPortrait" :src="item.headPortrait" class="news-avatar"></image>
											<text>{{ item.nickName || '管理员' }}</text>
										</view>
										<view class="news-meta">
											<text class="news-date">{{ item.creationTime ? formatDate(item.creationTime) : '' }}</text>
											<!-- <view class="news-stats">
												<u-icon name="eye" size="24" color="#999"></u-icon>
												<text class="news-views">{{ (item.viewCount || 0) + 5000 }}</text>
											</view> -->
										</view>
									</view>
								</view>
								<!-- 底部加载提示 -->
								<view class="news-load-more" v-if="moduleData[menu.menuCode] && moduleData[menu.menuCode].length > 0">
									<view class="load-more-btn" @click="loadMoreNews" v-if="newsHasMore && !newsLoading">
										<text>加载更多</text>
									</view>
									<view class="loading" v-if="newsLoading">
										<u-loading size="24" mode="circle"></u-loading>
										<text class="loading-text">加载中...</text>
									</view>
									<view class="no-more" v-if="!newsHasMore && !newsLoading">
										<text>没有更多了</text>
									</view>
								</view>
							</view>
						</template>
						
						<!-- 其他模块采用默认展示方式 -->
						<template v-else>
							<view class="default-module">
								<view class="default-item" v-for="(item, index) in moduleData[menu.menuCode]" :key="index" @click="handleItemClick(item)">
									<image v-if="item.cover" :src="item.cover" mode="aspectFill" class="default-image"></image>
									<view class="default-title">{{ item.tittle || '' }}</view>
								</view>
							</view>
						</template>
					</view>
				</template>
				
				<!-- 悬浮按钮 -->
				<view class="float-buttons">
					<view class="float-btn live-room" @click="openLiveRoom" v-if="showLiveRoom">
						<image src="/static/icon_liveHint_ss.gif" mode="widthFix" class="float-icon" style="width: 230upx;"></image>
						<!-- <text class="float-text">直播间</text> -->
					</view>
					<view class="float-btn business-meeting" @click="openLiveRoom" v-if="showBusinessMeeting">
						<image src="/static/icon_liveHint_zsh.gif" mode="widthFix" class="float-icon" style="width: 230upx;"></image>
						<!-- <text class="float-text">招商会</text> -->
					</view>
					<view class="float-btn digital-teacher" @click="openDigitalTeacher" v-if="showDigitalTeacher">
						<image src="/static/icon_liveHint_ai.gif" mode="widthFix" class="float-icon" ></image>
						<!-- <text class="float-text">数字人讲解</text> -->
					</view>
					
					
				</view>
			</view>
			
			<my-bottom></my-bottom>
			<u-toast ref="uToast" style="z-index: ***********;"></u-toast>
			<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
				:loading="loading" ></u-loading-page>

			<!-- 直播未开始弹窗 -->
			<u-popup :show="showLiveNotStartedPopup" @close="closeLiveNotStartedPopup" mode="center" 
				:closeable="true" closeIconPos="top-right" round="16" :safeAreaInsetBottom='false'>
				<view class="live-not-started-popup">
					<view class="live-popup-header">
						<text class="live-popup-title">{{liveNotStartedInfo.toptitle}}</text>
					</view>
					<view class="live-popup-content">
						<image 
							:src="liveNotStartedInfo.coverImg" 
							mode="widthFix" 
							class="live-popup-cover"
						></image>
						<view class="live-popup-info">
							<view class="live-info-title">{{liveNotStartedInfo.title}}</view>
							<view class="live-info-meta">
								<u-icon name="account-fill" color="#666" size="20"></u-icon>
								<text class="live-info-author">{{liveNotStartedInfo.author}}</text>
							</view>
							<view class="live-info-meta">
								<u-icon name="clock-fill" color="#666" size="20"></u-icon>
								<text class="live-info-time">{{liveNotStartedInfo.startTime}}</text>
							</view>
						</view>
					</view>
					<view class="live-popup-status">
						
						<view class="live-status-text" v-if="liveNotStartedInfo.toptitle=='直播已结束'">直播已结束，敬请期待</view>
						<view class="live-status-text" v-else>直播尚未开始，请稍后再来</view>
					</view>
					<view class="live-popup-footer">
						<view class="live-popup-btn" @click="closeLiveNotStartedPopup">
							我知道了
						</view>
					</view>
				</view>
			</u-popup>
		</z-paging>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				pageScrollTop: 0, // 页面滚动距离
				bgColor: 'rgba(255,255,255)',
				navtitle: '添加项目',
				bannerList: [],
				iconList: [],
				isPlaying: false,
				allMenuItems: [],
				moduleData: {},
				titleBgImage: '',
				titleX: 0,
				titleY: 0,
				titleFontSize: 0,
				titleColor: '',
				titleFontWeight: '',
				titleBarHeight: 0,
				showGallery: false,
				galleryTitle: '',
				galleryIndex: 0,
				galleryImages: [],
				currentVideoRecord: null, // 当前播放视频的记录
				companyId: '', // 公司ID
				lastScrollTop: 0, // 新增的lastScrollTop属性
				newsPage: 1, // 新闻当前页码
				newsLoading: false, // 新闻加载状态
				newsHasMore: true, // 是否还有更多新闻
				
				// 控制悬浮按钮显示
				showDigitalTeacher: false, // 数字人讲解按钮
				showLiveRoom: false, // 直播间按钮
				showBusinessMeeting: false, // 招商会按钮
				liveVideoData: [], // 存储liveVideopage API返回的数据
				indexLivingData: [], // 存储getIndexLiving API返回的数据
				
				// 暂未开播弹窗控制
				showLiveNotStartedPopup: false,
				liveNotStartedInfo: {
					toptitle:'',
					title: '',
					coverImg: '',
					startTime: '',
					author: ''
				},
				
				// 定时器
				indexLivingTimer: null, // 获取直播数据的定时器,
				showadd:false,

				// 邀请码相关
				inviteCode: '', // 邀请码
				submittingInvite: false, // 提交邀请码loading状态
				inputError: false, // 输入框错误状态
				inputShake: false, // 输入框震动动画

				// 公司ID缓存，用于避免重复调用接口
				cachedCompanyId: ''
			}
		},
		onLoad() {
			// 初始化公司ID
			// this.initCompanyId();
			
			// setTimeout(() => this.loading = false, 1000)
			// // 获取菜单数据
			// this.getMenu()
			
			// // 调用直播相关API
			// this.getIndexLivingData()
			// this.getLiveVideoPageData()
			
			// // 启动定时获取直播数据
			// this.startLivingDataTimer()
		},
		onShow() {
			setTimeout(() => this.loading = false, 1000)
			var company=uni.getStorageSync('defaultCompany')
			if(!company){
				this.showadd=true;
				return;
			}

			// 获取当前公司ID
			const currentCompanyId = company.companyId;
			this.navtitle = company.companyName;
			this.companyId = currentCompanyId;

			// 检查公司ID是否发生变化
			if (this.cachedCompanyId && this.cachedCompanyId === currentCompanyId) {
				// 公司ID没有变化，不需要重新调用接口
				console.log('公司ID未变化，跳过接口调用');
				return;
			}

			// 公司ID发生变化或首次进入，更新缓存并调用接口
			console.log('公司ID发生变化或首次进入，调用接口获取数据');
			this.cachedCompanyId = currentCompanyId;

			// 获取菜单数据
			this.getMenu()

			// 调用直播相关API
			this.getIndexLivingData()
			this.getLiveVideoPageData()

			// 启动定时获取直播数据
			this.startLivingDataTimer()
		},
		onPageScroll(e) {
			this.pageScrollTop = Math.floor(e.scrollTop);
		},
		onUnload() {
			// 清理定时器
			this.clearLivingDataTimer();
		},
		methods: {
			// 根据englishTittle字段过滤数据行
			getRowData(data, rowType) {
				if (!data || !Array.isArray(data)) return [];
				return data.filter(item => item.englishTittle === rowType);
			},

			// 启动定时获取直播数据的定时器
			startLivingDataTimer() {
				// 先清理可能存在的定时器
				this.clearLivingDataTimer();
				
				// 设置每分钟执行一次的定时器
				this.indexLivingTimer = setInterval(() => {
					console.log('定时获取直播数据');
					this.getIndexLivingData();
				}, 60000); // 60秒 = 1分钟
			},
			
			// 清理直播数据定时器
			clearLivingDataTimer() {
				if(this.indexLivingTimer) {
					clearInterval(this.indexLivingTimer);
					this.indexLivingTimer = null;
					console.log('直播数据定时器已清理');
				}
			},
			// 初始化公司ID
			initCompanyId() {
				// 默认公司ID
				// const defaultCompanyId = 'a3f2de53b88e4c5ab7c4af3442d37400';//科学队长
				// const defaultCompanyId = 'f78d05e497db433c8a96f44b548fce47';//勃学超市
				
				// 1. 检查是否存在全局公司ID
				const app = getApp();
				if (app.globalData && app.globalData.companyId) {
					this.companyId = app.globalData.companyId;
				} else {
					// 2. 如果没有全局ID，则设置默认ID并存储到全局
					this.companyId = '';
					
					// 3. 如果全局数据对象不存在，初始化它
					if (!app.globalData) {
						app.globalData = {};
					}
					
					// 4. 设置全局公司ID
					app.globalData.companyId = this.companyId;
					uni.setStorageSync('companyId',this.companyId)
				}
				
				console.log('当前使用的公司ID:', this.companyId);
			},
			handleBannerClick(item) {
				if (item.url) {
					uni.navigateTo({
						url: item.url
					})
				}
			},
			navigateTo(menuCode) {
				// 使用 uni.createSelectorQuery() 替代 document.getElementById
				const query = uni.createSelectorQuery().in(this);
				query.select(`#module-${menuCode}`).boundingClientRect(data => {
					if (data) {
						// 平滑滚动到该位置
						uni.pageScrollTo({
							scrollTop: data.top - 100, // 减去一些距离，确保标题在视图中
							duration: 300
						});
					} else {
						console.log('未找到对应模块:', menuCode);
					}
				}).exec();
			},
			playMainVideo() {
				this.isPlaying = true
			},
			openDigitalTeacher() {
				uni.navigateTo({
					url: '/pages/investment/aiList'
				})
			},
			openLiveRoom() {
				// 检查是否有直播数据
				if (!this.indexLivingData || this.indexLivingData.length === 0) {
					this.$refs.uToast.show({
						type: 'warning',
						message: '暂无直播内容'
					});
					return;
				}
				
				// 获取第一条直播数据
				const liveData = this.indexLivingData[0];
				
				// 判断是否正在直播 (identification == 1 表示正在直播)
				const isLiving = liveData.identification == 1;
				
				// 判断直播是否已结束（比较当前时间与直播时长）
				const currentTime = new Date().getTime();
				const startTime = new Date(liveData.startTime).getTime();
				const duration = liveData.duration || 0; // 直播时长（秒）
				const endTime = startTime + (duration * 1000); // 转换为毫秒
				
				const isLiveEnded = duration > 0 && currentTime > endTime;
				
				// 如果不是正在直播，显示未开播弹窗
				if (!isLiving) {
					// 更新弹窗信息
					this.liveNotStartedInfo = {
						toptitle:'直播未开始',
						title: liveData.tittle ,
						coverImg: liveData.liveCover || '/static/empty/2.png',
						startTime: liveData.startTime ? this.formatLiveTime(liveData.startTime) : '敬请期待',
						author: liveData.nickName || '项目负责人'
					};
					
					// 显示自定义弹窗
					this.showLiveNotStartedPopup = true;
					return;
				}
				
				// 对于contentType不为2的直播，通过API获取直播地址
				if (liveData.contentType !== 2) {
					// 调用API获取直播播放地址
					this.syghttp.ajax({
						url: this.syghttp.api.getLivePlay,
						method: 'GET',
						data: {
							dataId: liveData.id,
							companyId: this.companyId
						},
						success: (res) => {
							if (res.code === 1000 && res.data) {
								// 根据设备类型选择合适的直播流格式
								let liveUrl = '';
								
								// 使用uni API检测设备类型
								const systemInfo = uni.getSystemInfoSync();
								const platform = systemInfo.platform.toLowerCase();
								const isAppleDevice = platform === 'ios';
								
								if (isAppleDevice) {
									// 苹果设备优先使用HLS格式
									if (res.data.HLS) {
										console.log('苹果设备使用HLS格式');
										liveUrl = res.data.HLS;
									} else {
										// 苹果设备不支持FLV，回退到其他格式
										liveUrl = res.data.RTMP || liveData.liveUrl;
									}
								} else {
									// 安卓设备优先使用FLV格式
									if (res.data.FLV) {
										console.log('安卓设备使用FLV格式');
										liveUrl = res.data.FLV;
									} else if (res.data.HLS) {
										liveUrl = res.data.HLS;
									} else if (res.data.RTMP) {
										liveUrl = res.data.RTMP;
									} else {
										// 如果API没返回流地址，尝试使用原有地址
										liveUrl = liveData.liveUrl;
									}
								}
								
								console.log('获取到的直播URL:', liveUrl);
								if (!liveUrl) {
									this.$refs.uToast.show({
										type: 'error',
										message: '无法获取直播地址'
									});
									return;
								}
								
								// 准备传递给直播页面的视频数据
								const videoData = {
									url: liveUrl,
									id: liveData.id,
									name: liveData.tittle || '精彩直播',
									author: liveData.nickName || '项目负责人',
									startTime: liveData.startTime,
									playWay: liveData.playWay || 0, // 0是竖屏
									videoThumbnailUrl: liveData.liveCover || '',
									type: liveData.contentType // 真人直播
								};
								console.log('传递给直播页面的视频数据:', videoData);
								
								// 跳转到直播页面
								uni.navigateTo({
									url: `/pages/investment/live?videoData=${encodeURIComponent(JSON.stringify(videoData))}`
								});
							} else {
								this.$refs.uToast.show({
									type: 'error',
									message: '获取直播信息失败'
								});
							}
						},
						fail: (err) => {
							console.error('获取直播播放地址失败:', err);
							this.$refs.uToast.show({
								type: 'error',
								message: '获取直播信息失败'
							});
						}
					});
				} else {
					// contentType为2的直播
					// 先判断直播是否已结束
					if (isLiveEnded) {
							// 更新弹窗信息
							this.liveNotStartedInfo = {
								toptitle:'直播已结束',
								title: liveData.tittle ,
								coverImg: liveData.liveCover || '/static/empty/2.png',
								startTime: liveData.startTime ? this.formatLiveTime(liveData.startTime) : '敬请期待',
								author: liveData.nickName || '项目负责人'
							};
							// 显示自定义弹窗
							this.showLiveNotStartedPopup = true;
							return;
					}
					
					// 直播未结束，使用liveUrl直接跳转
					if (liveData.liveUrl) {
						// 计算当前视频应该播放的进度（秒）
						let currentProgress = 0;
						
						if (liveData.startTime) {
							// 获取当前时间和开始时间的时间差（毫秒）
							const currentTime = new Date().getTime();
							const startTime = new Date(liveData.startTime).getTime();
							const timeElapsed = currentTime - startTime;
							
							// 将时间差转换为秒
							const secondsElapsed = Math.floor(timeElapsed / 1000);
							
							// 如果设置了视频时长，确保播放进度不超过视频总时长
							if (liveData.duration && liveData.duration > 0) {
								currentProgress = Math.min(secondsElapsed, liveData.duration);
							} else {
								currentProgress = secondsElapsed;
							}
							
							console.log('计算的视频播放进度:', currentProgress, '秒');
						}
						
						// 准备传递给直播页面的视频数据
						const videoData = {
							url: liveData.liveUrl,
							id: liveData.id,
							name: liveData.tittle || '精彩直播',
							author: liveData.nickName || '项目负责人',
							startTime: liveData.startTime,
							playWay: liveData.playWay || 0, // 0是横屏
							videoThumbnailUrl: liveData.liveCover || '',
							currentProgress: currentProgress // 添加当前进度属性
						};
						
						// 跳转到直播页面
						uni.navigateTo({
							url: `/pages/investment/live?videoData=${encodeURIComponent(JSON.stringify(videoData))}`
						});
					} else {
						// 没有直播URL
						this.$refs.uToast.show({
							type: 'error',
							message: '无法获取直播地址'
						});
					}
				}
			},
			openBusinessMeeting() {
				uni.navigateTo({
					url: '/pages/business/meeting'
				})
			},
			getMenu() {
				this.syghttp.ajax({
					url: this.syghttp.api.homeMenu,
					method: 'GET',
					data:{
						companyId: this.companyId,
					},
					success: (res) => {
						if (res.code === 1000) {
							// 处理菜单数据
							if (res.data && res.data.dtos && res.data.dtos.length > 0) {
								// 调试信息
								console.log('所有菜单项:', res.data.dtos);
								
								// 处理所有菜单项
								this.allMenuItems = res.data.dtos;
								
								// 过滤金刚区菜单项
								this.iconList = res.data.dtos
									.filter(item => item.headColumn === 1)
									.map((item, index) => {
										return {
											id: index + 1,
											name: item.menuName,
											icon: item.menuIcon || '/static/image/default-icon.png',
											menuCode: item.menuCode,
											englishName: item.englishName
										}
									});
								
								// 获取所有模块的数据
								this.fetchAllModuleData();
								
								// 获取标题栏配置
								this.getStartupImgConfig();
								// this.getIconList();
							}
						}
					}
				})
			},
			getIconList() {
				this.syghttp.ajax({
					url: this.syghttp.api.apiGetPostInformation,
					method: 'POST',
					data:{
						"companyId": this.companyId,
						"page": {
						    pageNo:1,
						    pageSize: 2,
							}
					},
					success: (res) => {
						if (res.code === 1000) {
							// 处理菜单数据
							if (res.data && res.data.dtos && res.data.dtos.length > 0) {
								// 处理所有菜单项
								this.allMenuItems = res.data.dtos;
								
								// 过滤金刚区菜单项
								this.iconList = res.data.dtos
									.filter(item => item.headColumn === 1)
									.map((item, index) => {
										return {
											id: index + 1,
											name: item.menuName,
											icon: item.menuIcon || '/static/image/default-icon.png',
											menuCode: item.menuCode,
											englishName: item.englishName
										}
									});
								
								// 获取所有模块的数据
								this.fetchAllModuleData();
								
								// 获取标题栏配置
								this.getStartupImgConfig();
							}
						}
					}
				})
			},
			// 获取所有模块数据
			fetchAllModuleData() {
				if (!this.allMenuItems || this.allMenuItems.length === 0) return;
				
				// 创建模块数据的对象
				this.moduleData = {};
				
				// 遍历所有菜单项，获取每个模块的数据
				this.allMenuItems.forEach(menu => {
					this.getModuleData(menu.menuCode);
				});
			},
			
			// 获取单个模块的数据
			getModuleData(menuCode) {
				// 如果是新闻模块，使用专门的新闻接口
				if (menuCode === 'PostInfomartion'||menuCode === 'new') {
					console.log('正在使用apiGetPostInformation接口加载新闻数据');
					this.newsLoading = true;
					this.newsPage = 1; // 重置页码
					this.syghttp.ajax({
						url: this.syghttp.api.apiGetPostInformation,
						method: 'POST',
						data: {
							"companyId": this.companyId,
							"page": {
								"pageNo": this.newsPage,
								"pageSize": 10
							}
						},
						success: (res) => {
							if (res.code === 1000) {
								console.log('新闻接口数据返回:', res.data);
								
								// 存储模块数据
								if (res.data && res.data.items && res.data.items.items) {
									this.moduleData[menuCode] = res.data.items.items;
									console.log('新闻数据已存储，长度:', this.moduleData[menuCode].length);
									
									// 检查是否还有更多数据
									if (res.data.items.totalCount > this.moduleData[menuCode].length) {
										this.newsHasMore = true;
									} else {
										this.newsHasMore = false;
									}
									
									// 更新视图
									this.$forceUpdate();
								}
							}
							this.newsLoading = false;
						}
					});
					return; // 不再执行后续的默认接口调用
				}
				
				// 其他模块使用默认接口
				this.syghttp.ajax({
					url: this.syghttp.api.indexSwiper,
					method: 'POST',
					data: {
						"code": menuCode,
						"keyword": "",
						"page": {
							"maxResultCount": 0,
							"pageNo": 1,
							"pageSize": 40,
							"skipCount": 0
						},
						"companyId": this.companyId,
					},
					success: (res) => {
						if (res.code === 1000) {
							// 存储模块数据
							if (res.data && res.data.items && res.data.items.items) {
								this.moduleData[menuCode] = res.data.items.items;
								
								// 如果是首页轮播图，特殊处理
								if (menuCode === 'PromotionalVideo') {
									this.bannerList = res.data.items.items.map(item => {
										return {
											id: item.id || '',
											image: item.cover || item.fileUrl || (item.fileDTOList && item.fileDTOList.length > 0 ? item.fileDTOList[0].fileUrl : ''),
											url: item.url || '',
											title: item.tittle || ''
										}
									});
								}
								
								// 更新视图
								this.$forceUpdate();
							}
						}
					}
				});
			},
			
			// 获取标题栏配置
			getStartupImgConfig() {
				this.syghttp.ajax({
					url: this.syghttp.api.getStartupImgConfig,
					method: 'POST',
					data: {
						"pageSpliterBarLocation": 1,
						"companyId": this.companyId
					},
					success: (res) => {
						if (res.code === 1000) {
							if (res.data && res.data.item) {
								// 处理标题栏样式
								if (res.data.item.imageList && res.data.item.imageList.length > 0) {
									this.titleBgImage = res.data.item.imageList[0].fileUrl;
								}
								// 处理字体样式
								if (res.data.item.splitterFontStyle) {
									try {
										const fontStyle = JSON.parse(res.data.item.splitterFontStyle);
										this.titleX = fontStyle.x;
										if (fontStyle.x < 0) {
											this.titleX = (100 + (fontStyle.x * 100)) + '%';
											console.log(this.titleX,1)
										}else if(fontStyle.x == 0) {
											this.titleX = '50%';
											console.log(this.titleX,2)
										}
										else  {
											this.titleX = (fontStyle.x * 100) + '%';
											console.log(this.titleX,3)
										}
								
										console.log('字体位置:', fontStyle.x, fontStyle.y);
										this.titleY = fontStyle.y;
										if (fontStyle.y < 0) {
											this.titleY = (50 - (fontStyle.y * 100)) + '%';
										}
										else if(fontStyle.y == 0) {
											this.titleY = '50%';
										}
										 else {
											this.titleY = (50 + (fontStyle.y * 100)) + '%';
										}
													this.titleFontSize = fontStyle.fontSize;
													console.log(this.titleX,this.titleY)
										this.titleColor = fontStyle.color;
										this.titleFontWeight = fontStyle.fontWeight;
										this.titleBarHeight = fontStyle.splitBarImgHeight;
										console.log(this.titleColor,this.titleFontSize,this.titleFontWeight,this.titleBarHeight)
									} catch (e) {
										console.error('解析字体位置失败', e);
									}
								}
								
								// // 处理其他样式
								// if (res.data.item.commentStyle) {
								// 	try {
								// 		const commentStyle = JSON.parse(res.data.item.commentStyle);
								// 		this.titleFontSize = commentStyle.fontSize;
								// 		this.titleColor = commentStyle.color;
								// 		this.titleFontWeight = commentStyle.fontWeight;
								// 		this.titleBarHeight = commentStyle.splitBarImgHeight;
								// 		console.log(commentStyle)
								// 	} catch (e) {
								// 		console.error('解析字体样式失败', e);
								// 	}
								// }
								
								// 更新视图
								this.$forceUpdate();
							}
						}
					}
				});
			},
			
			// 处理不同类型内容的点击
			handleItemClick(item) {
				if (!item) return;
				
				// 根据type类型判断跳转方式
				switch(item.type) {
					case 0: // 打开图片或新闻
						// 如果有content字段或者是来自新闻模块，说明是新闻
						if (item.content) {
							uni.navigateTo({
								url: `/pages/investment/news?id=${item.id}&title=${encodeURIComponent(item.tittle || '')}&companyId=${this.companyId}`
							});
						// } else if (item.cover) {
						// 	this.handleImagePreview(item.cover, [{fileUrl: item.cover}]);
						// } else if (item.fileDTOList && item.fileDTOList.length > 0) {
						}
						else{
							this.handleImagePreview(item.fileDTOList[0].fileUrl, item.fileDTOList);
						}
						break;
					case 1: // 视频查看
						if(!item.url){
							return;
						}
						uni.navigateTo({
							url: `/pages/investment/video?videoUrl=${encodeURIComponent(item.url || item.videoUrl)}&title=${encodeURIComponent(item.tittle || '')}&id=${item.id || ''}&digest=${encodeURIComponent(item.digest || '')}&creationTime=${encodeURIComponent(item.creationTime || '')}&cover=${encodeURIComponent(item.cover || '')}&companyId=${this.companyId}`
						});
						break;
					case 2: // webview720全景
						uni.navigateTo({
							url: `/pages/investment/webview?url=${encodeURIComponent(item.panorama)}&title=${encodeURIComponent(item.tittle || '')}`
						});
						break;
					case 3: // 富文本
						const content = item.textInformationDTO && item.textInformationDTO.content ? item.textInformationDTO.content : '';
						uni.navigateTo({
							url: `/pages/investment/text?id=${item.id}&title=${encodeURIComponent(item.tittle || '')}&content=${encodeURIComponent(content)}&creationTime=${encodeURIComponent(item.creationTime || '')}`
						});
						break;
					default:
						console.log('未知类型:', item.type);
				}
			},
			
			// 使用uni-app原生的图片预览功能
			handleImagePreview(current, images) {
				try {
					// 提取文件URL
					const urls = images.map(img => typeof img === 'string' ? img : img.fileUrl);
					// 使用uni-app原生的预览图片API
					uni.previewImage({
						current: current, // 当前显示图片的链接
						urls: urls, // 所有图片的链接列表
						indicator: 'number', // 显示数字索引
						loop: true, // 允许循环预览
						success: function(res) {
							console.log('图片预览成功');
						},
						fail: function(err) {
							console.error('图片预览失败', err);
						}
					});
				} catch (error) {
					console.error('预览图片出错', error);
					uni.showToast({
						title: '图片预览失败',
						icon: 'none'
					});
				}
			},
			playVideoItem(item) {
				if (!item || (!item.url && !item.videoUrl)) {
					uni.showToast({
						title: '视频链接不存在',
						icon: 'none'
					});
					return;
				}
				
				// 导航到视频播放页
				uni.navigateTo({
					url: `/pages/investment/video?videoUrl=${encodeURIComponent(item.url || item.videoUrl)}&title=${encodeURIComponent(item.tittle || '')}&id=${item.id || ''}&digest=${encodeURIComponent(item.digest || '')}&creationTime=${encodeURIComponent(item.creationTime || '')}&cover=${encodeURIComponent(item.cover || '')}&companyId=${this.companyId}`
				});
			},
			// 视频播放回调
			handleVideoPlay(videoItem) {
				// 记录开始播放时间
				this.recordVideoWatching(videoItem, true);
			},
			
			// 视频暂停回调
			handleVideoPause() {
				// 记录暂停时的观看时间
				if (this.currentVideoRecord) {
					this.updateVideoWatchingTime(false);
				}
			},
			
			// 视频结束回调
			handleVideoEnd() {
				// 视频播放结束，记录观看时间
				if (this.currentVideoRecord) {
					this.updateVideoWatchingTime(true);
				}
				this.isPlaying = false;
			},
			
			// 视频错误回调
			handleVideoError() {
				uni.showToast({
					title: '视频加载失败',
					icon: 'none'
				});
				this.isPlaying = false;
			},
			
			// 记录视频观看
			recordVideoWatching(videoItem, isStart = true) {
				if (!videoItem || !videoItem.id) return;
				
				// 如果是开始播放
				if (isStart) {
					// 创建新的观看记录
					this.currentVideoRecord = {
						videoId: videoItem.id,
						startTime: new Date().getTime(),
						duration: 0
					};
					
					// 调用 API 记录开始观看
					this.syghttp.ajax({
						url: this.syghttp.api.updateWatchAppHomePageTime || '/api/updateWatchAppHomePageTime',
						method: 'POST',
						data: {
							browseRecordId: videoItem.id,
							duration: 0,
							companyId: this.companyId
						},
						success: (res) => {
							console.log('记录视频开始观看成功', res);
						},
						fail: (err) => {
							console.error('记录视频开始观看失败', err);
						}
					});
				}
			},
			
			// 更新视频观看时间
			updateVideoWatchingTime(isComplete = false) {
				if (!this.currentVideoRecord) return;
				
				// 计算观看时长（秒）
				const endTime = new Date().getTime();
				const duration = Math.floor((endTime - this.currentVideoRecord.startTime) / 1000);
				
				// 更新当前记录
				this.currentVideoRecord.duration = duration;
				
				// 调用 API 更新观看时长
				this.syghttp.ajax({
					url: this.syghttp.api.updateWatchAppHomePageTime || '/api/updateWatchAppHomePageTime',
					method: 'POST',
					data: {
						browseRecordId: this.currentVideoRecord.videoId,
						duration: duration,
						companyId: this.companyId
					},
					success: (res) => {
						console.log('更新视频观看时长成功', res);
					},
					fail: (err) => {
						console.error('更新视频观看时长失败', err);
					}
				});
				
				// 如果视频播放完成，清空当前记录
				if (isComplete) {
					this.currentVideoRecord = null;
				}
			},
			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return '';
				
				// 尝试解析日期字符串
				try {
					const date = new Date(dateStr);
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					
					return `${year}-${month}-${day}`;
				} catch (e) {
					console.error('日期格式化错误:', e);
					return dateStr; // 如果出错则返回原始字符串
				}
			},
			loadMoreNews() {
				// 如果已经在加载或没有更多数据，不再请求
				if (this.newsLoading || !this.newsHasMore) return;
				
				this.newsPage++;
				this.newsLoading = true;
				this.syghttp.ajax({
					url: this.syghttp.api.apiGetPostInformation,
					method: 'POST',
					data: {
						"companyId": this.companyId,
						"page": {
							"pageNo": this.newsPage,
							"pageSize": 10
						}
					},
					success: (res) => {
						if (res.code === 1000) {
							console.log('加载更多新闻数据返回:', res.data);
							
							// 存储模块数据
							if (res.data && res.data.items && res.data.items.items) {
								if (res.data.items.items.length > 0) {
									this.moduleData['new'] = [...this.moduleData['new'], ...res.data.items.items];
									console.log('新闻数据已追加，当前长度:', this.moduleData['new'].length);
									
									// 检查是否还有更多数据
									if (res.data.items.totalCount > this.moduleData['new'].length) {
										this.newsHasMore = true;
									} else {
										this.newsHasMore = false;
									}
								} else {
									// 没有更多数据了
									this.newsHasMore = false;
								}
								
								// 更新视图
								this.$forceUpdate();
							}
						} else {
							this.newsHasMore = false;
						}
						this.newsLoading = false;
					},
					fail: (err) => {
						console.error('加载更多新闻失败:', err);
						this.newsLoading = false;
					}
				});
			},
			// 获取首页直播数据
			getIndexLivingData() {
				this.syghttp.ajax({
					url: this.syghttp.api.getIndexLiving,
					method: 'POST',
					data: {
						"page": {
							"maxResultCount": 0,
							"pageNo": 1,
							"pageSize": 10,
							"skipCount": 0
						},
						"companyId": this.companyId
					},
					success: (res) => {
						console.log('getIndexLiving API返回数据:', res);
						
						// 处理返回数据，控制悬浮按钮显示逻辑
						if (res.code === 1000 && res.data && res.data.items && res.data.items.items && res.data.items.items.length > 0) {
							// 保存数据
							this.indexLivingData = res.data.items.items;
							
							// 根据第一个数据的contentType判断显示哪个按钮
							const firstItem = res.data.items.items[0];
							if (firstItem.contentType === 10) {
								// 显示招商会按钮
								this.showBusinessMeeting = true;
								this.showLiveRoom = false;
							} else if (firstItem.contentType === 5 || firstItem.contentType === 2) {
								// 显示直播间按钮
								this.showLiveRoom = true;
								this.showBusinessMeeting = false;
							}
						} else {
							// 无数据时两个按钮都不显示
							this.showLiveRoom = false;
							this.showBusinessMeeting = false;
						}
					},
					fail: (err) => {
						console.error('getIndexLiving API调用失败:', err);
						// 调用失败时，按钮都不显示
						this.showLiveRoom = false;
						this.showBusinessMeeting = false;
						// 显示错误提示
						this.$refs.uToast.show({
							type: 'error',
							message: '获取直播信息失败'
						});
					}
				});
			},
			// 获取直播视频页面数据
			getLiveVideoPageData() {
				this.syghttp.ajax({
					url: this.syghttp.api.liveVideopage,
					method: 'POST',
					data: {
						"page": {
							"maxResultCount": 0,
							"pageNo": 1,
							"pageSize": 10,
							"skipCount": 0
						},
						"companyId": this.companyId
					},
					success: (res) => {
						console.log('liveVideopage API返回数据:', res);
						
						// 处理返回数据，控制数字人讲解按钮显示
						if (res.code === 1000 && res.data && res.data.items && res.data.items.items && res.data.items.items.length > 0) {
							// 有数据，显示数字人讲解按钮
							this.showDigitalTeacher = true;
							// 保存视频数据
							this.liveVideoData = res.data.items.items;
						} else {
							// 无数据，不显示按钮
							this.showDigitalTeacher = false;
						}
					},
					fail: (err) => {
						console.error('liveVideopage API调用失败:', err);
						// 调用失败时，不显示按钮
						this.showDigitalTeacher = false;
					}
				});
			},
			// 添加格式化直播时间的方法
			formatLiveTime(timeStr) {
				try {
					const date = new Date(timeStr);
					const year = date.getFullYear();
					const month = (date.getMonth() + 1).toString().padStart(2, '0');
					const day = date.getDate().toString().padStart(2, '0');
					const hour = date.getHours().toString().padStart(2, '0');
					const minute = date.getMinutes().toString().padStart(2, '0');
					
					return `${year}-${month}-${day} ${hour}:${minute}`;
				} catch (e) {
					console.error('日期格式化错误:', e);
					return timeStr; // 如果出错则返回原始字符串
				}
			},
			// 关闭直播未开始弹窗
			closeLiveNotStartedPopup() {
				this.showLiveNotStartedPopup = false;
			},
			// 下拉刷新方法
			async onRefresh() {
				console.log('执行下拉刷新');
				// 重置页面状态
				this.newsPage = 1;
				this.newsHasMore = true;

				try {
					// 重新初始化公司ID
					this.initCompanyId();

					// 获取菜单数据（会触发fetchAllModuleData）
					// await this.getMenu();

					// 获取直播相关数据
					await this.getIndexLivingData();
					await this.getLiveVideoPageData();

					// 清理并重新启动直播数据定时器
					this.clearLivingDataTimer();
					this.startLivingDataTimer();

					// 提示刷新成功
					// this.$refs.uToast.show({
					// 	type: 'success',
					// 	message: '刷新成功'
					// });
				} catch (error) {
					console.error('刷新失败:', error);
					this.$refs.uToast.show({
						type: 'error',
						message: '刷新失败，请重试'
					});
				} finally {
					// 完成下拉刷新
					this.$refs.paging.complete();
				}
			},

			// 输入框获得焦点
			onInputFocus() {
				this.inputError = false;
			},

			// 输入框失去焦点
			onInputBlur() {
				// 可以在这里添加失去焦点的逻辑
			},

			// 确认邀请码
			confirmInviteCode() {
				console.log('1111')
				if (!this.inviteCode.trim()) {
					this.showInputError('请输入邀请码');
					return;
				}

				this.submittingInvite = true;

				// 调用createFocus接口
				this.syghttp.ajax({
					url: this.syghttp.api.createFocus,
					method: 'POST',
					data: {
						inviteCode: this.inviteCode.trim()
					},
					success: (res) => {
						console.log(res)
						this.submittingInvite = false;

						if (res.code === 1000) {
							// 成功提示
							this.$refs.uToast.show({
								message: '项目添加成功！',
								type: 'success'
							});

							// 清空输入框
							this.inviteCode = '';
							this.inputError = false;
							this.inputShake = false;

							// 刷新页面数据
							setTimeout(() => {
								// 检查是否有项目了
								var company = uni.getStorageSync('defaultCompany');
								if (company) {
									this.showadd = false;
									// 获取菜单数据
									this.getMenu();
									// 调用直播相关API
									this.getIndexLivingData();
									this.getLiveVideoPageData();
									// 启动定时获取直播数据
									this.startLivingDataTimer();
								}
							}, 1000);
						} else {
							// 显示错误信息
							// this.showInputError(res.msg || '邀请码无效，请检查后重试');
						}
					},
					fail: (err) => {
						console.error('添加项目失败:', err);
						this.submittingInvite = false;
						this.showInputError('网络错误，请稍后重试');
					}
				});
			},

			// 显示输入错误
			showInputError(message) {
				this.inputError = true;
				this.inputShake = true;
				console.log(message)
				// 显示错误提示
				this.$refs.uToast.show({
					message: message,
					type: 'error'
				});

				// 震动反馈
				if (uni.vibrateShort) {
					uni.vibrateShort();
				}

				// 清除震动动画
				setTimeout(() => {
					this.inputShake = false;
				}, 500);
			}
		}
	}
</script>

<style>
	page,
	body {
		background-color: #fdfcf8;
		height: 100%;
	}

	/* 欢迎页面样式 */
	.welcome-container {
		position: relative;
		height: 88vh;
		/* overflow: hidden; */
		background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 60rpx 40rpx;
		box-sizing: border-box;
		
	}

	.welcome-content {
		z-index: 99;
		width: 100%;
		max-width: 600rpx;
		background: rgba(255, 255, 255, 0.95);
		border-radius: 32rpx;
		padding:100rpx 40rpx 60rpx 40rpx;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
		backdrop-filter: blur(10rpx);
		text-align: center;
	}

	.welcome-title {
		font-size: 48rpx;
		font-weight: bold;
		color: #2d3436;
		margin-bottom: 16rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.welcome-subtitle {
		font-size: 28rpx;
		color: #636e72;
		margin-bottom: 60rpx;
		opacity: 0.8;
	}

	.welcome-image-container {
		position: absolute;
		top: -300rpx;
		left: 0;
		width: 100%;
		margin: 0 auto 60rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.welcome-image {
		width: 528rpx;
		height: 200rpx;
	}

	.welcome-badge {
		position: absolute;
		bottom: 55rpx;
		left: 50%;
		transform: translateX(-50%);
		
	}

	.badge-text {
		color: #fff;
		font-size: 36rpx;
		font-weight: 700;
	}

	.invite-input-section {
		margin-bottom: 60rpx;
	}

	.invite-input-container {
		margin-bottom: 40rpx;
		transition: all 0.3s ease;
	}

	.invite-input-container.shake {
		animation: shake 0.5s ease-in-out;
	}

	@keyframes shake {
		0%, 100% { transform: translateX(0); }
		25% { transform: translateX(-10rpx); }
		75% { transform: translateX(10rpx); }
	}

	.invite-input {
		width: 100%;
		height: 88rpx;
		background: #f8f9fa;
		border: 2rpx solid #e9ecef;
		border-radius: 44rpx;
		padding: 0 32rpx;
		font-size: 32rpx;
		color: #2d3436;
		text-align: center;
		transition: all 0.3s ease;
		box-sizing: border-box;
	}

	.invite-input:focus {
		border-color: #74b9ff;
		background: #fff;
		box-shadow: 0 0 20rpx rgba(116, 185, 255, 0.3);
	}

	.invite-input.error {
		border-color: #ff7675;
		background: #fff5f5;
		box-shadow: 0 0 20rpx rgba(255, 118, 117, 0.3);
	}

	.submit-button {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #ffb347 0%, #ff8c24 100%);
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 24rpx rgba(255, 140, 36, 0.4);
		transition: all 0.2s ease;
		position: relative;
		overflow: hidden;
	}

	.submit-button:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 12rpx rgba(255, 140, 36, 0.3);
	}

	.submit-button.loading {
		background: linear-gradient(135deg, #ffcc80 0%, #ffb347 100%);
		box-shadow: none;
	}

	.submit-button text {
		font-size: 32rpx;
		color: #fff;
		font-weight: 600;
	}

	.loading-spinner {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid rgba(255, 255, 255, 0.3);
		border-top: 3rpx solid #fff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.welcome-decoration {
		position: absolute;
		bottom: 0;
		width: 100%;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.decoration-image {
		width: 580rpx;
		height: 580rpx;
	}
</style>

<style lang="scss" scoped>
	.swiper-container {
		width: 100%;
		height: 408upx;
		padding: 30upx;
		box-sizing: border-box;
	}
	
	.swiper {
		width: 100%;
		height: 100%;
		border-radius: 16upx;
		overflow: hidden;
		box-shadow: 0 8upx 20upx rgba(0, 0, 0, 0.15);
	}
	
	.swiper-item {
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 16upx;
		overflow: hidden;
	}
	
	.swiper-image {
		width: 100%;
		height: 100%;
		border-radius: 16upx;
	}
	
	.icon-grid {
		display: flex;
		flex-wrap: wrap;
		padding: 20upx;
		background-color: #fff;
		border-radius: 10upx;
		margin: 20upx 30upx;
	}
	
	.icon-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20upx 0;
	}
	
	.icon-image {
		width: 100upx;
		height: 100upx;
	}
	
	.icon-text {
		font-size: 24upx;
		margin-top: 10upx;
		color: #333;
	}
	
	.section-title {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 30upx 0;
	}
	
	.title-icon {
		width: 50upx;
		height: 30upx;
	}
	
	.title-text {
		// font-size: 32upx;
		// font-weight: bold;
		
		margin: 0 20upx;
	}
	
	.intro-container {
		padding: 0 30upx;
	}
	
	.intro-item {
		width: 100%;
		border-radius: 10upx;
		overflow: hidden;
	}
	
	.intro-image {
		width: 100%;
		border-radius: 10upx;
	}
	
	/* 企业资质轮播图 */
	.cert-container {
		padding: 20upx 30upx;
		position: relative;
		// margin-top: 30upx;
	}
	
	.cert-title {
		font-size: 28upx;
		color: #333;
		margin-bottom: 20upx;
		text-align: center;
		font-weight: bold;
	}
	
	.cert-grid {
		display: flex;
		flex-wrap: wrap;
	}
	
	.cert-grid-item {
		width: 25%;
		padding: 5upx;
		position: relative;
		aspect-ratio: 1/1;
		height: 150upx;
	}
	
	.cert-grid-image {
		width: 100%;
		height: 100%;
		border-radius: 6upx;
		object-fit: cover;
		box-shadow: 0 2upx 6upx rgba(0, 0, 0, 0.1);
	}
	
	.cert-grid-mask {
		position: absolute;
		top: 5upx;
		left: 5upx;
		right: 5upx;
		bottom: 5upx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 6upx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.cert-grid-more {
		font-size: 32upx;
		font-weight: bold;
		color: #fff;
	}
	
	.teacher-swiper {
		width: 100%;
		height: 400upx;
		padding: 0 20upx;
	}
	
	.teacher-image {
		width: 100%;
		height: 100%;
		border-radius: 10upx;
	}
	
	.video-container {
		padding: 0 20upx;
	}
	
	.main-video-wrap {
		width: 100%;
		position: relative;
		border-radius: 10upx;
		overflow: hidden;
		background-color: #000;
		aspect-ratio: 16/9; /* 设置宽高比为16:9，对于横屏视频 */
	}
	
	.video-cover {
		width: 100%;
		height: 100%;
		object-fit: contain; /* 保证图片完整显示 */
		background-color: #000;
	}
	
	.play-icon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 1;
		width: 80upx;
		height: 80upx;
		background: rgba(0, 0, 0, 0.4);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding-left: 6upx; /* 微调图标位置 */
	}
	
	.main-video {
		width: 100%;
		height: 100%;
		background-color: #000;
	}
	
	.product-list {
		padding: 0 20upx;
	}
	
	.scroll-view {
		width: 100%;
		white-space: nowrap;
	}
	
	.product-scroll {
		display: flex;
	}
	
	.product-item {
		display: inline-block;
		width: 280upx;
		margin-right: 20upx;
	}
	
	.product-image {
		width: 280upx;
		height: 200upx;
		border-radius: 10upx;
	}
	
	.product-name {
		font-size: 28upx;
		color: #333;
		margin-top: 10upx;
		display: block;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	.form-container {
		background-color: #fff;
		border-radius: 10upx;
		padding: 30upx;
		margin: 20upx;
	}
	
	.form-item {
		margin-bottom: 30upx;
	}
	
	.input-field {
		width: 100%;
		height: 80upx;
		border-radius: 10upx;
		background-color: #f5f5f5;
		padding: 0 20upx;
		font-size: 28upx;
	}
	
	.submit-btn {
		border: 0;
		width: 100%;
		height: 80upx;
		background: #2BCBD4;
		border-radius: 40upx;
		color: #fff;
		font-size: 30upx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.news-list {
		padding: 0 20upx;
	}
	
	.news-item {
		background-color: #fff;
		border-radius: 10upx;
		padding: 20upx;
		margin-bottom: 20upx;
	}
	
	.news-title {
		font-size: 28upx;
		color: #333;
		line-height: 1.5;
	}
	
	.news-info {
		display: flex;
		justify-content: space-between;
		margin-top: 10upx;
	}
	
	.news-date,
	.news-views {
		font-size: 24upx;
		color: #999;
	}
	
	.float-buttons {
		position: fixed;
		right: 20upx;
		bottom: 150upx;
		z-index: 10; /* 降低层级，使其低于loading页面 */
		display: flex;
		flex-direction: column;
		pointer-events: auto;
	}
	
	/* 当loading为true时隐藏悬浮按钮 */
	:deep(.u-loading-page--show) ~ .float-buttons {
		display: none;
	}
	
	.float-btn {
		// width: 100upx;
		// height: 100upx;
		// border-radius: 50%;
		// background-color: rgba(43, 203, 212, 0.8);
		display: flex;
		flex-direction: column;
		align-items: end;
		justify-content: center;
		// margin-bottom: 20upx;
	}
	
	.float-icon {
		width: 200upx;
		// height: 200upx;
	}
	
	.float-text {
		font-size: 20upx;
		color: #fff;
		margin-top: 5upx;
	}
	
	/* 自定义标题栏样式 */
	.custom-title {
		position: relative;
		
		height: 67upx;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		margin: 20upx 30upx ;
	}
	
	.title-content {
		position: absolute;
		transform: translate(-50%, -50%);
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.title-eng-text {
		font-size: 24upx;
		margin-top: 10upx;
	}
	
	/* 创始人说模块样式 */
	.founder-container {
		padding: 0 30upx;
		margin-top: 10upx;
	}
	
	.founder-item {
		width: 100%;
		height: 400upx;
		position: relative;
		border-radius: 16upx;
		overflow: hidden;
		margin-bottom: 30upx;
		box-shadow: 0 8upx 20upx rgba(0, 0, 0, 0.15);
	}
	
	.founder-image {
		width: 100%;
		height: 100%;
		border-radius: 16upx;
		transition: transform 0.6s;
	}
	
	.founder-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		// background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.6));
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0.8;
		transition: opacity 0.3s ease;
	}
	
	.founder-play-icon {
		padding-left: 10upx;
		width: 100upx;
		height: 100upx;
		border-radius: 50%;
		// background: rgba(43, 203, 212, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		transform: scale(1);
		transition: transform 0.3s ease, background-color 0.3s ease;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
	
	.founder-info {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 30upx;
		background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
		color: #fff;
	}
	
	.founder-title {
		font-size: 36upx;
		font-weight: bold;
		margin-bottom: 10upx;
		text-shadow: 0 2upx 4upx rgba(0,0,0,0.5);
	}
	
	.founder-digest {
		font-size: 26upx;
		line-height: 1.5;
		opacity: 0.85;
		white-space: pre-line;
	}
	
	/* 缩放效果 */
	.founder-item:active .founder-play-icon {
		// transform: scale(1);
		// background: rgba(43, 203, 212, 1);
	}
	
	.founder-item:active .founder-image {
		transform: scale(1.05);
	}
	
	/* 产品网格样式 */
	.product-grid {
		display: flex;
		flex-wrap: wrap;
		padding: 0 10upx;
	}
	
	.product-grid-item {
		width: 50%;
		padding: 10upx;
	}
	
	.product-grid-image {
		width: 100%;
		height: 200upx;
		border-radius: 10upx;
	}
	
	.product-grid-name {
		font-size: 28upx;
		color: #333;
		margin-top: 10upx;
		display: block;
		text-align: center;
	}
	
	/* 默认模块样式 */
	.default-module {
		padding: 0 20upx;
	}
	
	.default-item {
		margin-bottom: 20upx;
		background-color: #fff;
		border-radius: 10upx;
		overflow: hidden;
	}
	
	.default-image {
		width: 100%;
		height: 300upx;
	}
	
	.default-title {
		padding: 20upx;
		font-size: 28upx;
		color: #333;
	}
	
	.intro-text {
		padding: 20upx;
		font-size: 28upx;
		line-height: 1.6;
		color: #666;
	}
	
	/* 运营管理模块 */
	.operation-container {
		padding: 0 20upx;
	}
	
	.operation-image {
		width: 100%;
		border-radius: 10upx;
	}
	
	/* 自定义指示器样式 */
	.cert-pagination {
		position: absolute;
		bottom: 20upx;
		left: 50%;
		transform: translateX(-50%);
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 10upx;
		padding: 5upx 10upx;
	}
	
	.cert-pagination-text {
		font-size: 24upx;
		color: #fff;
	}
	
	/* 模板41样式 - 特色内容 */
	.feature-container {
		padding: 0 30upx;
		margin-bottom: 20upx;
	}
	
	.feature-main-item {
		width: 100%;
		height: 330upx;
		position: relative;
		border-radius: 16upx;
		overflow: hidden;
		box-shadow: 0 8upx 16upx rgba(0, 0, 0, 0.12);
	}
	
	.feature-main-image {
		width: 100%;
		height: 100%;
		border-radius: 16upx;
		object-fit: cover;
		transition: transform 0.4s ease;
	}
	
	.feature-main-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		// background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.3));
	}
	
	.feature-main-play-icon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 80upx;
		height: 80upx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 6upx;
	}
	
	.feature-main-info {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 30upx;
		background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
	}
	
	.feature-main-title {
		font-size: 36upx;
		font-weight: bold;
		color: #fff;
		margin-bottom: 10upx;
		text-shadow: 0 2upx 4upx rgba(0,0,0,0.5);
	}
	
	.feature-main-digest {
		font-size: 26upx;
		color: #fff;
		opacity: 0.9;
		line-height: 1.5;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	.feature-scroll-container {
		padding: 0 30upx;
		margin-top: 20upx;
	}
	
	.feature-scroll-view {
		width: 100%;
	}
	
	.feature-scroll-list {
		display: flex;
		padding-bottom: 10upx;
	}
	
	.feature-scroll-item {
		width: 280upx;
		margin-right: 20upx;
		position: relative;
		flex-shrink: 0;
	}
	
	.feature-scroll-image {
		width: 280upx;
		height: 150upx;
		border-radius: 12upx;
		object-fit: cover;
	}
	
	.feature-scroll-play-icon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 50upx;
		height: 50upx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 4upx;
		z-index: 1;
		pointer-events: none;
	}
	
	.feature-scroll-title {
		font-size: 26upx;
		color: #333;
		margin-top: 10upx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	/* 点击效果 */
	.feature-main-item:active .feature-main-image {
		transform: scale(1.05);
	}
	
	/* 模板4样式 - 滑动卡片列表 */
	.slide-cards-container {
		padding: 0 30upx;
		margin-bottom: 30upx;
	}
	
	.slide-scroll-view {
		width: 100%;
	}
	
	.slide-card-list {
		display: flex;
		justify-content: space-between;
		padding: 10upx 0;
	}
	
	.slide-card-item {
		width: 260upx;
		margin-right: 20upx;
		position: relative;
		flex-shrink: 0;
		border-radius: 12upx;
		overflow: hidden;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
		background-color: #fff;
	}
	
	/* 单个卡片样式 - 撑满容器 */
	.slide-card-item-single {
		width: calc(100% - 40upx);
		margin-right: 0;
	}
	
	/* 双卡片样式 - 各占一半 */
	.slide-card-item-double {
		width: calc(50% - 15upx);
		margin-right: 20upx;
	}
	.slide-card-item-double:last-child {
		margin-right: 0;
	}
	
	.slide-card-image {
		width: 260upx;
		height: 352upx;
		object-fit: cover;
	}
	
	/* 单个卡片图片样式 */
	.slide-card-image-single {
		width: 100%;
	}
	
	/* 双卡片图片样式 */
	.slide-card-image-double {
		width: 100%;
	}
	
	.slide-card-play-icon {
		position: absolute;
		top: 220upx; /* 图片高度的一半 */
		left: 130upx; /* 卡片宽度的一半 */
		transform: translate(-50%, -50%);
		width: 60upx;
		height: 60upx;
		background: rgba(0, 0, 0, 0.4);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 4upx;
		pointer-events: none;
	}
	
	/* 调整单张和双张卡片时播放图标位置 */
	.slide-card-item-single .slide-card-play-icon,
	.slide-card-item-double .slide-card-play-icon {
		left: 50%;
	}
	
	.slide-card-title {
		margin-bottom: 10upx;
		font-size: 26upx;
		color: #333;
		padding: 16upx;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	/* 模板3样式 - 全部横向滚动小图 */
	.horizontal-scroll-container {
		padding: 0 30upx;
		margin: 10upx 0 30upx;
	}
	
	.horizontal-scroll-view {
		width: 100%;
	}
	
	.horizontal-scroll-list {
		display: flex;
		padding: 10upx 0;
	}
	
	.horizontal-scroll-item {
		width: 294upx;
		margin-right: 20upx;
		position: relative;
		flex-shrink: 0;
	}
	
	.horizontal-scroll-image {
		width: 294upx;
		height: 162upx;
		border-radius: 12upx;
		object-fit: cover;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
	}
	
	.horizontal-scroll-play-icon {
		position: absolute;
		top: 80upx; /* 图片高度的一半 */
		left: 147upx; /* 卡片宽度的一半 */
		transform: translate(-50%, -50%);
		width: 50upx;
		height: 50upx;
		background: rgba(0, 0, 0, 0.4);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 4upx;
		pointer-events: none;
	}
	
	.vr-icon {
		width: 30upx;
		height: 30upx;
		display: block;
	}
	
	.horizontal-scroll-title {
		font-size: 24upx;
		color: #333;
		margin-top: 10upx;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	.feature-vr-icon {
		width: 50upx;
		height: 32upx;
		display: block;
	}
	
	.feature-vr-icon-small {
		width: 40upx;
		height: 24upx;
		display: block;
	}
	
	/* 新闻资讯模块样式 */
	.news-container {
		padding: 0 30upx;
	}
	
	.news-item {
		margin-bottom: 30upx;
		padding: 30upx;
		background-color: #ffffff;
		border-radius: 20upx;
		box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.06);
		transition: transform 0.2s ease;
	}
	
	.news-item:active {
		transform: scale(0.98);
	}
	
	.news-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16upx;
	}
	
	.news-title-area {
		flex: 1;
		padding-right: 20upx;
	}
	
	.news-title {
		font-size: 32upx;
		font-weight: bold;
		color: #333;
		line-height: 1.4;
	}
	
	.news-image-container {
		width: 200upx;
		height: 140upx;
		border-radius: 12upx;
		overflow: hidden;
		flex-shrink: 0;
	}
	
	.news-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.news-brief {
		font-size: 26upx;
		color: #666;
		line-height: 1.5;
		margin: 20upx 0;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	
	.news-info-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 24upx;
		color: #999;
	}
	
	.news-author {
		display: flex;
		align-items: center;
	}
	
	.news-avatar {
		width: 40upx;
		height: 40upx;
		border-radius: 50%;
		margin-right: 10upx;
	}
	
	.news-meta {
		display: flex;
		align-items: center;
	}
	
	.news-date {
		margin-right: 20upx;
	}
	
	.news-stats {
		display: flex;
		align-items: center;
	}
	
	.news-views {
		margin-left: 6upx;
	}
	
	.news-load-more {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80upx;
		margin: 10upx 0 30upx;
	}
	
	.load-more-btn {
		background-color: #2BCBD4;
		border-radius: 40upx;
		padding: 10upx 30upx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.load-more-btn text {
		color: #fff;
		font-size: 24upx;
	}
	
	.loading {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.loading-text {
		font-size: 24upx;
		color: #999;
		margin-left: 10upx;
	}
	
	.no-more {
		font-size: 24upx;
		color: #999;
	}
	
	/* 直播未开始弹窗样式 */
	.live-not-started-popup {
		width: 600upx;
		background-color: #fff;
		border-radius: 16upx;
		overflow: hidden;
	}
	
	.live-popup-header {
		background: linear-gradient(135deg, #2BCBD4, #2573D9);
		padding: 30upx;
		text-align: center;
	}
	
	.live-popup-title {
		color: #fff;
		font-size: 32upx;
		font-weight: bold;
	}
	
	.live-popup-content {
		padding: 30upx;
	}
	
	.live-popup-cover {
		width: 100%;
		// height: 300upx;
		border-radius: 12upx;
		margin-bottom: 20upx;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
	}
	
	.live-popup-info {
		padding: 10upx 0;
	}
	
	.live-info-title {
		font-size: 30upx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16upx;
	}
	
	.live-info-meta {
		display: flex;
		align-items: center;
		margin-bottom: 10upx;
	}
	
	.live-info-author, .live-info-time {
		margin-left: 10upx;
		font-size: 24upx;
		color: #666;
	}
	
	.live-popup-status {
		background-color: #F8F8F8;
		padding: 20upx 30upx;
		display: flex;
		align-items: center;
		border-top: 1px solid #EEEEEE;
		border-bottom: 1px solid #EEEEEE;
	}
	
	.live-status-icon {
		width: 60upx;
		height: 60upx;
	}
	
	.live-status-text {
		margin-left: 20upx;
		font-size: 26upx;
		color: #FF5F5F;
	}
	
	.live-popup-footer {
		padding: 30upx;
		display: flex;
		justify-content: center;
	}
	
	.live-popup-btn {
		width: 80%;
		height: 80upx;
		background: linear-gradient(135deg, #2BCBD4, #2573D9);
		border-radius: 40upx;
		color: #fff;
		font-size: 28upx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4upx 12upx rgba(43, 203, 212, 0.3);
		transition: transform 0.2s ease;
	}
	
	.live-popup-btn:active {
		transform: scale(0.96);
	}

	/* 模板61样式 - 第一个大图+下方横向滚动小图 */
	.main-feature-container {
		padding: 0 30upx;
		margin-bottom: 20upx;
	}

	.main-feature-item {
		width: 100%;
		height: 350upx;
		position: relative;
		border-radius: 16upx;
		overflow: hidden;
		box-shadow: 0 8upx 16upx rgba(0, 0, 0, 0.12);
	}

	.main-feature-image {
		width: 100%;
		height: 100%;
		border-radius: 16upx;
		object-fit: cover;
		transition: transform 0.4s ease;
	}

	.main-feature-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}

	.main-feature-play-icon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 80upx;
		height: 80upx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 6upx;
	}

	.main-feature-vr-icon {
		width: 50upx;
		height: 32upx;
		display: block;
	}

	.gallery-scroll-container {
		padding: 0 30upx;
		margin-top: 20upx;
	}

	.gallery-scroll-view {
		width: 100%;
	}

	.gallery-scroll-list {
		display: flex;
		padding: 10upx 0;
	}

	.gallery-scroll-item {
		width: 200upx;
		height: 120upx;
		margin-right: 15upx;
		position: relative;
		flex-shrink: 0;
		border-radius: 10upx;
		overflow: hidden;
	}

	.gallery-scroll-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		border-radius: 10upx;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;
	}

	.gallery-scroll-item:active .gallery-scroll-image {
		transform: scale(0.95);
	}

	.main-feature-item:active .main-feature-image {
		transform: scale(1.05);
	}

	/* 模板31样式 - 双排横向滚动列表 */
	.double-row-container {
		padding: 0 30upx;
		margin: 10upx 0 30upx;
	}

	.double-row-section {
		margin-bottom: 20upx;
	}

	.double-row-section:last-child {
		margin-bottom: 0;
	}

	.double-row-scroll-view {
		width: 100%;
	}

	.double-row-scroll-list {
		display: flex;
		padding: 10upx 0;
	}

	.double-row-scroll-item {
		width: 240upx;
		margin-right: 20upx;
		position: relative;
		flex-shrink: 0;
	}

	.double-row-scroll-image {
		width: 240upx;
		height: 135upx;
		border-radius: 12upx;
		object-fit: cover;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
	}

	.double-row-scroll-play-icon {
		position: absolute;
		top: 67.5upx; /* 图片高度的一半 */
		left: 120upx; /* 卡片宽度的一半 */
		transform: translate(-50%, -50%);
		width: 40upx;
		height: 40upx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 3upx;
		pointer-events: none;
	}

	.double-row-vr-icon {
		width: 24upx;
		height: 24upx;
		display: block;
	}

	.double-row-scroll-title {
		font-size: 22upx;
		color: #333;
		margin-top: 8upx;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	/* 模板42样式 - 竖屏小图横向滚动 */
	.vertical-scroll-container {
		padding: 0 30upx;
		margin: 10upx 0 30upx;
	}

	.vertical-scroll-view {
		width: 100%;
	}

	.vertical-scroll-list {
		display: flex;
		padding: 10upx 0;
	}

	.vertical-scroll-item {
		width: 200upx;
		margin-right: 20upx;
		position: relative;
		flex-shrink: 0;
	}

	.vertical-scroll-image {
		width: 200upx;
		height: 280upx;
		border-radius: 12upx;
		object-fit: cover;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
	}

	.vertical-scroll-play-icon {
		position: absolute;
		top: 140upx; /* 图片高度的一半 */
		left: 100upx; /* 卡片宽度的一半 */
		transform: translate(-50%, -50%);
		width: 50upx;
		height: 50upx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 4upx;
		pointer-events: none;
	}

	.vertical-vr-icon {
		width: 30upx;
		height: 30upx;
		display: block;
	}

	.vertical-scroll-title {
		font-size: 24upx;
		color: #333;
		margin-top: 10upx;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
</style>