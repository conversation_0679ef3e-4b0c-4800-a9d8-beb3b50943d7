<template>
	<view class="ai-chat-container">
		<u-navbar
			title="智能客服"
			:autoBack="true"
			:placeholder="true"
		>
			<view slot="right" class="navbar-right">
				<view class="icon-wrapper" @click="toggleCall">
					<u-icon name="phone" color="#333" size="24"></u-icon>
				</view>
				<view class="icon-wrapper" @click="toggleMute">
					<u-icon :name="isMuted ? 'volume-off' : 'volume'" color="#333" size="24"></u-icon>
				</view>
			</view>
		</u-navbar>
		
		<!-- 使用z-paging组件替代scroll-view -->
		<z-paging 
			ref="paging" 
			class="chat-content"
			:show-scrollbar="false" 
			refresher-background='#f6f6f6' 
			:use-page-scroll="false"
			@query="loadMessages"
			:show-loading-more-no-more-view="false"
			:hide-empty-view="true"
			style="padding-top: 100upx;"
		>
			<template #top>
				<!-- 顶部占位，如有需要 -->
			</template>
			
			<template #refresher="{refresherStatus}">
				<custom-refresher :status="refresherStatus" color="#000" />
			</template>

			
			<view v-if="loading" class="loading-container">
				<!-- 简单加载提示，移除u-loading组件 -->
				<view class="simple-loading">
					<text>加载中...</text>
				</view>
			</view>
			
			<view v-else class="message-list">
				<view class="time-divider">{{currentDate}}</view>
				
				<!-- 合并所有消息并按时间排序显示 -->
				<template v-for="(item, index) in sortedMessages">
					<!-- AI消息 -->
					<view class="message-item ai-message" v-if="!item.isUser" :key="'ai-'+index">
						<view class="avatar">
							<image src="/static/image/ai-avatar.png" mode="aspectFill"></image>
						</view>
						<view class="message-content">
							<view class="message-bubble" :class="{'system-message': item.isSystem}">
								<rich-text :nodes="item.content"></rich-text>
								<!-- 如果消息有音频，显示播放按钮 -->
								<view v-if="item.hasAudio" class="audio-player" @click="playAudio(item.audioUrl, index)">
									<u-icon :name="item.isPlaying ? 'pause-circle' : 'play-right-fill'" color="#2BCBD4" size="24"></u-icon>
									<text>{{item.isPlaying ? '暂停语音' : '播放语音'}}</text>
								</view>
							</view>
							<view class="message-time">{{item.time}}</view>
						</view>
					</view>
					
					<!-- 用户消息 -->
					<view class="message-item user-message" v-else :key="'user-'+index">
						<view class="message-content">
							<view class="message-bubble" :class="{'voice-message': item.isVoice}">
								<view v-if="item.isVoice" class="voice-message-content" @click="playVoice(item)">
									<view class="voice-icon">
										<view class="voice-wave-icon">
											<view class="wave-line" v-for="(line, i) in 4" :key="i"></view>
										</view>
									</view>
									<text>{{item.duration}}″</text>
								</view>
								<rich-text v-else :nodes="item.content"></rich-text>
							</view>
							<view class="message-time">{{item.time}}</view>
						</view>
						<view class="avatar">
							<image :src="userAvatar || '/static/image/default-avatar.png'" mode="aspectFill"></image>
						</view>
					</view>
				</template>
				
				<!-- 底部空白区域，防止内容被输入框遮挡 -->
				<view class="bottom-safe-area"></view>
			</view>
		</z-paging>
		
		<!-- 底部输入区域 - 固定在底部 -->
		<view class="chat-footer">
			<view class="input-area">
				<!-- 语音/键盘切换按钮 -->
				<view class="voice-keyboard-toggle" @click="toggleInputMode">
					<image src="/static/image/keyboard-fill.png" mode="" style="width: 50upx;height: 50upx;" v-if="isVoiceMode"></image>
					<u-icon name="mic" color="#333" size="26" v-else></u-icon>
				</view>
				
				<!-- 文本输入框 - 在非语音模式下显示 -->
				<input 
					v-if="!isVoiceMode"
					class="message-input" 
					v-model="inputMessage" 
					placeholder="请输入消息..." 
					confirm-type="send"
					@confirm="sendMessage"
				/>
				
				<!-- 语音按钮 - 在语音模式下显示 -->
				<view 
					v-else
					class="voice-button-large" 
					@touchstart="startRecording" 
					@touchend="stopRecording"
					@touchcancel="cancelRecording"
				>
					<text>按住说话</text>
				</view>
				
				<view class="attachment-button" @click="showActionSheet = true">
					<u-icon name="plus-circle" color="#333" size="26"></u-icon>
				</view>
				
				<!-- 发送按钮或停止按钮 -->
				<view v-if="inputMessage.trim() && !isVoiceMode && !isResponding" class="send-button" @click="sendMessage">
					<text>发送</text>
				</view>
				<view v-else-if="isResponding" class="stop-button" @click="stopResponse">
					<u-icon name="close" color="#ff3636" size="26"></u-icon>
				</view>
			</view>
			
			<!-- AI响应状态指示器 -->
			<view v-if="isResponding" class="responding-indicator">
				<text>AI正在思考中{{typingIndicator}}</text>
			</view>
		</view>
		
		<!-- 功能选择面板 -->
		<u-action-sheet 
			:actions="actionItems" 
			:show="showActionSheet"
			@select="handleActionSelect"
			@close="showActionSheet = false"
			cancel-text="取消">
		</u-action-sheet>
		
		<!-- 语音录制动画提示 -->
		<view class="voice-recording-tip" v-if="isRecording">
			<view class="voice-wave">
				<!-- 动态波形效果 -->
				<view class="voice-wave-container">
					<view 
						v-for="(item, index) in voiceWaveArray" 
						:key="index" 
						class="voice-wave-bar"
						:style="{height: item + 'rpx'}"
					></view>
				</view>
			</view>
			<text>{{recordingTip}}</text>
		</view>
		
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: false,
				inputMessage: '',
				messages: [],
				isRefreshing: false,
				showActionSheet: false,
				isMuted: false,
				isInCall: false,
				userAvatar: '',
				currentDate: this.formatDate(new Date()),
				isVoiceMode: false,
				isRecording: false,
				recordingTip: '松开发送，上滑取消',
				recorderManager: null,
				currentVoiceFile: '',
				recordingTimeout: null,
				recordDuration: 0,
				recordStartTime: 0,
				cancelRecordY: 0,
				voiceWaveArray: [10, 15, 20, 15, 10, 15, 20, 15, 10],
				animationTimer: null,
				websocket: null,
				wsUrl: 'ws://manage.boxuehao.cn:6808/websocket/agent/',
				userId: '',
				botId: '1',
				isConnected: false,
				audioSources: [],
				currentTrackIndex: 0,
				isPlaying: false,
				MAX_CHUNK_SIZE: 1024,
				actionItems: [
					{
						name: '发送图片',
						icon: 'photo'
					},
					{
						name: '发送文件',
						icon: 'file-text'
					},
					{
						name: '我的材料',
						icon: 'folder'
					}
				],
				aiMessages: [],
				userMessages: [],
				currentAIMessage: null,
				isCurrentResponseComplete: true,
				audioBuffer: [],
				
				// WebSocket心跳相关
				heartbeatTimer: null,
				reconnectTimer: null,
				heartbeatInterval: 30000, // 30秒发送一次心跳
				reconnectInterval: 3000, // 3秒尝试重连一次
				maxReconnectAttempts: 5,
				currentReconnectAttempts: 0,
				reconnecting: false,
				
				// 流式音频播放相关
				audioChunks: [],
				audioContext: null,
				isStreamPlaying: false,
				lastAudioTimestamp: 0,
				streamAudioQueue: [], // 存储待播放的音频块队列
				
				// 音频播放控制相关
				currentAudioContext: null, // 当前播放的音频上下文
				currentPlayingUrl: '', // 当前播放的音频URL
				
				// 响应状态相关
				isResponding: false, // AI是否正在响应
				typingIndicator: '...' // 打字指示器文本
			}
		},
		computed: {
			// 所有消息按时间排序
			allMessages() {
				// 实际应用中需要合并aiMessages和userMessages并按时间排序
				return this.messages;
			},
			// 是否有更多消息可加载
			hasMoreMessages() {
				// 实际应用中根据API返回判断是否有更多历史消息
				return false;
			},
			// 完整的WebSocket URL
			fullWsUrl() {
				if (!this.userId) {
					// 如果没有用户ID，则从缓存中获取
					const userInfo = uni.getStorageSync('userInfo');
					if (userInfo && userInfo.id) {
						this.userId = userInfo.id;
					} else {
						// 默认使用示例ID
						this.userId = '291';
					}
				}
				return `${this.wsUrl}${this.userId}/${this.botId}`;
			},
			// 所有消息合并并按时间排序
			sortedMessages() {
				// 合并AI消息和用户消息
				const allMessages = [...this.aiMessages, ...this.userMessages];
				
				// 为消息添加时间戳，用于排序
				allMessages.forEach(msg => {
					if (!msg.timestamp) {
						const [hours, minutes] = msg.time.split(':');
						const today = new Date();
						today.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);
						msg.timestamp = today.getTime();
					}
				});
				
				// 按时间戳排序
				return allMessages.sort((a, b) => a.timestamp - b.timestamp);
			}
		},
		onLoad() {
			// 初始化数据
			this.initData();
			
			// 获取用户信息
			this.getUserInfo();
			
			// 初始化录音管理器
			this.initRecorderManager();
			
			// 初始化WebSocket连接
			this.initWebSocket();
			
			// 启动打字指示器动画
			this.startTypingAnimation();
		},
		onUnload() {
			// 页面卸载时关闭WebSocket连接
			this.closeWebSocket();
			
			// 清理录音相关资源
			if (this.animationTimer) {
				clearInterval(this.animationTimer);
				this.animationTimer = null;
			}
			
			// 停止录音
			if (this.isRecording && this.recorderManager) {
				try {
					this.recorderManager.stop();
				} catch (e) {
					console.error('停止录音失败', e);
				}
			}
			
			// 停止所有正在播放的音频
			this.stopAllAudio();
			
			// 清理音频URL资源
			this.clearAudioResources();
			
			// 重置所有状态
			this.resetAllStates();
			
			console.log('页面已卸载，所有资源已清理');
		},
		onHide() {
			// 暂停所有音频播放
			this.stopAllAudio();
			
			console.log('页面已隐藏，暂停音频播放');
		},
		methods: {
			// 初始化WebSocket连接
			initWebSocket() {
				try {
					// 关闭已有连接
					this.closeWebSocket();
					
					// 重置消息和音频数据（仅在初次连接或用户手动刷新时）
					if (!this.reconnecting) {
						this.clearMessages();
					}
					
					// 创建新的WebSocket连接
					this.websocket = new WebSocket(this.fullWsUrl);
					
					// 连接打开事件
					this.websocket.onopen = (evt) => {
						console.log('WebSocket连接已打开');
						this.isConnected = true;
						this.currentReconnectAttempts = 0;
						this.reconnecting = false;
						
						// 清除重连定时器
						if (this.reconnectTimer) {
							clearTimeout(this.reconnectTimer);
							this.reconnectTimer = null;
						}
						
						// 开始心跳
						this.startHeartbeat();
						
						// 只在初次连接时显示系统消息，重连时不显示
						if (!this.reconnecting) {
							// 添加系统消息
							this.addSystemMessage('连接成功，您可以开始聊天了');
						}
					};
					
					// 连接关闭事件
					this.websocket.onclose = (evt) => {
						console.log('WebSocket连接已断开', evt);
						this.isConnected = false;
						
						// 停止心跳
						this.stopHeartbeat();
						
						// 尝试重连
						this.reconnect();
						
						// 只在非重连状态下显示断开消息
						if (!this.reconnecting) {
							// 添加系统消息
							// this.addSystemMessage('连接已断开'); // 不向用户显示断开消息
						}
					};
					
					// 连接错误事件
					this.websocket.onerror = (evt) => {
						console.error('WebSocket错误', evt);
						this.isConnected = false;
						
						// 只在非重连状态下显示错误消息
						if (!this.reconnecting) {
							// 添加系统消息
							// this.addSystemMessage('连接发生错误'); // 不向用户显示错误消息
						}
					};
					
					// 收到消息事件
					this.websocket.onmessage = (evt) => {
						this.handleWebSocketMessage(evt);
					};
				} catch (e) {
					console.error('初始化WebSocket失败', e);
					
					// 尝试重连
					this.reconnect();
					
					if (!this.reconnecting) {
						uni.showToast({
							title: 'WebSocket连接失败',
							icon: 'none'
						});
					}
				}
			},
			
			// 开始心跳
			startHeartbeat() {
				// 清除已有的心跳定时器
				this.stopHeartbeat();
				
				// 创建新的心跳定时器
				this.heartbeatTimer = setInterval(() => {
					if (this.isConnected && this.websocket && this.websocket.readyState === WebSocket.OPEN) {
						try {
							// 发送心跳包，使用一个特殊的字符串标识心跳
							this.websocket.send('[HEARTBEAT]');
							console.log('心跳包已发送');
						} catch (e) {
							console.error('发送心跳包失败', e);
							// 心跳失败，可能连接已断开，尝试重连
							this.reconnect();
						}
					} else if (this.websocket && this.websocket.readyState !== WebSocket.OPEN) {
						// WebSocket不在开启状态，尝试重连
						this.reconnect();
					}
				}, this.heartbeatInterval);
			},
			
			// 停止心跳
			stopHeartbeat() {
				if (this.heartbeatTimer) {
					clearInterval(this.heartbeatTimer);
					this.heartbeatTimer = null;
				}
			},
			
			// 重连逻辑
			reconnect() {
				// 如果已经在尝试重连，则不重复操作
				if (this.reconnecting) return;
				
				// 如果达到最大重连次数，则停止重连
				if (this.currentReconnectAttempts >= this.maxReconnectAttempts) {
					console.log('达到最大重连次数，停止重连');
					this.reconnecting = false;
					this.currentReconnectAttempts = 0;
					
					// 显示消息（可选，视需求而定）
					// uni.showToast({
					//     title: '网络连接不稳定，请稍后再试',
					//     icon: 'none'
					// });
					return;
				}
				
				// 标记为正在重连
				this.reconnecting = true;
				this.currentReconnectAttempts++;
				
				console.log(`尝试第 ${this.currentReconnectAttempts} 次重连`);
				
				// 清除旧的重连定时器
				if (this.reconnectTimer) {
					clearTimeout(this.reconnectTimer);
				}
				
				// 设置新的重连定时器
				this.reconnectTimer = setTimeout(() => {
					// 尝试初始化WebSocket
					this.initWebSocket();
				}, this.reconnectInterval);
			},
			
			// 关闭WebSocket连接
			closeWebSocket() {
				// 停止心跳
				this.stopHeartbeat();
				
				// 停止重连
				if (this.reconnectTimer) {
					clearTimeout(this.reconnectTimer);
					this.reconnectTimer = null;
				}
				
				if (this.websocket) {
					try {
						this.websocket.close();
					} catch (e) {
						console.error('关闭WebSocket失败', e);
					}
					this.websocket = null;
					this.isConnected = false;
				}
			},
			
			// 处理WebSocket消息
			handleWebSocketMessage(event) {
				if (typeof event.data === 'string') {
					// 处理文本消息
					console.log('收到WebSocket文本消息', event.data);
					
					// 忽略心跳响应
					if (event.data === '[HEARTBEAT_ACK]' || event.data === '[HEARTBEAT]') {
						console.log('收到心跳响应');
						return;
					}
					
					// 检查是否是新会话或当前会话已结束
					const isNewResponse = this.isCurrentResponseComplete || !this.currentAIMessage;
					
					if (isNewResponse) {
						// 创建新的AI消息
						this.currentAIMessage = {
							content: event.data,
							time: this.formatTime(new Date()),
							timestamp: Date.now(),
							isUser: false,
							isComplete: false
						};
						// 添加到AI消息列表
						this.aiMessages.push(this.currentAIMessage);
						this.isCurrentResponseComplete = false;
						
						// 新会话开始时，重置音频相关状态
						this.resetStreamAudioState();
					} else {
						// 追加到当前消息
						this.currentAIMessage.content += event.data;
						// 更新时间戳
						this.currentAIMessage.timestamp = Date.now();
					}
					
					// 滚动到底部
					this.scrollToBottom();
				} else {
					// 处理二进制消息（音频）
					console.log('收到WebSocket二进制消息', event.data.size);
					
					// 检查是否是结束标记 (2字节大小的数据)
					if (event.data.size === 2) {
						// 读取Blob内容以确认是否为结束标记
						const reader = new FileReader();
						reader.onload = () => {
							const arrayBuffer = reader.result;
							const uint8Array = new Uint8Array(arrayBuffer);
							
							// 检查是否是结束标记 [255, 255]
							if (uint8Array[0] === 255 && uint8Array[1] === 255) {
								console.log('收到结束标记');
								
								// 处理完成的音频
								if (this.audioBuffer && this.audioBuffer.length > 0) {
									this.processCompleteAudio();
								}
								
								// 处理流式播放结束
								this.finalizeStreamAudio();
								
								// 标记文本响应也结束
								if (this.currentAIMessage) {
									this.isCurrentResponseComplete = true;
									this.currentAIMessage.isComplete = true;
								}
								
								// 标记响应结束
								this.isResponding = false;
							} else {
								// 不是结束标记，将其添加到缓冲区
								if (!this.audioBuffer) {
									this.audioBuffer = [];
								}
								this.audioBuffer.push(uint8Array);
								
								// 将音频块加入流式播放
								this.processAudioChunk(uint8Array);
							}
						};
						reader.readAsArrayBuffer(event.data);
					} else {
						// 普通音频数据
						const reader = new FileReader();
						reader.onload = () => {
							const arrayBuffer = reader.result;
							const uint8Array = new Uint8Array(arrayBuffer);
							
							// 将数据添加到完整音频缓冲区
							if (!this.audioBuffer) {
								this.audioBuffer = [];
							}
							this.audioBuffer.push(uint8Array);
							
							// 添加到流式播放队列并播放
							this.processAudioChunk(uint8Array);
						};
						reader.readAsArrayBuffer(event.data);
					}
				}
			},
			
			// 重置流式音频状态
			resetStreamAudioState() {
				// 停止之前可能存在的流式播放
				this.isStreamPlaying = false;
				this.streamAudioQueue = [];
				this.audioChunks = [];
				this.lastAudioTimestamp = 0;
			},
			
			// 处理单个音频块用于流式播放
			processAudioChunk(uint8Array) {
				// 创建音频块的拷贝并添加到队列
				const chunk = new Uint8Array(uint8Array);
				this.audioChunks.push(chunk);
				
				// 创建Blob对象
				const audioBlob = new Blob([chunk], {type: 'audio/wav'});
				const audioUrl = URL.createObjectURL(audioBlob);
				
				// 将新的音频块加入播放队列
				this.streamAudioQueue.push(audioUrl);
				
				// 如果当前没有正在播放，开始播放
				if (!this.isStreamPlaying) {
					this.playNextAudioChunk();
				}
			},
			
			// 播放下一个音频块
			playNextAudioChunk() {
				if (this.streamAudioQueue.length === 0) {
					this.isStreamPlaying = false;
					return;
				}
				
				this.isStreamPlaying = true;
				
				// 获取队列中的下一个音频URL
				const audioUrl = this.streamAudioQueue.shift();
				
				// 播放音频
				try {
					const audioCtx = uni.createInnerAudioContext();
					audioCtx.src = audioUrl;
					audioCtx.autoplay = true;
					
					// 设置音量 - 确保响应静音状态
					audioCtx.volume = this.isMuted ? 0 : 1;
					
					// 播放结束后释放资源并继续播放下一块
					audioCtx.onEnded(() => {
						// 释放URL对象
						URL.revokeObjectURL(audioUrl);
						
						// 继续播放下一个音频块
						this.playNextAudioChunk();
					});
					
					// 播放错误处理
					audioCtx.onError((err) => {
						console.error('音频块播放错误', err);
						// 继续尝试播放下一块
						this.playNextAudioChunk();
					});
				} catch (e) {
					console.error('创建音频播放器失败', e);
					this.playNextAudioChunk(); // 尝试继续播放
				}
			},
			
			// 流式播放完成后的处理
			finalizeStreamAudio() {
				// 等待所有音频块播放完毕
				this.isStreamPlaying = false;
				
				// 不需要额外处理，因为完整的音频已经在processCompleteAudio中处理
				console.log('流式音频播放已完成');
			},
			
			// 处理完整的音频数据
			processCompleteAudio() {
				// 合并所有音频数据
				let totalLength = 0;
				this.audioBuffer.forEach(buffer => {
					totalLength += buffer.length;
				});
				
				const mergedBuffer = new Uint8Array(totalLength);
				let offset = 0;
				
				this.audioBuffer.forEach(buffer => {
					mergedBuffer.set(buffer, offset);
					offset += buffer.length;
				});
				
				// 创建Blob对象，明确指定MIME类型
				const audioBlob = new Blob([mergedBuffer], {type: 'audio/wav'});
				const audioUrl = URL.createObjectURL(audioBlob);
				
				console.log('创建音频Blob', audioBlob.size, audioBlob.type);
				
				// 添加到音频源
				this.audioSources.push(audioUrl);
				
				// 添加或更新AI消息中的音频信息
				if (!this.currentAIMessage) {
					// 如果没有当前文本消息，创建一个新的音频消息
					const audioMessage = {
						content: '[语音回复]',
						time: this.formatTime(new Date()),
						timestamp: Date.now(),
						isUser: false,
						hasAudio: true,
						audioUrl: audioUrl,
						isPlaying: false // 添加播放状态标记
					};
					this.aiMessages.push(audioMessage);
				} else {
					// 将音频添加到当前文本消息
					this.currentAIMessage.hasAudio = true;
					this.currentAIMessage.audioUrl = audioUrl;
					this.currentAIMessage.isPlaying = false; // 添加播放状态标记
				}
				
				// 清空音频缓冲区
				this.audioBuffer = [];
				
				// 滚动到底部
				this.scrollToBottom();
			},
			
			// 播放或暂停音频
			playAudio(audioUrl, messageIndex) {
				console.log('播放/暂停音频', audioUrl);
				
				// 静音处理
				if (this.isMuted) {
					console.log('当前为静音状态，不播放音频');
					uni.showToast({
						title: '当前为静音状态',
						icon: 'none'
					});
					return;
				}
				
				// 停止所有片段播放
				this.isStreamPlaying = false;
				this.streamAudioQueue = [];
				
				// 如果正在播放同一个音频，则暂停播放
				if (this.currentPlayingUrl === audioUrl && this.currentAudioContext) {
					// 查找当前正在播放的消息
					const playingMessage = this.aiMessages.find(msg => msg.audioUrl === audioUrl);
					if (playingMessage) {
						playingMessage.isPlaying = false;
					}
					
					this.currentAudioContext.destroy(); // 销毁音频实例
					this.currentAudioContext = null;
					this.currentPlayingUrl = '';
					this.isPlaying = false;
					console.log('音频已暂停');
					return;
				}
				
				// 如果正在播放其他音频，先停止它
				if (this.currentAudioContext) {
					// 查找当前正在播放的消息
					const oldPlayingMessage = this.aiMessages.find(msg => msg.audioUrl === this.currentPlayingUrl);
					if (oldPlayingMessage) {
						oldPlayingMessage.isPlaying = false;
					}
					
					this.currentAudioContext.destroy();
					this.currentAudioContext = null;
					this.currentPlayingUrl = '';
				}
				
				// 开始播放新的音频
				try {
					// 查找并更新消息的播放状态
					const messageToPlay = this.aiMessages.find(msg => msg.audioUrl === audioUrl);
					if (messageToPlay) {
						messageToPlay.isPlaying = true;
					}
					
					// 创建新的音频上下文
					const audioCtx = uni.createInnerAudioContext();
					audioCtx.src = audioUrl;
					audioCtx.autoplay = true;
					
					// 保存当前播放的音频上下文和URL
					this.currentAudioContext = audioCtx;
					this.currentPlayingUrl = audioUrl;
					this.isPlaying = true;
					
					audioCtx.onPlay(() => {
						console.log('音频播放开始');
					});
					
					audioCtx.onEnded(() => {
						console.log('音频播放结束');
						
						// 更新消息的播放状态
						if (messageToPlay) {
							messageToPlay.isPlaying = false;
						}
						
						// 重置播放状态
						this.isPlaying = false;
						this.currentAudioContext = null;
						this.currentPlayingUrl = '';
					});
					
					audioCtx.onError((res) => {
						console.error('音频播放错误', res);
						
						// 更新消息的播放状态
						if (messageToPlay) {
							messageToPlay.isPlaying = false;
						}
						
						// 重置播放状态
						this.isPlaying = false;
						this.currentAudioContext = null;
						this.currentPlayingUrl = '';
						
						// 显示错误提示
						uni.showToast({
							title: '音频播放失败',
							icon: 'none'
						});
					});
				} catch (e) {
					console.error('创建音频播放器失败', e);
					this.isPlaying = false;
					
					uni.showToast({
						title: '创建音频播放器失败',
						icon: 'none'
					});
				}
			},
			
			// 初始化数据
			initData() {
				// 重置音频相关数据
				this.audioSources = [];
				this.currentTrackIndex = 0;
				this.isPlaying = false;
				this.audioBuffer = null;
				
				// 重置消息流相关数据
				this.currentAIMessage = null;
				this.isCurrentResponseComplete = true;
			},
			
			// 获取用户信息
			getUserInfo() {
				// 从缓存或API获取用户头像等信息
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.userAvatar = userInfo.avatarUrl;
					if (userInfo.id) {
						this.userId = userInfo.id;
					}
				}
			},
			
			// 初始化录音管理器
			initRecorderManager() {
				try {
					// 检查平台是否支持录音功能
					if (!uni.getRecorderManager) {
						console.error('当前平台不支持录音功能');
						uni.showToast({
							title: '当前平台不支持录音',
							icon: 'none'
						});
						return;
					}
					
					// 获取全局唯一的录音管理器
					this.recorderManager = uni.getRecorderManager();
					
					// 检查录音管理器是否有效
					if (!this.recorderManager) {
						console.error('录音管理器初始化失败');
						uni.showToast({
							title: '录音功能不可用',
							icon: 'none'
						});
						return;
					}
					
					// 监听录音开始事件
					if (typeof this.recorderManager.onStart === 'function') {
						this.recorderManager.onStart(() => {
							console.log('录音开始');
							this.isRecording = true;
							this.recordStartTime = Date.now();
							
							// 开始波形动画
							this.startWaveAnimation();
							
							// 设置录音超时（最长60秒）
							this.recordingTimeout = setTimeout(() => {
								if (this.isRecording) {
									this.stopRecording();
								}
							}, 60000);
						});
					}
					
					// 监听录音结束事件
					if (typeof this.recorderManager.onStop === 'function') {
						this.recorderManager.onStop((res) => {
							console.log('录音结束', res);
							this.isRecording = false;
							clearTimeout(this.recordingTimeout);
							
							// 停止波形动画
							if (this.animationTimer) {
								clearInterval(this.animationTimer);
								this.animationTimer = null;
							}
							
							// 计算录音时长
							this.recordDuration = Math.round((Date.now() - this.recordStartTime) / 1000);
							
							// 处理录音文件
							if (res && res.tempFilePath) {
								this.currentVoiceFile = res.tempFilePath;
								// 发送语音消息
								this.sendVoiceMessage(res.tempFilePath, this.recordDuration);
							}
						});
					}
					
					// 监听录音错误事件
					if (typeof this.recorderManager.onError === 'function') {
						this.recorderManager.onError((res) => {
							console.error('录音失败', res);
							this.isRecording = false;
							clearTimeout(this.recordingTimeout);
							
							uni.showToast({
								title: '录音失败',
								icon: 'none'
							});
						});
					}
					
					// 监听声音大小变化事件 - 仅在支持的平台使用
					if (this.recorderManager.onFrameRecorded && typeof this.recorderManager.onFrameRecorded === 'function') {
						this.recorderManager.onFrameRecorded((res) => {
							// 处理音频数据
						});
					}
				} catch (e) {
					console.error('初始化录音管理器失败', e);
					this.recorderManager = null; // 确保失败时设为null
					uni.showToast({
						title: '录音功能初始化失败',
						icon: 'none'
					});
				}
			},
			
			// 开始波形动画 - 使用随机波形代替依赖于分贝值的波形
			startWaveAnimation() {
				// 清除之前的动画定时器
				if (this.animationTimer) {
					clearInterval(this.animationTimer);
				}
				
				// 创建新的动画定时器 - 模拟波形变化
				this.animationTimer = setInterval(() => {
					let baseHeight = 10;
					let maxHeight = 40;
					
					let newWave = [];
					for (let i = 0; i < 9; i++) {
						// 生成随机波形高度
						let randomFactor = Math.random(); // 0~1之间的随机数
						let height = baseHeight + (maxHeight - baseHeight) * randomFactor;
						newWave.push(Math.round(height));
					}
					
					this.voiceWaveArray = newWave;
				}, 150); // 每150ms更新一次波形
			},
			
			// z-paging的查询方法
			loadMessages(pageNo, pageSize) {
				if (pageNo === 1) {
					// 第一页，加载最新消息
					this.loadChatHistory();
				} else {
					// 加载更多历史消息
					this.loadMoreHistoryMessages(pageNo, pageSize);
				}
			},
			
			// 加载聊天历史
			loadChatHistory() {
				this.loading = true;
				// 实际应用中这里应该调用API获取聊天记录
				setTimeout(() => {
					this.loading = false;
					
					// 如果没有连接，尝试连接WebSocket
					if (!this.isConnected) {
						this.initWebSocket();
					}
					
					// 通知z-paging加载完成
					this.$refs.paging && this.$refs.paging.complete();
				}, 800);
			},
			
			// 加载更多历史消息
			loadMoreHistoryMessages(pageNo, pageSize) {
				// 实现分页加载更多历史消息逻辑
				this.loading = true;
				// 模拟API请求
				setTimeout(() => {
					// 加载更多历史消息
					this.loading = false;
					
					if (this.hasMoreMessages) {
						// 有更多数据
						this.$refs.paging && this.$refs.paging.complete();
					} else {
						// 没有更多数据了
						this.$refs.paging && this.$refs.paging.completeByTotal(this.aiMessages.length + this.userMessages.length);
					}
				}, 800);
			},
			
			// 发送消息
			sendMessage() {
				if (!this.inputMessage.trim()) return;
				
				// 检查是否正在响应中，如果是则不允许发送新消息
				if (this.isResponding) {
					uni.showToast({
						title: 'AI正在回复中，请稍候...',
						icon: 'none'
					});
					return;
				}
				
				// 检查WebSocket连接
				if (!this.isConnected) {
					uni.showToast({
						title: '连接已断开，请重新连接',
						icon: 'none'
					});
					
					// 尝试重新连接
					this.initWebSocket();
					return;
				}
				
				// 获取消息内容
				const messageContent = this.inputMessage.trim();
				
				// 添加到用户消息列表
				this.addUserMessage(messageContent);
				
				// 清空输入框
				this.inputMessage = '';
				
				// 重置音频和消息流状态
				this.resetMessageState();
				
				// 立即进入响应状态
				this.isResponding = true;
				
				// 使用WebSocket发送消息
				try {
					// 创建文本编码器
					const encoder = new TextEncoder();
					const uint8Array = encoder.encode(messageContent);
					
					// 发送文本消息
					this.websocket.send(uint8Array);
					
					// 发送结束标记
					const endMarker = new Uint8Array([255, 255]);
					this.websocket.send(endMarker.buffer);
					
					console.log('消息已发送');
				} catch (e) {
					console.error('发送消息失败', e);
					// 发送失败时重置响应状态
					this.isResponding = false;
					uni.showToast({
						title: '发送失败',
						icon: 'none'
					});
				}
			},
			
			// 重置消息状态
			resetMessageState() {
				// 标记当前响应已完成
				this.isCurrentResponseComplete = true;
				this.currentAIMessage = null;
				
				// 清空音频缓冲区
				this.audioBuffer = [];
			},
			
			// 发送语音消息
			sendVoiceMessage(filePath, duration) {
				// 检查WebSocket连接
				if (!this.isConnected) {
					uni.showToast({
						title: '连接已断开，请重新连接',
						icon: 'none'
					});
					
					// 尝试重新连接
					this.initWebSocket();
					return;
				}
				
				// 构造语音消息对象
				const message = {
					content: '[语音消息]',
					time: this.formatTime(new Date()),
					timestamp: Date.now(),
					isUser: true,
					isVoice: true,
					filePath: filePath, // 语音文件路径
					duration: duration || 1 // 语音时长(秒)
				};
				
				// 添加到用户消息列表
				this.userMessages.push(message);
				
				// 滚动到底部
				this.scrollToBottom();
				
				// 重置音频和消息流状态
				this.resetMessageState();
				
				// 读取语音文件并通过WebSocket发送
				uni.getFileSystemManager().readFile({
					filePath: filePath,
					success: (res) => {
						try {
							// 将文件数据转换为Uint8Array
							const audioData = new Uint8Array(res.data);
							
							// 分片发送音频数据
							let offset = 0;
							while (offset < audioData.byteLength) {
								const end = Math.min(offset + this.MAX_CHUNK_SIZE, audioData.byteLength);
								const chunk = audioData.slice(offset, end);
								
								// 发送音频数据片段
								this.websocket.send(chunk.buffer);
								
								offset = end;
							}
							
							// 发送结束标记
							const endMarker = new Uint8Array([255, 255]);
							this.websocket.send(endMarker.buffer);
							
							console.log('语音消息已发送');
						} catch (e) {
							console.error('发送语音消息失败', e);
							uni.showToast({
								title: '发送失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						console.error('读取语音文件失败', err);
						uni.showToast({
							title: '发送失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 播放语音消息
			playVoice(message) {
				if (!message.filePath) return;
				
				try {
					const innerAudioContext = uni.createInnerAudioContext();
					innerAudioContext.src = message.filePath;
					innerAudioContext.autoplay = true;
					
					innerAudioContext.onPlay(() => {
						console.log('开始播放语音');
					});
					
					innerAudioContext.onError((res) => {
						console.error('播放语音失败', res);
						uni.showToast({
							title: '播放失败',
							icon: 'none'
						});
					});
				} catch (e) {
					console.error('播放语音失败', e);
					uni.showToast({
						title: '播放失败',
						icon: 'none'
					});
				}
			},
			
			// 滚动到底部
			scrollToBottom() {
				this.$nextTick(() => {
					// 延迟执行滚动，确保消息渲染完成
					setTimeout(() => {
						// 使用z-paging的滚动到底部方法
						this.$refs.paging && this.$refs.paging.scrollToBottom(false);
					}, 100);
				});
			},
			
			// 处理附件选择
			handleActionSelect(index) {
				switch(index) {
					case 0: // 发送图片
						this.chooseImage();
						break;
					case 1: // 发送文件
						this.chooseFile();
						break;
					case 2: // 我的材料
						uni.navigateTo({
							url: '/pages/materials/index'
						});
						break;
				}
			},
			
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						// 处理选择的图片
						const tempFilePath = res.tempFilePaths[0];
						this.uploadFile(tempFilePath, 'image');
					}
				});
			},
			
			// 选择文件
			chooseFile() {
				// 选择文件逻辑，具体API根据平台支持情况调整
				uni.showToast({
					title: '选择文件功能开发中',
					icon: 'none'
				});
			},
			
			// 上传文件
			uploadFile(filePath, type) {
				uni.showLoading({
					title: '正在上传...'
				});
				
				// 检查WebSocket连接
				if (!this.isConnected) {
					uni.hideLoading();
					uni.showToast({
						title: '连接已断开，请重新连接',
						icon: 'none'
					});
					
					// 尝试重新连接
					this.initWebSocket();
					return;
				}
				
				// 读取文件
				uni.getFileSystemManager().readFile({
					filePath: filePath,
					success: (res) => {
						try {
							// 添加到用户消息列表
							const messageContent = type === 'image' ? 
								`<img src="${filePath}" style="max-width: 200px;" />` : 
								'[文件]';
							
							// 添加用户消息
							this.addUserMessage(messageContent);
							
							// 将文件数据转换为Uint8Array
							const fileData = new Uint8Array(res.data);
							
							// 分片发送文件数据
							let offset = 0;
							while (offset < fileData.byteLength) {
								const end = Math.min(offset + this.MAX_CHUNK_SIZE, fileData.byteLength);
								const chunk = fileData.slice(offset, end);
								
								// 发送文件数据片段
								this.websocket.send(chunk.buffer);
								
								offset = end;
							}
							
							// 发送结束标记
							const endMarker = new Uint8Array([255, 255]);
							this.websocket.send(endMarker.buffer);
							
							console.log('文件已发送');
							uni.hideLoading();
						} catch (e) {
							console.error('发送文件失败', e);
							uni.hideLoading();
							uni.showToast({
								title: '发送失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						console.error('读取文件失败', err);
						uni.hideLoading();
						uni.showToast({
							title: '读取文件失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 切换通话状态
			toggleCall() {
				this.isInCall = !this.isInCall;
				uni.showToast({
					title: this.isInCall ? '通话已开始' : '通话已结束',
					icon: 'none'
				});
			},
			
			// 切换静音状态
			toggleMute() {
				this.isMuted = !this.isMuted;
				
				// 更新当前正在播放的音频状态
				if (this.currentAudioContext) {
					this.currentAudioContext.volume = this.isMuted ? 0 : 1;
				}
				
				// 提示用户
				uni.showToast({
					title: this.isMuted ? '已静音' : '已取消静音',
					icon: 'none'
				});
			},
			
			// 下拉刷新 - 会自动触发loadMessages
			onRefresh() {
				// z-paging已经内部处理了刷新逻辑
			},
			
			// 格式化日期
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 格式化时间
			formatTime(date) {
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${hours}:${minutes}`;
			},
			
			// 切换输入模式（语音/键盘）
			toggleInputMode() {
				this.isVoiceMode = !this.isVoiceMode;
			},
			
			// 开始录音
			startRecording(e) {
				// 防止多次调用
				if (this.isRecording) return;
				
				try {
					// 检查录音管理器是否可用
					if (!this.recorderManager) {
						console.error('录音管理器未初始化');
						uni.showToast({
							title: '录音功能不可用',
							icon: 'none'
						});
						return;
					}
					
					// 检查start方法是否存在
					if (typeof this.recorderManager.start !== 'function') {
						console.error('录音管理器不支持start方法');
						uni.showToast({
							title: '录音功能不支持',
							icon: 'none'
						});
						return;
					}
					
					// 记录触摸开始的Y坐标，用于判断上滑取消
					if (e && e.touches && e.touches[0]) {
						this.cancelRecordY = e.touches[0].clientY;
					}
					
					// 设置录音参数
					const options = {
						duration: 60000, // 最长录音时间，单位ms
						sampleRate: 16000, // 采样率
						numberOfChannels: 1, // 录音通道数
						encodeBitRate: 96000, // 编码码率
						format: 'mp3' // 音频格式
					};
					
					// 开始录音
					this.recorderManager.start(options);
				} catch (e) {
					console.error('开始录音失败', e);
					this.isRecording = false; // 确保状态正确
					uni.showToast({
						title: '录音失败',
						icon: 'none'
					});
				}
			},
			
			// 结束录音并发送
			stopRecording(e) {
				if (!this.isRecording) return;
				
				try {
					// 检查录音管理器是否可用
					if (!this.recorderManager || typeof this.recorderManager.stop !== 'function') {
						console.error('录音管理器不可用或不支持stop方法');
						this.isRecording = false;
						clearTimeout(this.recordingTimeout);
						
						if (this.animationTimer) {
							clearInterval(this.animationTimer);
							this.animationTimer = null;
						}
						
						uni.showToast({
							title: '录音功能不支持',
							icon: 'none'
						});
						return;
					}
					
					// 检查是否为上滑取消
					if (e && e.changedTouches && e.changedTouches[0]) {
						const curY = e.changedTouches[0].clientY;
						const diffY = this.cancelRecordY - curY;
						
						// 如果上滑距离超过50，则取消发送
						if (diffY > 50) {
							this.cancelRecording();
							return;
						}
					}
					
					// 停止录音
					this.recorderManager.stop();
				} catch (e) {
					console.error('停止录音失败', e);
					this.isRecording = false;
					clearTimeout(this.recordingTimeout);
					
					// 清除动画定时器
					if (this.animationTimer) {
						clearInterval(this.animationTimer);
						this.animationTimer = null;
					}
					
					uni.showToast({
						title: '录音失败',
						icon: 'none'
					});
				}
			},
			
			// 取消录音
			cancelRecording() {
				if (!this.isRecording) return;
				
				try {
					// 检查录音管理器是否可用
					if (!this.recorderManager || typeof this.recorderManager.stop !== 'function') {
						console.error('录音管理器不可用或不支持stop方法');
						this.isRecording = false;
						this.recordingTip = '录音已取消';
						
						setTimeout(() => {
							this.recordingTip = '松开发送，上滑取消';
						}, 500);
						
						// 清除录音文件
						this.currentVoiceFile = '';
						
						// 清除动画定时器
						if (this.animationTimer) {
							clearInterval(this.animationTimer);
							this.animationTimer = null;
						}
						
						uni.showToast({
							title: '录音功能不支持',
							icon: 'none'
						});
						return;
					}
					
					this.recordingTip = '已取消录音';
					setTimeout(() => {
						this.recordingTip = '松开发送，上滑取消';
						this.isRecording = false;
					}, 500);
					
					// 停止录音但不发送
					this.recorderManager.stop();
					
					// 清除录音文件
					this.currentVoiceFile = '';
					
					uni.showToast({
						title: '已取消录音',
						icon: 'none'
					});
				} catch (e) {
					console.error('取消录音失败', e);
					this.isRecording = false;
					
					// 清除动画定时器
					if (this.animationTimer) {
						clearInterval(this.animationTimer);
						this.animationTimer = null;
					}
					
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					});
				}
			},
			
			// 添加用户消息
			addUserMessage(content, isVoice = false, filePath = '', duration = 0) {
				const message = {
					content: content,
					time: this.formatTime(new Date()),
					timestamp: Date.now(),
					isUser: true,
					isVoice: isVoice,
					filePath: filePath,
					duration: duration
				};
				
				// 添加到用户消息列表
				this.userMessages.push(message);
				
				// 滚动到底部
				this.scrollToBottom();
			},
			
			// 添加AI消息
			addAIMessage(content) {
				const message = {
					content: content,
					time: this.formatTime(new Date()),
					timestamp: Date.now(),
					isUser: false
				};
				
				// 添加到AI消息列表
				this.aiMessages.push(message);
				
				// 滚动到底部
				this.scrollToBottom();
			},
			
			// 添加系统消息
			addSystemMessage(content) {
				const message = {
					content: content,
					time: this.formatTime(new Date()),
					timestamp: Date.now(),
					isUser: false,
					isSystem: true
				};
				
				// 添加到AI消息列表（作为系统提示）
				this.aiMessages.push(message);
				
				// 滚动到底部
				this.scrollToBottom();
			},
			
			// 清空消息列表
			clearMessages() {
				this.aiMessages = [];
				this.userMessages = [];
				this.resetMessageState();
				this.audioSources = [];
				this.currentTrackIndex = 0;
				this.isPlaying = false;
			},
			
			// 停止所有音频播放
			stopAllAudio() {
				// 停止流式播放
				this.isStreamPlaying = false;
				this.streamAudioQueue = [];
				
				// 停止完整音频播放
				this.isPlaying = false;
				
				// 如果有正在播放的音频，停止它
				if (this.currentAudioContext) {
					try {
						this.currentAudioContext.destroy();
					} catch (e) {
						console.error('停止音频播放失败', e);
					}
					this.currentAudioContext = null;
					this.currentPlayingUrl = '';
				}
				
				// 重置所有消息的播放状态
				this.aiMessages.forEach(msg => {
					if (msg.hasAudio) {
						msg.isPlaying = false;
					}
				});
				
				// 实际停止音频需要在小程序环境获取背景音频管理器
				// 或使用uni API进行处理，每个平台可能不同
				// 以下为通用处理方法
				try {
					const bgAudioManager = uni.getBackgroundAudioManager();
					if (bgAudioManager) {
						bgAudioManager.stop();
					}
				} catch (e) {
					console.error('停止背景音频失败', e);
				}
			},
			
			// 清理音频资源
			clearAudioResources() {
				// 释放所有音频URL
				this.audioSources.forEach(url => {
					try {
						URL.revokeObjectURL(url);
					} catch (e) {
						console.error('释放音频URL失败', e);
					}
				});
				
				// 清空队列
				this.streamAudioQueue.forEach(url => {
					try {
						URL.revokeObjectURL(url);
					} catch (e) {
						console.error('释放流式音频URL失败', e);
					}
				});
				
				// 清空数组
				this.audioSources = [];
				this.streamAudioQueue = [];
				this.audioChunks = [];
				this.audioBuffer = [];
			},
			
			// 重置所有状态
			resetAllStates() {
				this.isPlaying = false;
				this.isStreamPlaying = false;
				this.isRecording = false;
				this.currentAIMessage = null;
				this.isCurrentResponseComplete = true;
				this.currentTrackIndex = 0;
				this.reconnecting = false;
			},
			
			// 停止AI响应
			stopResponse() {
				// 如果没有正在响应，则不需要操作
				if (!this.isResponding) return;
				
				// 发送停止响应指令
				if (this.isConnected && this.websocket && this.websocket.readyState === WebSocket.OPEN) {
					try {
						this.websocket.send('[STOP]');
						console.log('已发送停止响应指令');
					} catch (e) {
						console.error('发送停止响应指令失败', e);
					}
				}
				
				// 清理相关状态
				if (this.currentAIMessage) {
					this.currentAIMessage.content += ' [已停止]';
					this.currentAIMessage.isComplete = true;
				}
				
				// 停止流式播放
				this.isStreamPlaying = false;
				this.streamAudioQueue = [];
				
				// 标记响应已完成
				this.isCurrentResponseComplete = true;
				this.isResponding = false;
				
				// 通知用户
				uni.showToast({
					title: '已停止AI响应',
					icon: 'none'
				});
			},
			
			// 开始打字指示器动画
			startTypingAnimation() {
				// 使用定时器制作打字动画效果
				setInterval(() => {
					if (this.typingIndicator === '.') {
						this.typingIndicator = '..';
					} else if (this.typingIndicator === '..') {
						this.typingIndicator = '...';
					} else {
						this.typingIndicator = '.';
					}
				}, 500);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.ai-chat-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f6f6f6;
		position: relative;
	}
	
	.navbar-right {
		display: flex;
		align-items: center;
		padding-right: 10rpx;
		
		.icon-wrapper {
			width: 80rpx;
			height: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	
	.chat-content {
		flex: 1;
		box-sizing: border-box;
	}
	
	.loading-container {
		display: flex;
		justify-content: center;
		padding: 30rpx 0;
		
		.simple-loading {
			color: #999;
			font-size: 26rpx;
		}
	}
	
	.message-list {
		padding: 20rpx;
		padding-bottom: 140rpx; /* 增加底部内边距，防止内容被输入框遮挡 */
	}
	
	.time-divider {
		text-align: center;
		font-size: 24rpx;
		color: #999;
		margin: 20rpx 0;
		background: rgba(0,0,0,0.05);
		padding: 6rpx 20rpx;
		border-radius: 20rpx;
		width: fit-content;
		margin: 20rpx auto;
	}
	
	.message-item {
		display: flex;
		margin-bottom: 30rpx;
		
		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			overflow: hidden;
			flex-shrink: 0;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.message-content {
			max-width: 70%;
			
			.message-bubble {
				padding: 20rpx;
				border-radius: 12rpx;
				word-break: break-all;
				line-height: 1.4;
				
				&.voice-message {
					min-width: 160rpx;
					padding: 16rpx 20rpx;
				}
			}
			
			.message-time {
				font-size: 24rpx;
				color: #999;
				margin-top: 8rpx;
			}
			
			.voice-message-content {
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				.voice-icon {
					display: flex;
					align-items: center;
					
					.voice-wave-icon {
						display: flex;
						align-items: center;
						height: 30rpx;
						
						.wave-line {
							width: 4rpx;
							height: 16rpx;
							background-color: currentColor;
							margin: 0 2rpx;
							border-radius: 2rpx;
							
							&:nth-child(1) {
								height: 10rpx;
							}
							&:nth-child(2) {
								height: 16rpx;
							}
							&:nth-child(3) {
								height: 22rpx;
							}
							&:nth-child(4) {
								height: 16rpx;
							}
						}
					}
				}
				
				text {
					margin-left: 10rpx;
					font-size: 24rpx;
				}
			}
		}
	}
	
	.ai-message {
		.message-content {
			margin-left: 20rpx;
			
			.message-bubble {
				background-color: #fff;
				border: 1px solid #eee;
				border-top-left-radius: 4rpx;
				
				.audio-player {
					display: flex;
					align-items: center;
					margin-top: 10rpx;
					padding: 6rpx 12rpx;
					background-color: rgba(43, 203, 212, 0.1);
					border-radius: 8rpx;
					cursor: pointer;
					
					text {
						margin-left: 10rpx;
						font-size: 24rpx;
						color: #2BCBD4;
					}
				}
			}
			
			.message-bubble.system-message {
				background-color: #f8f8f8;
				color: #999;
				font-size: 26rpx;
			}
		}
	}
	
	.user-message {
		flex-direction: row-reverse;
		
		.message-content {
			margin-right: 20rpx;
			align-items: flex-end;
			
			.message-bubble {
				background-color: #2BCBD4;
				color: #fff;
				border-top-right-radius: 4rpx;
			}
			
			.message-time {
				text-align: right;
			}
			
			.voice-message-content {
				flex-direction: row-reverse;
				
				.voice-icon {
					transform: rotateY(180deg); // 翻转语音图标
				}
				
				text {
					margin-left: 0;
					margin-right: 10rpx;
				}
			}
		}
	}
	
	.chat-footer {
		background-color: #fff;
		padding: 20rpx;
		border-top: 1px solid #eee;
		box-sizing: border-box;
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 10;
	}
	
	.input-area {
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		border-radius: 36rpx;
		padding: 10rpx 20rpx;
	}
	
	.voice-keyboard-toggle, .attachment-button {
		flex-shrink: 0;
		padding: 10rpx;
	}
	
	.message-input {
		flex: 1;
		height: 72rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
	}
	
	.voice-button-large {
		flex: 1;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		border-radius: 36rpx;
		margin: 0 10rpx;
		font-size: 28rpx;
		color: #666;
	}
	
	.send-button {
		flex-shrink: 0;
		padding: 10rpx 20rpx;
		background-color: #2BCBD4; 
		color: #fff;
		border-radius: 30rpx;
		font-size: 26rpx;
	}
	
	.stop-button {
		flex-shrink: 0;
		padding: 10rpx;
		background-color: rgba(255, 54, 54, 0.1);
		border-radius: 50%;
		width: 40rpx;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.voice-recording-tip {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 260rpx;
		height: 260rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		z-index: 999;
		
		.voice-wave {
			width: 160rpx;
			height: 120rpx;
			margin-bottom: 20rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			
			.voice-wave-container {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: flex-end;
				
				.voice-wave-bar {
					width: 8rpx;
					background-color: #fff;
					margin: 0 4rpx;
					border-radius: 4rpx;
					transition: height 0.1s ease-in-out;
				}
			}
		}
		
		text {
			color: #fff;
			font-size: 26rpx;
		}
	}
	
	/* 底部安全区域，防止内容被输入框遮挡 */
	.bottom-safe-area {
		height: 140rpx;
		width: 100%;
	}
	
	.responding-indicator {
		position: absolute;
		bottom: 100rpx;
		left: 50%;
		transform: translateX(-50%);
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
		display: flex;
		align-items: center;
		z-index: 100;
		
		text {
			color: #fff;
			font-size: 26rpx;
		}
	}
</style>